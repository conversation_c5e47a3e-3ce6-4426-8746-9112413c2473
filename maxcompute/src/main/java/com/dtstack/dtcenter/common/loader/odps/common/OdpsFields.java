/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.odps.common;

/** @company: www.dtstack.com <AUTHOR> @Date ：Created in 下午5:30 2020/8/4 @Description： */
public class OdpsFields {

    public static final String DEFAULT_ODPS_SERVER = "http://service.odps.aliyun.com/api";

    public static final String KEY_ODPS_SERVER = "endPoint";

    public static final String KEY_ACCESS_ID = "accessId";

    public static final String KEY_ACCESS_KEY = "accessKey";

    public static final String KEY_PROJECT = "project";

    public static final String PACKAGE_AUTHORIZED_PROJECT = "packageAuthorizedProject";

    public static final String KEY_ACCOUNT_TYPE = "accountType";

    public static final String DEFAULT_ACCOUNT_TYPE = "aliyun";
}

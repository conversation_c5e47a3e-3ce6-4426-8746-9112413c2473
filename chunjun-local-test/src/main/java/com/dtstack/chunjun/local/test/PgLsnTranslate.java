package com.dtstack.chunjun.local.test;

public class PgLsnTranslate {
    public static long lsnToLong(String lsn) {
        String[] parts = lsn.split("/");
        if (parts.length != 2) {
            throw new IllegalArgumentException("Invalid LSN format: " + lsn);
        }

        long segment = Long.parseLong(parts[0], 16);
        long offset = Long.parseLong(parts[1], 16);

        return (segment << 32) | offset;
    }

    public static void main(String[] args) {
        String lsn = "0/4C12D718";
        long result = lsnToLong(lsn);
        System.out.println("LSN as long: " + result);
    }
}

create table dehoop.CTR_BASELINES
(
    ID                    varchar(30)    not null comment '主键'
        primary key,
    NAME                  varchar(200)   not null comment '基线名称',
    PROJECT_ID            varchar(30)    not null comment '所属项目ID',
    PRIORITY              varchar(200)   not null comment '优先级',
    DEAD_LINE             varchar(200)   not null comment '最晚完成时间',
    ALARM_TYPE            varchar(200)   not null comment '告警方式',
    ALARM_TIME            varchar(200)   null comment '告警时间',
    ALARM_INTERVAL        int default 10 not null comment '告警间隔',
    MAX_ALARM_TIMES       int default 1  null comment '最大告警次数',
    ALARM_CONTENT         varchar(500)   not null comment '告警内容',
    CREATED_BY            varchar(30)    null comment '创建者',
    CREATION_DATE         datetime       null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)    null comment '更新者',
    LAST_UPDATE_DATE      datetime       null comment '更新时间',
    OBJECT_VERSION_NUMBER int            null comment '数据版本',
    TENANT_ID             varchar(30)    null comment '租户id'
)
    comment '基线表';

create table dehoop.CTR_BASELINE_INSTANCES
(
    ID                    varchar(30)                      not null comment '主键'
        primary key,
    BASELINE_ID           varchar(30)                      not null comment '基线ID',
    WORK_ID               varchar(30)                      not null comment '作业ID',
    WORK_VERSION_ID       varchar(30)                      null comment '作业版本ID',
    ALARM_STATE           varchar(200) default 'UNTREATED' null comment '告警状态 UNTREATED 为未处理 TREATED 为已处理',
    ALARM_DATE            datetime                         null comment '告警时间',
    ALARM_TIMES           int                              null comment '告警次数',
    WORK_TYPE             varchar(200) default 'WORK'      null comment '报警作业类型',
    ALARM_TYPE            varchar(200)                     null comment '报警类型',
    ALARM_CONTENT         varchar(200)                     null comment '报警内容',
    CREATED_BY            varchar(30)                      null comment '创建者',
    CREATION_DATE         datetime                         null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                      null comment '更新者',
    LAST_UPDATE_DATE      datetime                         null comment '更新时间',
    OBJECT_VERSION_NUMBER int                              null comment '数据版本',
    TENANT_ID             varchar(30)                      null comment '租户id'
)
    comment '基线实例表';

create table dehoop.CTR_BASELINE_INSTANCE_USER
(
    ID                    varchar(32) not null comment '主键'
        primary key,
    INSTANCE_ID           varchar(32) not null comment '告警实例id',
    USER_ID               varchar(32) not null comment '告警用户id',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment '基线报警实例与用户绑定对象';

create table dehoop.CTR_BASELINE_SCH_JOBS
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    JOB_ID                mediumtext  not null comment 'powerjob 工作id',
    BASELINE_ID           varchar(30) not null comment '基线ID',
    CREATED_BY            varchar(30) null comment '创建人',
    LAST_UPDATED_BY       varchar(30) null comment '上次更新人',
    LAST_UPDATE_DATE      datetime    null comment '上次更新时间',
    CREATION_DATE         datetime    null comment '创建时间',
    OBJECT_VERSION_NUMBER int         null comment '版本',
    TENANT_ID             varchar(30) null comment '租户ID'
)
    comment '处于定时调度中的基线';

create table dehoop.CTR_BASELINE_USERS
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    BASELINE_ID           varchar(30) not null comment '基线ID',
    USER_ID               varchar(30) not null comment '用户ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment '基线与用户关联表';

create table dehoop.CTR_BASELINE_WORKS
(
    ID                    varchar(30)                    not null comment '主键'
        primary key,
    BASELINE_ID           varchar(30)                    not null comment '基线ID',
    WORK_ID               varchar(30)                    not null comment '作业ID',
    WORK_VERSION_ID       varchar(30)                    null comment '作业的版本ID',
    WORK_STATE            varchar(200) default 'UNAPPRO' null comment '是否审批 UNAPPRO 为未审批 APPRO 为审批后的作业,未审批的作业不存在版本号，且只有状态处于审批后的作业基线才可以生效',
    TYPE                  varchar(100) default 'WORK'    null comment '作业类型',
    CREATED_BY            varchar(30)                    null comment '创建者',
    CREATION_DATE         datetime                       null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                    null comment '更新者',
    LAST_UPDATE_DATE      datetime                       null comment '更新时间',
    OBJECT_VERSION_NUMBER int                            null comment '数据版本',
    TENANT_ID             varchar(30)                    null comment '租户id'
)
    comment '基线与作业关联表';

create table dehoop.DAQ_DATA_LAYERS
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    CREATED_BY            varchar(30)  null comment '创建人',
    LAST_UPDATED_BY       varchar(30)  null comment '上次更新人',
    LAST_UPDATE_DATE      datetime     null comment '上次更新时间',
    CREATION_DATE         datetime     null comment '创建时间',
    OBJECT_VERSION_NUMBER int          null comment '版本',
    TENANT_ID             varchar(30)  null comment '租户ID',
    ENG_SIMPLE_NAME       varchar(200) not null comment '英文名缩写',
    ENG_NAME              varchar(200) not null comment '英文名',
    NAME                  varchar(200) not null comment '中文名称',
    DIRECTOR              varchar(32)  not null comment '负责人',
    DESCR                 varchar(500) null comment '描述',
    PRO_ID                varchar(32)  null comment '项目id'
)
    comment '数据分层表';

create table dehoop.DAQ_TABLE_INSPECTORS
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    CREATED_BY            varchar(30)  null comment '创建人',
    LAST_UPDATED_BY       varchar(30)  null comment '上次更新人',
    LAST_UPDATE_DATE      datetime     null comment '上次更新时间',
    CREATION_DATE         datetime     null comment '创建时间',
    OBJECT_VERSION_NUMBER int          null comment '版本',
    TENANT_ID             varchar(30)  null comment '租户ID',
    ENG_SIMPLE_NAME       varchar(200) not null comment '英文缩写',
    NAME                  varchar(200) not null comment '名称',
    EXPRESSION            varchar(200) null,
    TABLE_MAX_LENGTH      int          null comment '表长度',
    KEY_WORDS             varchar(500) null,
    CASE_SENSITIVE_STATUS varchar(30)  not null comment '大小写敏感',
    OPEN_STATUS           varchar(30)  not null comment '开关状态',
    REAL_CHECK_STATUS     varchar(30)  not null comment '实时检查',
    DESCR                 varchar(500) null comment '描述',
    DATA_LAYER_ID         varchar(32)  not null comment '数据分层id'
)
    comment '数据检查器';

create table dehoop.DAQ_TABLE_LAYERS
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    CREATED_BY            varchar(30) null comment '创建人',
    LAST_UPDATED_BY       varchar(30) null comment '上次更新人',
    LAST_UPDATE_DATE      datetime    null comment '上次更新时间',
    CREATION_DATE         datetime    null comment '创建时间',
    OBJECT_VERSION_NUMBER int         null comment '版本',
    TENANT_ID             varchar(30) null comment '租户ID',
    TABLE_ID              varchar(32) not null comment '资产库表id',
    DATA_LAYER_ID         varchar(32) not null comment '数据分层id',
    VALID_STATUS          varchar(20) not null comment '合法状态',
    CHECK_DATE            datetime    null comment '检测时间',
    WORK_ID               varchar(32) null comment '作业id'
)
    comment '数据检查器';

create table dehoop.DAQ_TABLE_LAYERS_DATA_DIMENSION
(
    ID                    varchar(32) null,
    ENTITY_ID             varchar(32) null comment '实体ID',
    TABLE_LAYER_ID        varchar(32) null comment '分层ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    VERSION_ID            varchar(32) null comment '当前版本号'
)
    comment '分层实体关联表';

create table dehoop.DAQ_TABLE_LAYERS_DATA_TAG
(
    ID                    varchar(32) null,
    TAG_ID                varchar(32) null comment '标签ID',
    TABLE_LAYER_ID        varchar(32) null comment '分层ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    VERSION_ID            varchar(32) null comment '当前版本号'
)
    comment '分层实体关联表';

create table dehoop.DAQ_TABLE_LAYERS_MODEL
(
    ID                    varchar(32) null,
    MODEL_TABLE_ID        varchar(32) null comment '实体ID',
    DATA_LAYER_ID         varchar(32) null comment '分层ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    VERSION_ID            varchar(32) null comment '当前版本号',
    TYPE                  varchar(32) null comment 'ENTITY/DATA_DIMENSION
'
)
    comment '分层实体关联表';

create table dehoop.DAS_APIS
(
    ID                    varchar(30)                     not null comment '主键'
        primary key,
    API_NAME              varchar(200)                    not null comment 'api名称',
    API_CODE              varchar(200)                    not null comment 'api编码',
    PATH                  varchar(200)                    not null comment '请求路径',
    REQUEST_METHOD        varchar(200)                    not null comment '请求方式 GET/POST',
    AUTH_TYPE             varchar(200)                    not null comment '认证方式 SIMPLE_AUTH 表示简单身份认证
',
    DESCR                 varchar(500)                    null comment '描述',
    REQUEST_BODY          text                            null comment '请求参数',
    RESPONSE_BODY         text                            null comment '返回参数',
    DATASOURCE_TYPE       varchar(30)                     null comment '数据源类型',
    SOURCE_ID             varchar(30)                     null comment '来源ID 如果是Hive则为存储空间的ID 如果是MySQL的话则为数据源的ID',
    TABLE_NAME            varchar(200)                    null comment ' 数据表名称 ',
    STATE                 varchar(30) default 'UN_SUBMIT' not null comment 'api发布状态
UN_SUBMIT 未提交
SUBMITTED 已提交
PUBLISHED 已发布
OFFLINE 已下线',
    PAGE_QUERY            varchar(30) default 'DISABLE'   null comment '是否分页查询,ENABLE表示是,DISABLE表示否',
    ACCESS_KEY            varchar(100)                    not null comment '访问的key',
    API_VERSION           varchar(100)                    null comment 'api当前的版本号',
    PROJECT_ID            varchar(30)                     null comment '项目ID ',
    CREATED_BY            varchar(30)                     null comment '创建者',
    CREATION_DATE         datetime                        null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                     null comment '更新者',
    LAST_UPDATE_DATE      datetime                        null comment '更新时间',
    OBJECT_VERSION_NUMBER int                             null comment '数据版本',
    TENANT_ID             varchar(30)                     null comment '租户id'
)
    comment '数据服务api';

create table dehoop.DAS_API_AUTHORITY
(
    ID                    varchar(30)               null comment '主键',
    GROUP_ID              varchar(30)               null comment '权限组ID',
    SUBJECT_ID            varchar(200)              null comment '主体',
    SUBJECT_TYPE          varchar(255)              null comment '主体类型：人员(USER)、角色(ROLE)、全体人员（ALL）',
    SEQUENCE_NUMBER       int                       null comment '序号',
    SCOPE                 varchar(30) default 'ALL' null comment '范围：ONESELF / ALL',
    RETRIEVE              tinyint     default 1     null comment '查询权限',
    EDIT                  tinyint     default 1     null comment '编辑权限',
    DELETION              tinyint     default 1     null comment '删除权限',
    PUBLISH               tinyint     default 1     null comment '发布权限',
    CREATED_BY            varchar(30)               null comment '创建者',
    CREATION_DATE         datetime                  null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)               null comment '更新者',
    LAST_UPDATE_DATE      datetime                  null comment '更新时间',
    OBJECT_VERSION_NUMBER int                       null comment '数据版本',
    TENANT_ID             varchar(30)               null comment '租户id',
    IS_DEFAULT            tinyint     default 0     null comment '是否是默認'
)
    comment 'api权限表';

create table dehoop.DAS_API_AUTHORITY_GROUP
(
    ID                    varchar(30)  null comment '主键',
    NAME                  varchar(500) null comment '权限组名',
    DESCR                 varchar(200) null comment '备注',
    SEQUENCE_NUMBER       int          null comment '序号',
    PROJECT_ID            varchar(30)  null comment '项目ID',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment 'API权限组';

create table dehoop.DAS_API_AUTHORITY_GROUP_CLAUSE
(
    ID                     varchar(30)  null comment '主键',
    GROUP_ID               varchar(30)  null comment '权限组ID',
    GROUP_SEQUENCE_NUMBER  int          null comment '权限组排序',
    CLAUSE_SEQUENCE_NUMBER int          null comment '范围中排序',
    FIELD                  varchar(200) null comment '字段',
    CONDITION_VAL          varchar(200) null comment '条件',
    VALUE_VAL              varchar(300) null comment '值',
    CREATED_BY             varchar(30)  null comment '创建者',
    CREATION_DATE          datetime     null comment '创建时间',
    LAST_UPDATED_BY        varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE       datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER  int          null comment '数据版本',
    TENANT_ID              varchar(30)  null comment '租户id'
)
    comment 'API权限范围表';

create table dehoop.DAS_API_AUTHORITY_ROLE
(
    ID                    varchar(30) null,
    ROLE_ID               varchar(30) null comment '角色ID',
    ROLE_NAME             varchar(30) null comment '角色名称',
    GROUP_ID              varchar(30) null comment '权限组ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    AUTHORITY_ID          varchar(30) null
);

create table dehoop.DAS_API_AUTHORITY_USER
(
    ID                    varchar(30) null,
    USER_ID               varchar(30) null,
    CHECKED               tinyint     null,
    GROUP_ID              varchar(30) null,
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    AUTHORITY_ID          varchar(30) null
)
    comment 'api权限用户';

create table dehoop.DAS_API_LOGS
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    API_ID                varchar(30)  not null comment 'api对应ID',
    API_NAME              varchar(200) null comment 'api名称',
    API_CODE              varchar(200) null comment 'api编码',
    PATH                  varchar(200) null comment 'api路径',
    IP_ADDR               varchar(200) not null comment 'ip地址',
    RESPONSE_TIME         int          not null comment '请求时间',
    REQUEST_METHOD        varchar(200) not null comment '请求方式 GET/POST',
    REQUEST_SIZE          int          null comment '请求报文大小',
    RESPONSE_SIZE         int          null comment '响应报文大小',
    RESPONSE_CODE         varchar(50)  null comment '响应编码',
    CALL_STATE            varchar(50)  not null comment '调用状态
SUCCESS成功
ERROR 失败',
    REQUEST_HEADER        text         null comment '请求头',
    REQUEST_BODY          text         null comment '请求体',
    RESPONSE_BODY         text         null comment '返回体',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id',
    API_VERSION           varchar(30)  null comment 'api版本号',
    PROJECT_ID            varchar(30)  null comment '项目id'
)
    comment 'api调用日志表';

create table dehoop.DAS_API_SECURITY
(
    ID                    varchar(30) not null
        primary key,
    API_ID                varchar(30) not null comment 'api对应ID',
    TYPE                  varchar(30) not null comment '黑白名单类型BLACK/WHITE',
    RULE                  text        not null comment '规则',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment 'api安全管理';

create table dehoop.DOGS_T
(
    id                    int          not null,
    name                  varchar(100) not null,
    amount_double         double       not null,
    amount_big_decimal    decimal      not null,
    is_alive              tinyint(1)   not null,
    birth_date            date         not null,
    birth_local_date      date         not null,
    birth_local_date_time datetime     not null
);

create table dehoop.DQ_CUSTOM_RULES
(
    ID                    varchar(32)  not null comment '主键'
        primary key,
    NAME                  varchar(200) not null comment '名称',
    TYPE                  varchar(50)  not null comment '类型
REGEX 正则表达式
WILDCARD 通配符表达式',
    EXPRESSION            varchar(500) not null comment '表达式',
    DESCR                 varchar(500) null comment '描述',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment '规范检查自定义规则';

create table dehoop.DQ_QUALITY_LOG
(
    ID                    varchar(32)                   null comment '主键',
    RESULT                varchar(32)    default '0.00' null comment '结果',
    ERROR_NUM             int            default 0      null comment '异常数量',
    SCORE                 decimal(10, 2) default 0.00   null comment '分数',
    MODE                  varchar(32)                   null comment '质检方式',
    ELAPSED_TIME          mediumtext                    null comment '耗时',
    EXECUTOR              varchar(32)                   null comment '执行人',
    CREATED_BY            varchar(30)                   null comment '创建者',
    CREATION_DATE         datetime                      null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                   null comment '更新者',
    LAST_UPDATE_DATE      datetime                      null comment '更新时间',
    OBJECT_VERSION_NUMBER int                           null comment '数据版本',
    TENANT_ID             varchar(30)                   null comment '租户id',
    SCHEME_ID             varchar(32)                   null comment '方案ID',
    LOG_TEXT              longtext                      null comment '日志文本',
    SUM_NUM               int            default 0      null comment '总数',
    END_TIME              datetime                      null comment '结束时间'
)
    comment '质检日志';

create table dehoop.DQ_QUALITY_LOG_RULE
(
    ID                    varchar(32)  null comment '主键',
    LEVEL                 varchar(32)  null comment '级别',
    WEIGHT                int          null comment '权重',
    TABLE_ASSET_ID        varchar(32)  null comment '资源表',
    ERROR_NUM             int          null comment '异常数',
    SUM_NUM               int          null comment '总数',
    EXECUTOR              varchar(32)  null comment '执行人',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id',
    LOG_ID                varchar(32)  null comment '日志表ID',
    RULE_ID               varchar(32)  null comment '规则表ID',
    TABLE_NAME            varchar(200) null comment '表名',
    RULE_TYPE             varchar(32)  null comment '规则类型'
)
    comment '质检日志';

create table dehoop.DQ_QUALITY_MODEL
(
    ID                    varchar(32)  not null comment '主键'
        primary key,
    NAME                  varchar(200) not null comment '模型名称',
    EN_NAME               varchar(32)  not null comment '英文名字',
    DESCR                 varchar(500) null comment '描述',
    DATA_FIELD_ID         varchar(32)  not null comment '数据域id',
    BUSINESS_PROCESS_ID   varchar(32)  not null comment '业务流程id',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment '质量模型';

create table dehoop.DQ_QUALITY_MODEL_RULES
(
    ID                      varchar(32)  not null comment '主键'
        primary key,
    NAME                    varchar(200) not null comment '名称',
    DESCR                   varchar(500) null comment '描述',
    TYPE                    varchar(32)  not null comment '类型',
    QUALITY_MODEL_ID        varchar(32)  not null comment '质量模型id',
    LEVEL                   varchar(32)  not null comment '级别',
    WEIGHT                  int          not null comment '权重',
    TABLE_ASSET_ID          varchar(200) not null comment '主题表',
    STATE                   varchar(32)  null comment '状态 ENABLE / DISABLE',
    CHECK_FIELD             text         null comment '检查字段',
    UNION_NULL              varchar(32)  null comment '是否联合为空 ENABLE /DISABLE',
    VALUE_RANGE             varchar(200) null comment '值域范围',
    UNION_REPEAT            varchar(32)  null comment '组合重复',
    REGULATION_CHECK_CONFIG text         null comment '规范检查的配置',
    CREATED_BY              varchar(30)  null comment '创建者',
    CREATION_DATE           datetime     null comment '创建时间',
    LAST_UPDATED_BY         varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE        datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER   int          null comment '数据版本',
    TENANT_ID               varchar(30)  null comment '租户id'
)
    comment '质量模型-规则库';

create table dehoop.DQ_QUALITY_SCHEME
(
    ID                    varchar(32)                   not null comment '主键'
        primary key,
    NAME                  varchar(200)                  null comment '质检方案名称',
    QUALITY_MODEL_ID      varchar(32)                   not null comment '质检模型id',
    STATE                 varchar(32) default 'DISABLE' not null comment '开启或关闭
ENABLE
DISABLE',
    EFFECT_START_DATE     date                          null comment '生效开始日期',
    EFFECT_END_DATE       date                          null comment '生效结束日期',
    PERIOD_TIME           time                          null comment '日执行时间',
    DAYS                  varchar(500)                  null comment '执行日',
    PERIOD_TYPE           varchar(200)                  null comment '调度方式 日 周 月',
    DESCR                 varchar(500)                  null comment '描述',
    CREATED_BY            varchar(30)                   null comment '创建者',
    CREATION_DATE         datetime                      null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                   null comment '更新者',
    LAST_UPDATE_DATE      datetime                      null comment '更新时间',
    OBJECT_VERSION_NUMBER int                           null comment '数据版本',
    TENANT_ID             varchar(30)                   null comment '租户id'
)
    comment '质检方案表';

create table dehoop.DQ_SCHEME_EXCEPT_TABLE
(
    ID                    varchar(32)  not null comment '主键'
        primary key,
    RULE_ID               varchar(32)  null comment '规则id',
    RULE_NAME             varchar(200) not null comment '规则名称',
    RULE_TYPE             varchar(200) null comment '规则类型',
    TABLE_NAME            varchar(500) not null comment '表名',
    ERROR_NUM             bigint       null comment '错误数',
    DATABASE_NAME         varchar(500) not null comment '数据库名称',
    SCHEME_ID             varchar(32)  not null comment '质检方案id',
    LOG_ID                varchar(32)  not null comment '质检日志id',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment '质检方案例外表';

create table dehoop.DQ_SCHEME_RESULT_TABLE
(
    ID                    varchar(32)  not null comment '主键',
    TABLE_NAME            varchar(500) not null comment '表名',
    RESULT_NUM            bigint       null comment '错误数',
    DATABASE_NAME         varchar(500) not null comment '数据库名称',
    SCHEME_ID             varchar(32)  not null comment '质检方案id',
    LOG_ID                varchar(32)  not null comment '质检日志id',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment '质检方案例外表';

create table dehoop.DQ_SCHEME_RULES
(
    ID                    varchar(32) not null comment '主键'
        primary key,
    RULE_ID               varchar(32) not null comment '规则id',
    SCHEME_ID             varchar(32) not null comment '质检方案id',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    constraint DQ_SCHEME_RULES_ID_uindex
        unique (ID)
)
    comment '质检方案与规则的关联表';

create table dehoop.FND_AUTHORITY
(
    ID                    varchar(50)       null,
    NAME                  varchar(100)      null comment '名称',
    TYPE                  varchar(100)      null comment '类型：MODULE/MENU/FUNCTION',
    CHECKED               tinyint default 0 null comment '是否选中',
    INDETERMINATE         tinyint           null comment '半选中',
    PARENT_ID             varchar(50)       null comment '父ID',
    SEQUENCE_NUM          int     default 0 null comment '序号',
    CODE                  varchar(200)      null comment '编码',
    CREATION_DATE         datetime          null,
    LAST_UPDATED_BY       varchar(30)       null,
    LAST_UPDATE_DATE      datetime          null,
    OBJECT_VERSION_NUMBER int               null,
    TENANT_ID             varchar(30)       null,
    CREATED_BY            varchar(30)       null
)
    comment '权限表';

create table dehoop.FND_ROLES
(
    ID                    varchar(30)                   not null comment '主键'
        primary key,
    NAME                  varchar(200)                  not null comment '角色名称',
    TYPE                  varchar(200) default 'MANUAL' null comment '角色类型',
    CREATED_BY            varchar(30)                   null,
    CREATION_DATE         datetime                      null,
    LAST_UPDATED_BY       varchar(30)                   null,
    LAST_UPDATE_DATE      datetime                      null,
    OBJECT_VERSION_NUMBER int                           null,
    TENANT_ID             varchar(30)                   null
)
    comment '角色表';

create table dehoop.FND_ROLE_AUTHORITY
(
    ID                    varchar(50)       null comment 'ID',
    role_id               varchar(50)       null comment '角色ID',
    authority_id          varchar(50)       null comment '权限ID',
    checked               tinyint default 0 null comment '选中状态',
    INDETERMINATE         tinyint default 0 null comment '半选中状态',
    CODE                  varchar(100)      null comment '编码',
    CREATION_DATE         datetime          null,
    LAST_UPDATED_BY       varchar(30)       null,
    LAST_UPDATE_DATE      datetime          null,
    OBJECT_VERSION_NUMBER int               null,
    TENANT_ID             varchar(30)       null,
    CREATED_BY            varchar(30)       null
)
    comment '角色权限映射表';

create table dehoop.FND_ROLE_USERS
(
    ID                    varchar(30)                  not null comment '主键'
        primary key,
    USER_ID               varchar(30)                  not null comment '用户id',
    ROLE_ID               varchar(30)                  null comment '角色id',
    CREATED_BY            varchar(30)                  null,
    CREATION_DATE         datetime                     null,
    LAST_UPDATED_BY       varchar(30)                  null,
    LAST_UPDATE_DATE      datetime                     null,
    OBJECT_VERSION_NUMBER int                          null,
    TENANT_ID             varchar(30)                  null,
    SYS_ROLE              varchar(30) default 'MEMBER' null comment '系统角色'
)
    comment '角色关联表';

create table dehoop.FND_USERS
(
    ID                    varchar(30)                                             not null comment '主键'
        primary key,
    USERNAME              varchar(200)                                            not null comment '用户姓名',
    PHONE                 varchar(20)                                             not null comment '用户手机号',
    PASSWD                varchar(100) default '201f00b5ca5d65a1c118e5e32431514c' not null comment '用户密码',
    EMAIL                 varchar(30)                                             null comment '用户邮箱',
    JOB_NO                varchar(30)                                             null comment '工号',
    BIRTHDAY              varchar(10)                                             null comment '用户生日',
    SEX                   varchar(10)  default 'SECRET'                           null comment '用户性别(男 MALE/ 女 FEMALE)',
    POSITION              varchar(200)                                            null comment '用户职位',
    AVATAR                varchar(30)                                             null comment '用户头像附件id',
    INTRODUCE             varchar(500)                                            null comment '用户简介',
    STATE                 varchar(10)  default 'ENABLE'                           not null comment '用户是否被禁用(ENABLE 启用/ DISABLE 禁用)',
    CREATED_BY            varchar(30)                                             null,
    CREATION_DATE         datetime                                                null,
    LAST_UPDATED_BY       varchar(30)                                             null,
    LAST_UPDATE_DATE      datetime                                                null,
    OBJECT_VERSION_NUMBER int                                                     null,
    TENANT_ID             varchar(30)                                             null,
    SYS_ROLE              varchar(30)  default 'MEMBER'                           not null comment '系统角色 ADMIN 管理者/ MEMBER 普通成员',
    EFFECT_START_DATE     date         default '2020-12-22'                       not null comment '生效开始日期',
    EFFECT_END_DATE       date         default '9999-12-31'                       not null comment '生效结束日期',
    constraint FND_USERS_EMAIL_uindex
        unique (EMAIL),
    constraint FND_USERS_PHONE_uindex
        unique (PHONE)
)
    comment '用户表';

create table dehoop.JOB_BUILD_IN_FUNCTIONS
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    NAME                  varchar(200) not null comment '函数名称',
    DESCR                 varchar(500) null comment '函数描述',
    TYPE                  varchar(30)  not null comment '函数类型',
    COMMAND               varchar(500) null comment '命令格式',
    EXAMPLE               varchar(500) null comment '函数示例',
    PARAMS_DESCR          varchar(200) null comment '参数描述',
    USAGES                varchar(200) null comment '函数的使用',
    RETURN_TYPE           varchar(200) null comment '返回类型',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment 'hive内置函数';

create table dehoop.JOB_DDLV_CONFIGS
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    WORK_ID               varchar(30)  not null comment '作业ID',
    OPT_TYPE              varchar(30)  not null comment '操作类型CREATE=创建表，ALTER=修改表',
    TABLE_NAME            varchar(200) not null comment '表名',
    COMMENTS              varchar(500) null comment '描述',
    STORE_TYPE            varchar(100) not null comment '存储格式  TEXTFILE文本类型  SEQUENCEFILE 压缩文件',
    DELIMITER_BY          varchar(30)  not null comment '分隔符',
    TABLE_TYPE            varchar(30)  not null comment '表的类型，INTERNAL = 内部表，EXTERNAL = 外部表',
    STORE_LOCATION        varchar(100) null comment '保存位置',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本
',
    TENANT_ID             varchar(30)  null comment '租户ID'
)
    comment 'DDL可视化基本配置表';

create table dehoop.JOB_DDLV_FIELDS
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    FIELD_NAME            varchar(100) not null comment '字段名称',
    FIELD_TYPE            varchar(100) not null comment '字段类型',
    MODULE_TYPE           varchar(100) not null comment '显示字段类型',
    LOGIC_NAME            varchar(100) not null comment '逻辑名称',
    LENGTH_LIMIT          int          null comment '字段长度',
    PRECISIONS            int          null comment '精度',
    SCALES                int          null comment '小数后几位',
    COMMENTS              varchar(500) null comment '字段说明',
    IS_PRIMARY_KEY        tinyint      not null comment '是否是主键',
    IS_NOT_NULL           tinyint      null comment '是否允许空',
    IS_PARTITION_KEY      tinyint      not null comment '是否是分区键',
    WORK_ID               varchar(30)  not null comment '作业ID',
    SEQUENCE_NUMBER       int          not null comment '顺序号',
    FIELD_CREATED         tinyint      null comment '字段是否创建',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment 'ddl可视化字段信息表';

create table dehoop.JOB_ETL_BUILD_IN_FUNCTION
(
    ID                    varchar(32)  not null comment '主键id'
        primary key,
    NAME                  varchar(200) not null comment '函数名称',
    TYPE                  varchar(200) null comment '函数类型',
    FORMAT                varchar(500) null comment '函数的格式',
    EXAMPLE               varchar(500) null comment '函数的示例',
    USAGES                varchar(200) null comment '函数用法',
    FORMAT_DESCR          varchar(500) null comment '格式描述',
    EXAMPLE_DESCR         varchar(500) null comment '示例描述',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment 'ETL表达式自带函数';

create table dehoop.JOB_ETL_WORK_NODE
(
    ID                    varchar(50)  not null comment '主键'
        primary key,
    NAME                  varchar(200) null comment '节点名称',
    TYPE                  varchar(200) null comment '作业类型',
    NAME_EN               varchar(30)  null comment '节点英文名',
    DESCR                 varchar(500) null comment '描述',
    WORK_ID               varchar(30)  null comment '作业ID',
    DATA_SOURCE_ID        varchar(32)  null comment '数据源ID',
    TABLE_NAME            varchar(200) null comment '表名',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null,
    COMPONENT_TYPE        varchar(32)  null
)
    comment '实时作业表 ';

create table dehoop.JOB_ETL_WORK_NODE_AGGREGATION_FILED
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    NODE_ID               varchar(32)  null comment '节点ID',
    FIELD_NAME            varchar(200) null comment '字段名称',
    SOURCE_NODE_ID        varchar(32)  null comment '数据源节点',
    SOURCE_FIELD_NAME     varchar(200) null comment '原字段',
    FIELD_TYPE            varchar(30)  null comment '字段类型',
    DESCR                 varchar(500) null comment '描述',
    SEQUENCE_NUMBER       int          null comment '序号',
    IS_GROUP_FIELD        tinyint      null comment '是否分组字段',
    AGGREGATE_FUNCTION    varchar(200) null comment '聚合函数',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null
)
    comment 'ETL聚合节点字段 ';

create table dehoop.JOB_ETL_WORK_NODE_ASSOCIATION_CONDITION
(
    ID                    varchar(32)  null,
    NODE_ID               varchar(100) null comment '所属节点ID',
    CONDITIONS            varchar(100) null comment '关联条件',
    TYPE                  varchar(50)  null comment '关联类型 relationField/relationCondition',
    FILED_ID              varchar(32)  null comment '所属字段ID',
    SEQUENCE_NUMBER       int          null comment '序号',
    ASSOCIATE_VALUE       varchar(200) null comment '关联值',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null,
    ASSOCIATE_FIELD       varchar(200) null comment '关联字段',
    ASSOCIATE_TABLE       varchar(100) null comment '所属表'
)
    comment 'ETL关联组件关联字段关联条件';

create table dehoop.JOB_ETL_WORK_NODE_ASSOCIATION_FIELD
(
    ID                    varchar(100) not null comment '主键'
        primary key,
    NODE_ID               varchar(100) null comment '节点ID',
    ASSOCIATION_TYPE      varchar(200) null comment '关联类型',
    ASSOCIATION_MODULE    varchar(200) null comment '关联组件 ',
    SEQUENCE_NUMBER       int          null comment '序号',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null
)
    comment 'ETL关联组件字段 ';

create table dehoop.JOB_ETL_WORK_NODE_CONFIG
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    WORK_ID               varchar(200) null comment '作业ID',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null,
    WEB_CONFIG            longtext     null
)
    comment '实时作业表 ';

create table dehoop.JOB_ETL_WORK_NODE_FILED
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    NODE_ID               varchar(32)  null comment '节点ID',
    FIELD_NAME            varchar(200) null comment '字段名称',
    SOURCE_NODE_ID        varchar(32)  null comment '数据源节点',
    SOURCE_FIELD_NAME     varchar(200) null comment '原字段',
    FIELD_TYPE            varchar(30)  null comment '字段类型',
    LENGTH                int          null comment '字段长度',
    SCALE                 int          null comment '刻度',
    DESCR                 varchar(500) null comment '描述',
    SEQUENCE_NUMBER       int          null comment '序号',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null,
    EXPRESSION            text         null
)
    comment '实时作业表 ';

create table dehoop.JOB_ETL_WORK_NODE_FILTER_FILED
(
    ID                    varchar(200) not null comment '主键'
        primary key,
    NODE_ID               varchar(100) null comment '节点ID',
    FIELD                 varchar(200) null comment '字段名称',
    VALUE                 varchar(200) null comment '取值',
    CONDITIONS            varchar(100) null comment '条件',
    CONJUNCTION           varchar(100) null comment '连接关系 OR/AND',
    SEQUENCE_NUMBER       int          null comment '序号',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null,
    PARENT_ID             varchar(32)  null comment '父节点'
)
    comment '过滤组件字段 ';

create table dehoop.JOB_ETL_WORK_NODE_INSTANCE
(
    ID                    varchar(50)  not null comment '主键'
        primary key,
    STATE                 varchar(100) null comment '节点状态',
    EXECUTE_ID            varchar(50)  null comment '执行ID',
    NODE_ID               varchar(50)  null comment '节点ID',
    WORK_ID               varchar(50)  null comment '作业ID',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null
)
    comment 'ETL节点状态 ';

create table dehoop.JOB_ETL_WORK_NODE_OUT
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    NODE_ID               varchar(200) null comment '节点名称',
    WORK_ID               varchar(32)  null comment '作业ID',
    CREATE_TABLE          tinyint      null comment '是否建表',
    UPDATE_MODEL          varchar(32)  null comment '更新方式 INSERT/OVERRIDE',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null
)
    comment '实时作业表 ';

create table dehoop.JOB_ETL_WORK_NODE_RELATION
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    WORK_ID               varchar(30) null comment '作业ID',
    CREATED_BY            varchar(30) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(30) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(30) null,
    TARGET_ID             varchar(32) null,
    SOURCE_ID             varchar(32) null
)
    comment '实时作业表 ';

create table dehoop.JOB_ETL_WORK_NODE_ROUTER_FILED
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    FILTER_CONDITION      varchar(500) null comment '过滤条件',
    TARGET_MODULE         varchar(32)  null comment '目标组件',
    DEFAULT_BRANCH        tinyint      null comment '是否默认分支',
    SEQUENCE_NUMBER       int          null comment '序号',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null,
    NODE_ID               varchar(32)  null comment '节点ID
'
)
    comment '路由组件字段 ';

create table dehoop.JOB_EXECUTE_QUERY
(
    ID                    varchar(32) not null comment '主键'
        primary key,
    EXECUTE_ID            varchar(32) null comment '执行ID',
    QUERY_RESULT          longtext    null comment '查询结果',
    CREATED_BY            varchar(32) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(32) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(32) null,
    STATE                 varchar(50) null comment 'INIT 初始化 /OVER 插入结束'
);

create table dehoop.JOB_FLOW_CONFIGS
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    FLOW_ID               varchar(30) null comment '工作流id',
    WEB_CONFIG            text        null comment '界面配置',
    FLOW_CONFIG           text        null comment '流配置',
    CREATED_BY            varchar(30) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(30) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(30) null,
    WORK_FLOW             text        null,
    constraint JOB_FLOW_CONFIGS_FLOW_ID_uindex
        unique (FLOW_ID)
)
    comment '工作流配置表';

create table dehoop.JOB_FLOW_CONTROLS
(
    ID                       varchar(30)                  not null comment '主键'
        primary key,
    STATE                    varchar(10) default 'ENABLE' not null comment '调度状态',
    EFFECT_START_DATE        date                         null comment '生效开始时间',
    EFFECT_END_DATE          date                         null comment '生效结束时间',
    PERIOD_TYPE              varchar(10)                  null comment '调度周期类型',
    PERIOD_NUM               int                          null comment '周期间隔数',
    START_TIME               time                         null comment '周期开始时间',
    END_TIME                 time                         null comment '周期结束时间',
    PERIOD_TIME              time                         null comment '调度时间',
    TYPE                     varchar(50)                  null comment '调度类型',
    CREATED_BY               varchar(30)                  null,
    CREATION_DATE            datetime                     null,
    LAST_UPDATED_BY          varchar(30)                  null,
    LAST_UPDATE_DATE         datetime                     null,
    OBJECT_VERSION_NUMBER    int                          null,
    TENANT_ID                varchar(30)                  null,
    FLOW_ID                  varchar(30)                  null comment '工作流id',
    DAYS                     varchar(400)                 null comment '每月的几号 [1,2,3,4]',
    MONTH_END_INSTANCE_STATE varchar(10)                  null
)
    comment '工作流调度配置表';

create table dehoop.JOB_FLOW_DEPENDENTS
(
    ID                    varchar(30)  not null
        primary key,
    FLOW_ID               varchar(30)  not null comment '工作流id',
    PROJECT_ID            varchar(30)  null comment '项目id',
    DEPENDENT_FLOW_ID     varchar(30)  null comment '上游工作流id',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null,
    TYPE                  varchar(100) null comment '工作流依赖类型 (''SON''/''PARENT'')',
    FLOW_NAME             varchar(200) null comment '工作流名称'
)
    comment '工作流调度依赖表';

create table dehoop.JOB_FLOW_INSTANCES
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    WORK_ID               varchar(30) null comment '作业id',
    INSTANCE_ID           varchar(30) null comment '实例id',
    STATE                 varchar(20) null comment '实例状态',
    EXECUTE_ID            varchar(30) null comment '执行id',
    CREATED_BY            varchar(30) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(30) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(30) null,
    constraint JOB_FLOW_INSTANCES_ID_INDEX
        unique (ID)
)
    comment '工作流实例表';

create table dehoop.JOB_FUNCTIONS
(
    ID                    varchar(32)  not null
        primary key,
    NAME                  varchar(200) null comment '函数名称',
    CLASS_NAME            varchar(200) null comment '类名',
    RESOURCE_ID           varchar(32)  null comment '资源依赖',
    DIRECTOR              varchar(32)  null comment '负责人',
    DESCR                 varchar(500) null comment '用途描述',
    COMMAND               varchar(500) null comment '命令行格式',
    PARAMS_DESCR          varchar(500) null comment '参数说明',
    CREATED_BY            varchar(32)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(32)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(32)  null,
    PROJECT_ID            varchar(32)  null comment '项目id',
    ENV_ID                varchar(32)  null comment '环境id'
)
    comment '原函数表';

create table dehoop.JOB_OUTLINE_WORKS
(
    ID                    varchar(30)                 not null comment '主键'
        primary key,
    NAME                  varchar(200)                not null comment '作业名称',
    FLOW_ID               varchar(30)                 not null comment '作业流',
    TYPE                  varchar(200)                not null comment '作业类型',
    WORKSPACE_ID          varchar(30)                 null comment '作业空间id',
    PARENT_ID             varchar(30)                 null comment '父级id',
    DIRECTOR              varchar(30)                 not null comment '作业负责人',
    DESCR                 varchar(500)                null comment '作业描述',
    PROJECT_ID            varchar(30)                 not null comment '项目id',
    ENV_ID                varchar(30)                 not null comment '环境id',
    STATE                 varchar(200) default 'INIT' null comment 'INIT 未锁定状态 LOCKED 锁定',
    LOCKER                varchar(30)                 null comment '锁定人',
    RUNNING_STATUS        varchar(200)                null comment 'SUCCESS成功 ERROR失败',
    CREATED_BY            varchar(30)                 null,
    CREATION_DATE         datetime                    null,
    LAST_UPDATED_BY       varchar(30)                 null,
    LAST_UPDATE_DATE      datetime                    null,
    OBJECT_VERSION_NUMBER int                         null,
    TENANT_ID             varchar(30)                 null,
    TEMPLATE_TYPE         varchar(20)                 not null comment '模板类型 DEFAULT 启动分层/ EMPTY 不启动分层',
    WORK_VERSION          varchar(32)                 null,
    DATA_LAYER_ID         varchar(32)                 null comment '所属分层id'
)
    comment '离线作业表 ';

create table dehoop.JOB_REAL_WORKS
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    NAME                  varchar(200) not null comment '作业名称',
    TYPE                  varchar(200) not null comment '作业类型',
    WORKSPACE_ID          varchar(30)  null comment '作业空间id',
    DIRECTOR              varchar(30)  not null comment '作业负责人',
    DESCR                 varchar(500) null comment '作业描述',
    PROJECT_ID            varchar(30)  not null comment '项目id',
    ENV_ID                varchar(30)  not null comment '环境id',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null,
    WORK_VERSION          varchar(32)  null,
    WEB_CONFIG            text         null comment '页面配置信息',
    FLOW_CONFIG           text         null comment '实时作业流配置',
    VERSION_ID            varchar(32)  null comment '版本号',
    STATE                 varchar(255) null comment '状态(LOCKED 锁定 /INIT未锁定 )',
    LOCKER                varchar(32)  null comment '锁定人',
    RUNNING_STATUS        varchar(32)  null comment '判断实时作业是否运行成功过',
    FLINK_JOB_ID          varchar(50)  null comment '实时作业jobId'
)
    comment '实时作业表 ';

create table dehoop.JOB_REAL_WORK_NODE_CONFIGS
(
    ID                    varchar(32)  not null comment '主键'
        primary key,
    NODE_ID               varchar(32)  not null comment '实时作业节点id',
    CONFIG                text         null comment '节点配置',
    CREATED_BY            varchar(32)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(32)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(32)  null comment '租户id',
    TYPE                  varchar(50)  null comment '作业类型',
    NAME                  varchar(200) null
)
    comment '实时作业单个作业配置';

create table dehoop.JOB_SYNC_WORK_API_CONFIGS
(
    ID                    varchar(100)                  not null comment '主键'
        primary key,
    WORK_ID               varchar(30)                   not null comment '作业ID',
    REQUEST_PARAM         text                          null comment '请求参数',
    REQUEST_HEADER        text                          null comment '请求头',
    REQUEST_BODY          text                          null comment '请求体',
    DATA_LIST_PATH        varchar(100)                  null comment '数组路径',
    SYNC_FIELD            text                          null comment '同步字段信息',
    RESPONSE_INFO         mediumtext                    null comment 'api返回信息',
    PAGE_LOOP             varchar(30) default 'DISABLE' null comment 'ENABLE开启分页循环，DISABLE关闭分页循环',
    START_PAGE            int                           null comment '开始页',
    END_PAGE              int                           null comment '截至页',
    PAGE_SIZE             int                           null comment '页大小',
    CREATED_BY            varchar(30)                   null comment '创建者',
    CREATION_DATE         datetime                      null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                   null comment '更新者',
    LAST_UPDATE_DATE      datetime                      null comment '更新时间',
    OBJECT_VERSION_NUMBER int                           null comment '数据版本',
    TENANT_ID             varchar(30)                   null comment '租户id'
)
    comment '数据同步api配置表';

create table dehoop.JOB_SYNC_WORK_CONFIG
(
    ID                    varchar(30)   not null comment '主键'
        primary key,
    FROM_DB_ID            varchar(30)   null comment '来源数据源id',
    TO_DB_ID              varchar(30)   null comment '去向数据源id',
    FROM_DB_TABLE         varchar(50)   null comment '来源数据表',
    TO_DB_TABLE           varchar(50)   null comment '去向数据表',
    CONDITION_SQL         varchar(500)  null comment '来源数据过滤',
    PARTITION_VALUE       varchar(100)  null comment '去向分区信息',
    MAPPING_CONFIG        text          null comment '字段映射配置',
    WORK_ID               varchar(30)   not null comment '作业id',
    API_CONFIG_ID         varchar(50)   null comment 'api配置的ID',
    MAX_CONCURRENT_NUM    int           null comment '最大并发数',
    MAX_TRANSFER_SPEED    double        null comment '最大传输速率',
    MAX_ERROR_COUNT       int           null comment '最大错误记录',
    CREATED_BY            varchar(30)   null,
    CREATION_DATE         datetime      null,
    LAST_UPDATED_BY       varchar(30)   null,
    LAST_UPDATE_DATE      datetime      null,
    OBJECT_VERSION_NUMBER int           null,
    TENANT_ID             varchar(30)   null,
    APPEND_MODE           varchar(20)   null comment '新增 ENABLE 全更新 DISABLE',
    SPLIT_BY_VALUE        varchar(100)  null comment '分割键',
    GEN_TABLE_SQL         text          null comment '建表自定义sql语句',
    API_URL               varchar(200)  null comment 'API 地址',
    REQUEST_BODY          varchar(500)  null comment '请求体',
    TIMESTAMP_FIELD       varchar(200)  null comment '时间戳字段',
    COLUMN_FAMILY         varchar(200)  null comment 'hbase专用 用来记录列族',
    LINK_TABLES           varchar(2000) null comment '存储多表关联信息'
)
    comment '作业数据同步配置表';

create table dehoop.JOB_TEMP_WORKS
(
    ID                    varchar(30)                 not null comment '主键'
        primary key,
    NAME                  varchar(200)                not null comment '作业名称',
    DESCR                 varchar(500)                null comment '作业描述',
    TYPE                  varchar(30)                 not null comment '作业类型',
    WORKSPACE_ID          varchar(30)                 null comment '资源组ID',
    PROJECT_ID            varchar(30)                 not null comment '所述项目ID',
    ENV_ID                varchar(30)                 not null comment '所属环境ID',
    DIRECTOR              varchar(30)                 not null comment '项目负责人',
    PARENT_ID             varchar(30)                 null comment '父级目录ID',
    STATE                 varchar(200) default 'INIT' null comment '状态 锁定为LOCKED 未锁定为INIT',
    LOCKER                varchar(30)                 null comment '锁定人',
    CREATED_BY            varchar(30)                 null comment '创建者',
    CREATION_DATE         datetime                    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                 null comment '更新者',
    LAST_UPDATE_DATE      datetime                    null comment '更新时间',
    OBJECT_VERSION_NUMBER int                         null comment '数据版本',
    TENANT_ID             varchar(30)                 null comment '租户id'
)
    comment '临时作业表';

create table dehoop.JOB_WORK_CONTROLS
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    WORK_ID               varchar(30)  null comment '作业id',
    PERIOD_TYPE           varchar(200) null comment '调度类型',
    PERIOD_NUM            int          null comment '时间间隔数',
    START_TIME            time         null comment '周期开始时间',
    END_TIME              time         null comment '周期结束时间',
    PERIOD_TIME           time         null comment '调度时间',
    TYPE                  varchar(100) null comment '调度类型',
    LEVEL                 varchar(100) null comment '调度优先级',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null,
    DAYS                  varchar(500) null
)
    comment '作业调度配置表';

create table dehoop.JOB_WORK_FLOWS
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    FLOW_ID               varchar(30) null comment '工作流id',
    WORK_ID               varchar(30) null comment '作业id',
    CREATED_BY            varchar(30) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(30) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(30) null
)
    comment ' 工作流表';

create table dehoop.JOB_WORK_LOGS
(
    ID                    varchar(32)                   not null comment '主键'
        primary key,
    EXECUTE_ID            varchar(50)                   not null comment '执行id',
    CONTENT               text                          null comment '日志内容',
    CREATED_BY            varchar(30)                   null,
    CREATION_DATE         datetime                      null,
    LAST_UPDATED_BY       varchar(30)                   null,
    LAST_UPDATE_DATE      datetime                      null,
    OBJECT_VERSION_NUMBER int                           null,
    TENANT_ID             varchar(30)                   null,
    STATE                 varchar(20) default 'PENDING' null,
    WORK_ID               varchar(32)                   null comment '绑定的作业id',
    constraint JOB_WORK_LOGS_EXECUTE_ID_uindex
        unique (EXECUTE_ID)
)
    comment '作业日志表';

create table dehoop.JOB_WORK_PARAMS
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    WORK_ID               varchar(30)  not null comment '作业id',
    PARAM_NAME            varchar(200) null comment '参数名称',
    PARAM_VALUE           varchar(200) null comment '参数值',
    CREATION_DATE         datetime     null,
    CREATED_BY            varchar(30)  null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null
)
    comment '作业参数表';

create table dehoop.JOB_WORK_RESOURCES
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    WORK_ID               varchar(30) null comment '作业id',
    RESOURCE_ID           varchar(30) null comment '资源文件id',
    CREATED_BY            varchar(30) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(30) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(30) null
)
    comment '工作资源表 ';

create table dehoop.JOB_WORK_SCRIPTS
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    WORK_ID               varchar(30) null comment '作业id',
    CONTENT               text        null comment '脚本内容',
    CREATED_BY            varchar(30) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(30) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(30) null,
    KEY_WORDS             text        null comment '关键字'
)
    comment '工作脚本表';

create table dehoop.MODELING_BUSINESS_PROCESS
(
    ID                    varchar(32)  null,
    NAME_EN               varchar(200) null comment '英文名称',
    NAME_CN               varchar(200) null comment '中文名称',
    NAME_EN_ABBR          varchar(200) null comment '英文名称缩写',
    DIRECTOR              varchar(200) null comment '负责人',
    DESCR                 varchar(500) null comment '描述',
    DATA_FIELD            varchar(32)  null comment '数据域',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id',
    VERSION_ID            varchar(32)  null comment '当前版本号'
)
    comment '数据域业务流程';

create table dehoop.MODELING_DATA_DIMENSION
(
    ID                    varchar(30)                 not null comment '主键'
        primary key,
    NAME                  varchar(255)                null comment '名称',
    TABLE_NAME            varchar(255)                null comment '表名',
    GRANULARITY           varchar(100)                null comment '颗粒度:ATOMIC_TRANSACTIONS 原子事务型/PERIODIC_SNAPSHOT 周期快照型',
    TYPE                  varchar(32)                 null comment '类型',
    DESCR                 varchar(600)                null comment '描述',
    PRIMARY_KEY           varchar(255)                null comment '主键',
    MEMORY_SPACE_ID       varchar(255)                null comment '存储空间ID',
    PROJECT_ID            varchar(30)                 null comment '项目ID',
    STATE                 varchar(100)                null comment '状态',
    TABLE_CREATED         tinyint      default 0      null comment '表是否创建',
    PUBLISH_STATE         varchar(100) default 'INIT' not null comment '发布状态 
''INIT'' 新建
''SUBMITTED'' 已提交
''PUBLISHED'' 已发布',
    CREATED_BY            varchar(30)                 null comment '创建者',
    CREATION_DATE         datetime                    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                 null comment '更新者',
    LAST_UPDATE_DATE      datetime                    null comment '更新时间',
    OBJECT_VERSION_NUMBER int                         null comment '数据版本',
    TENANT_ID             varchar(30)                 null comment '租户id',
    VERSION_ID            varchar(32)                 null comment '当前版本号'
)
    comment '数据维度';

create table dehoop.MODELING_DATA_DIMENSION_FIELD
(
    ID                    varchar(50)                null comment '主键',
    FIELD_NAME            varchar(255)               null comment '名称',
    FIELD_TYPE            varchar(50)                null comment '字段类型',
    MODULE_TYPE           varchar(100)               null comment '组件类型',
    LOGIC_NAME            varchar(255)               null comment '逻辑名称',
    LENGTH                int                        null comment '长度',
    PRECISIONS            int                        null comment '精度',
    SCALES                int                        null comment '数值范围',
    COMMENTS              varchar(500)               null comment '字段说明',
    IS_PRIMARY_KEY        tinyint     default 0      null comment '是否主键',
    IS_NOT_NULL           tinyint     default 0      null comment '是否非空',
    DATA_DIMENSION_ID     varchar(30)                null comment '数据维度ID',
    SEQUENCE_NUMBER       int                        null comment '序号',
    FIELD_CREATED         tinyint     default 0      null comment '自否创建',
    CREATED_BY            varchar(30)                null comment '创建者',
    CREATION_DATE         datetime                   null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                null comment '更新者',
    LAST_UPDATE_DATE      datetime                   null comment '更新时间',
    OBJECT_VERSION_NUMBER int                        null comment '数据版本',
    TENANT_ID             varchar(30)                null comment '租户id',
    PUBLISH_STATE         varchar(30) default 'INIT' null comment '字段是否被发布
''INIT'' 未发布
''PUBLISHED'' 已发布',
    MODEL_RELATIONSHIP    varchar(32)                null comment '模型关系'
)
    comment '数据建模实体字段表';

create table dehoop.MODELING_DATA_FIELD
(
    ID                    varchar(32)       null,
    NAME_EN               varchar(200)      null comment '英文名称',
    NAME_CN               varchar(200)      null comment '中文名称',
    NAME_EN_ABBR          varchar(200)      null comment '英文名称缩写',
    DIRECTOR              varchar(200)      null comment '负责人',
    DESCR                 varchar(500)      null comment '描述',
    CREATED_BY            varchar(30)       null comment '创建者',
    CREATION_DATE         datetime          null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)       null comment '更新者',
    LAST_UPDATE_DATE      datetime          null comment '更新时间',
    OBJECT_VERSION_NUMBER int               null comment '数据版本',
    TENANT_ID             varchar(30)       null comment '租户id',
    VERSION_ID            varchar(32)       null comment '当前版本号',
    IS_DEFAULT            tinyint default 0 null
)
    comment '数据域';

create table dehoop.MODELING_DATA_MODEL
(
    ID                    varchar(32) not null
        primary key,
    WEB_CONFIG            longtext    null comment '前端坐标位置',
    RELATION_CONFIG       longtext    null comment '关系配置',
    CREATED_BY            varchar(30) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(30) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(30) null,
    BUSINESS_PROCESS_ID   varchar(32) null
);

create table dehoop.MODELING_DATA_MODEL_TABLE
(
    ID                    varchar(32)   not null
        primary key,
    MODEL_ID              varchar(2000) null comment '前端坐标位置',
    TYPE                  varchar(32)   null,
    MODEL_TABLE_ID        varchar(2000) null comment '关系配置',
    CREATED_BY            varchar(30)   null,
    CREATION_DATE         datetime      null,
    LAST_UPDATED_BY       varchar(30)   null,
    LAST_UPDATE_DATE      datetime      null,
    OBJECT_VERSION_NUMBER int           null,
    TENANT_ID             varchar(30)   null
)
    comment '数据模型关联实体维度表';

create table dehoop.MODELING_ENTITY
(
    ID                    varchar(30)                 not null comment '主键'
        primary key,
    NAME                  varchar(255)                null comment '名称',
    TABLE_NAME            varchar(255)                null comment '表名',
    PRIMARY_KEY           varchar(255)                null comment '主键',
    DESCR                 varchar(600)                null comment '描述',
    MEMORY_SPACE_ID       varchar(255)                null comment '存储空间ID',
    PROJECT_ID            varchar(30)                 null comment '项目ID',
    GRANULARITY           varchar(100)                null comment '颗粒度:ATOMIC_TRANSACTIONS 原子事务型/PERIODIC_SNAPSHOT 周期快照型',
    STATE                 varchar(100)                null comment '状态',
    TABLE_CREATED         tinyint      default 0      null comment '表是否创建',
    PUBLISH_STATE         varchar(100) default 'INIT' not null comment '发布状态 
''INIT'' 新建
''SUBMITTED'' 已提交
''PUBLISHED'' 已发布',
    CREATED_BY            varchar(30)                 null comment '创建者',
    CREATION_DATE         datetime                    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                 null comment '更新者',
    LAST_UPDATE_DATE      datetime                    null comment '更新时间',
    OBJECT_VERSION_NUMBER int                         null comment '数据版本',
    TENANT_ID             varchar(30)                 null comment '租户id',
    VERSION_ID            varchar(32)                 null comment '当前版本号',
    DATA_LAYER_ID         varchar(32)                 null comment '数据分层id'
);

create table dehoop.MODELING_ENTITY_BUSINESS_PROCESS
(
    ID                    varchar(32) null,
    ENTITY_ID             varchar(32) null comment '实体ID',
    BUSINESS_PROCESS_ID   varchar(32) null comment '业务过程主键',
    DATA_FIELD_ID         varchar(32) null comment '数据域ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    VERSION_ID            varchar(32) null comment '当前版本号'
)
    comment '实体业务过程关联表';

create table dehoop.MODELING_ENTITY_FIELD
(
    ID                    varchar(50)                null comment '主键',
    FIELD_NAME            varchar(255)               null comment '名称',
    FIELD_TYPE            varchar(50)                null comment '字段类型',
    MODULE_TYPE           varchar(100)               null comment '组件类型',
    LOGIC_NAME            varchar(255)               null comment '逻辑名称',
    LENGTH                int                        null comment '长度',
    PRECISIONS            int                        null comment '精度',
    SCALES                int                        null comment '数值范围',
    COMMENTS              varchar(500)               null comment '字段说明',
    IS_PRIMARY_KEY        tinyint     default 0      null comment '是否主键',
    IS_NOT_NULL           tinyint     default 0      null comment '是否非空',
    ENTITY_ID             varchar(30)                null comment '项目ID',
    SEQUENCE_NUMBER       int                        null comment '序号',
    FIELD_CREATED         tinyint     default 0      null comment '自否创建',
    CREATED_BY            varchar(30)                null comment '创建者',
    CREATION_DATE         datetime                   null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                null comment '更新者',
    LAST_UPDATE_DATE      datetime                   null comment '更新时间',
    OBJECT_VERSION_NUMBER int                        null comment '数据版本',
    TENANT_ID             varchar(30)                null comment '租户id',
    PUBLISH_STATE         varchar(30) default 'INIT' null comment '字段是否被发布
''INIT'' 未发布
''PUBLISHED'' 已发布',
    MODEL_RELATIONSHIP    varchar(32)                null comment '模型关系',
    MEASURE_UNIT_ID       varchar(32)                null comment '度量单位的id'
)
    comment '数据建模实体字段表';

create table dehoop.MODELING_MEASURE_UNIT
(
    ID                    varchar(32)  not null
        primary key,
    NAME_CN               varchar(200) null comment '中文名称',
    NAME_EN               varchar(200) null comment '英文名称',
    CATEGORY_ID           varchar(32)  null comment '分类id',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment '度量单位';

create table dehoop.MODELING_MEASURE_UNIT_CATEGORIES
(
    ID                    varchar(32)  not null comment '主键'
        primary key,
    NAME                  varchar(200) not null comment '类目名称',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment '度量单位类型';

create table dehoop.MODELING_PROJECT_DATA_FIELD
(
    ID                    varchar(32) null,
    PROJECT_ID            varchar(32) null comment '实体ID',
    DATA_FIELD_ID         varchar(32) null comment '数据域ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    VERSION_ID            varchar(32) null comment '当前版本号'
)
    comment '实体业务过程关联表';

create table dehoop.MODELING_TAG_BUSINESS_PROCESS
(
    ID                    varchar(32) null,
    TAG_ID                varchar(32) null comment '实体ID',
    BUSINESS_PROCESS_ID   varchar(32) null comment '业务过程主键',
    DATA_FIELD_ID         varchar(32) null comment '数据域ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    VERSION_ID            varchar(32) null comment '当前版本号'
)
    comment '标签业务过程关联表';

create table dehoop.MODELING_WORK_FLOW_BUSINESS_PROCESS
(
    ID                    varchar(32) null,
    FLOW_ID               varchar(32) null comment '工作流ID',
    BUSINESS_PROCESS_ID   varchar(32) null comment '业务过程主键',
    DATA_FIELD_ID         varchar(32) null comment '数据域ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    VERSION_ID            varchar(32) null comment '当前版本号'
)
    comment '工作流业务过程关联表';

create table dehoop.PRO_ENVIRONMENT
(
    ID                    varchar(30)                    not null comment '主键'
        primary key,
    PROJECT_ID            varchar(30)                    not null comment '环境所属项目ID',
    NAME                  varchar(200)                   not null comment '环境名称',
    DATABASE_NAME         varchar(200)                   null comment '数据库名称',
    DESCR                 varchar(500)                   null comment '环境描述',
    SCHEDULING_QUEUE      varchar(200)                   not null comment '调度队列',
    RES_FILE_PATH         varchar(500)                   null comment '资源文件路径',
    MAX_QUERY_COUNT       int          default 5000      null comment '查询结果限制',
    MAX_FILE_SIZE         int          default 50        null comment '资源文件大小限制',
    TYPE                  varchar(200) default 'DEV'     null comment '环境类型',
    CYCLE_SCHEDULING      varchar(200) default 'ENABLE'  not null comment '启用/禁用周期调度',
    SUBMIT_CHECK          varchar(200) default 'ENABLE'  not null comment '启用/禁用提交检查',
    DOWNLOAD_QUERY        varchar(200) default 'ENABLE'  not null comment '启用/禁用下载查询接口',
    EDIT_JOB              varchar(200) default ' ENABLE' not null comment '启用/禁用编辑作业',
    RUN_JOB               varchar(200) default 'ENABLE'  not null comment '启用/禁用运行作业',
    SUBMIT_JOB            varchar(200) default 'ENABLE'  not null comment '启用/禁用提交作业',
    BUSINESS_ID           varchar(30)                    null comment '业务板块ID',
    CREATED_BY            varchar(30)                    null comment '创建者',
    CREATION_DATE         datetime                       null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                    null comment '更新者',
    LAST_UPDATE_DATE      datetime                       null comment '更新时间',
    OBJECT_VERSION_NUMBER int                            null comment '数据版本',
    TENANT_ID             varchar(30)                    null comment '租户id'
)
    comment '项目管理-环境配置表 关联表';

create table dehoop.PRO_ENVIRONMENT_CAL
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    CAL_ID                varchar(30) not null comment '计算引擎ID',
    ENV_ID                varchar(30) not null comment '环境配置ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment '项目管理-环境配置-计算引擎 关联表';

create table dehoop.PRO_ENVIRONMENT_DATASOURCE
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    ENV_ID                varchar(30) not null comment '环境配置ID',
    DATASOURCE_ID         varchar(30) not null comment '数据源ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment '项目管理-环境配置-数据源 关联表';

create table dehoop.PRO_ENVIRONMENT_WORKSPACE
(
    ID                    varchar(30) not null
        primary key,
    ENV_ID                varchar(30) not null comment '环境配置ID',
    WORKSPACE_ID          varchar(30) not null comment '工作空间ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment '项目管理-环境配置-工作空间 关联表';

create table dehoop.PRO_ENV_MAPPINGS
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    LEFT_ENV_ID           varchar(30) not null comment '左侧环境ID',
    RIGHT_ENV_ID          varchar(30) not null comment '右侧环境ID',
    PROJECT_ID            varchar(30) null comment '项目ID',
    MAPPING_RELATIONS     text        null comment '映射关系',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment '项目环境映射关系表';

create table dehoop.PRO_MANAGE
(
    ID                    varchar(30)                     not null comment '主键'
        primary key,
    NAME                  varchar(200)                    not null comment '项目名称',
    TYPE                  varchar(200)                    not null comment '计算引擎类型',
    DESCR                 varchar(500)                    null comment '项目描述',
    JOB_TYPE              varchar(500)                    null comment '作业类型',
    RES_FILE_TYPE         varchar(500)                    null comment '资源文件类型',
    STATE                 varchar(200) default 'TOCONFIG' null comment '项目状态 INDEV 开发中，TOCONFIG 待配置',
    CODE                  varchar(200) default 'INIT'     not null comment 'INIT为初始状态 DELETE为放入回收站状态',
    CREATED_BY            varchar(30)                     null comment '创建者',
    CREATION_DATE         datetime                        null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                     null comment '更新者',
    LAST_UPDATE_DATE      datetime                        null comment '更新时间',
    OBJECT_VERSION_NUMBER int                             null comment '数据版本',
    TENANT_ID             varchar(30)                     null comment '租户id'
)
    comment '项目管理表,包含项目的基本信息';

create table dehoop.PRO_USER
(
    ID                    varchar(30)                    not null comment '主键'
        primary key,
    USER_ID               varchar(30)                    not null comment '用户ID',
    PROJECT_ID            varchar(30)                    not null comment '项目ID',
    ROLE                  varchar(30)                    not null comment '成员角色
ADMIN 管理员
CREATOR 创建者
DEV 开发者
OPS 运维人员
VISITOR 访客',
    IS_APPROVER           varchar(200) default 'DISABLE' null comment '是否为审批人 ENABLE/DISABLE',
    CREATED_BY            varchar(30)                    null comment '创建者',
    CREATION_DATE         datetime                       null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                    null comment '更新者',
    LAST_UPDATE_DATE      datetime                       null comment '更新时间',
    OBJECT_VERSION_NUMBER int                            null comment '数据版本',
    TENANT_ID             varchar(30)                    null comment '租户id'
)
    comment '项目管理 - 成员表 关联表';

create table dehoop.REL_PACKAGES
(
    ID                    varchar(30)                            not null comment '发布包ID'
        primary key,
    NAME                  varchar(200)                           not null comment '发布包的名称',
    DESCR                 varchar(500)                           null comment '发布包的描述',
    STATE                 varchar(200) default 'WAITING_CONFIRM' null comment '发布包的状态
WAITING_CONFIRM=待审核
PUBLISHED=已发布
TO_SUBMIT=待提交',
    SRC_ENV_ID            varchar(30)                            not null comment '发布包的源环境ID',
    DST_ENV_ID            varchar(30)                            not null comment '发布包发布到的环境',
    PROJECT_ID            varchar(30)                            not null comment '项目ID',
    PROPOSER_ID           varchar(30)                            not null comment '申请人ID',
    DEFA_APPROR_ID        varchar(30)                            not null comment '默认审批人ID',
    CUR_APPROR_ID         varchar(30)                            not null comment '当前审批人ID',
    REL_DATE              datetime                               null comment '发布时间',
    APP_DATE              datetime                               null comment '申请时间',
    APPR_OPINION          varchar(500)                           null comment '审批意见',
    CREATED_BY            varchar(30)                            null comment '创建者',
    CREATION_DATE         datetime                               null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                            null comment '更新者',
    LAST_UPDATE_DATE      datetime                               null comment '更新时间',
    OBJECT_VERSION_NUMBER int                                    null comment '数据版本',
    TENANT_ID             varchar(30)                            null comment '租户id'
)
    comment '发布包表';

create table dehoop.REL_PACKAGE_WORKS
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    PACKAGE_ID            varchar(30) not null comment '发布包ID',
    WORK_RES_ID           varchar(30) not null comment '作业资源ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    VERSION_ID            varchar(32) null
)
    comment '发布包与作业资源关联表';

create table dehoop.RES_BUSINESS_UNIT
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    NAME                  varchar(255) null comment '名称',
    CAL_ID                varchar(30)  null comment '计算引擎ID',
    WORKSPACE_ID          varchar(30)  null comment '工作空间ID',
    DATABASE_NAME         varchar(255) null comment '数据库名称',
    ADMIN_ID              varchar(30)  null comment '管理员ID',
    ICON                  varchar(30)  null comment '图标',
    DESCRIPTION           varchar(600) null comment '描述',
    ENVIRONMENT_TYPE      varchar(100) null comment '环境类型:DEV/PROD',
    STATE                 varchar(20)  null comment '状态 是否被禁用(ENABLE 启用/ DISABLE 禁用/DELETE 删除)',
    CREATED_BY            varchar(30)  null comment '创建者',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id',
    CREATION_DATE         datetime     null comment '创建时间'
);

create table dehoop.RES_CAL_ENGINES
(
    ID                    varchar(30)  not null comment '计算引擎ID'
        primary key,
    NAME                  varchar(200) not null comment '计算引擎名称',
    TYPE                  varchar(200) not null comment '计算引擎类型',
    CREATOR               varchar(30)  not null comment '计算引擎的创建者ID',
    ENGINE_INFO           varchar(500) null comment '计算引擎的连接信息',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null
)
    comment '计算引擎表';

create table dehoop.RES_DATASOURCES
(
    ID                    varchar(30)                             not null comment '数据源ID'
        primary key,
    NAME                  varchar(200) collate utf8mb4_general_ci null,
    TYPE                  varchar(200)                            not null comment '数据源类型',
    CREATOR               varchar(30)                             not null comment '数据源创建者ID',
    CONNECT_INFO          text                                    not null comment '数据源连接信息',
    DESCR                 varchar(500)                            null comment '数据源描述',
    STATE                 varchar(10)  default 'ENABLE'           not null comment '数据源状态',
    CODE                  varchar(200) default 'INIT'             not null comment '数据源标识默认为INIT 如果为DEFAULT则为默认数据源',
    CREATED_BY            varchar(30)                             null,
    CREATION_DATE         datetime                                null,
    LAST_UPDATED_BY       varchar(30)                             null,
    LAST_UPDATE_DATE      datetime                                null,
    OBJECT_VERSION_NUMBER int                                     null,
    TENANT_ID             varchar(30)                             null
)
    comment '数据源表';

create table dehoop.RES_DATASOURCE_AUTHORITY
(
    ID                    varchar(32) not null comment '主键'
        primary key,
    DATASOURCE_ID         varchar(32) not null comment '数据源id',
    PROJECT_ID            varchar(32) not null comment '项目id',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id',
    constraint RES_DATASOURCE_AUTHORITY_ID_uindex
        unique (ID)
)
    comment '数据源授权管理';

create table dehoop.RES_LABEL
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    NAME                  varchar(200) not null comment '标签名称',
    COLOR                 varchar(200) not null comment '标签颜色',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment '资产管理标签表';

create table dehoop.RES_LABEL_TABLE
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    TABLE_ID              varchar(30) not null comment '表ID',
    LABEL_ID              varchar(30) not null comment '标签ID',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment '资产管理标签与表关联表';

create table dehoop.RES_RESOURCE_FILES
(
    ID                    varchar(30)                   not null comment '主键'
        primary key,
    NAME                  varchar(200)                  not null comment '资源名称',
    TYPE                  varchar(200)                  not null comment '资源类型 (DIR/JAR/TXT)',
    DIRECTOR              varchar(30)                   not null comment '资源负责人',
    DESCR                 varchar(500)                  null comment '资源描述',
    PARENT_ID             varchar(30)                   null comment '资源父级id',
    STATE                 varchar(200) default 'ENABLE' not null comment '状态(ENABLE 启用/ DISABLE 禁用)',
    PROJECT_ID            varchar(30)                   not null comment '项目id',
    ENV_ID                varchar(30)                   not null comment '环境id',
    HDFS_FILE_ID          varchar(30)                   null comment 'HDFS文件id',
    CREATED_BY            varchar(30)                   null,
    CREATION_DATE         datetime                      null,
    LAST_UPDATED_BY       varchar(30)                   null,
    LAST_UPDATE_DATE      datetime                      null,
    OBJECT_VERSION_NUMBER int                           null,
    TENANT_ID             varchar(30)                   null,
    IS_DELETED            int          default 0        null comment '资源文件假删除'
)
    comment '资源文件表';

create table dehoop.RES_SERVERS
(
    ID                    varchar(30)                not null comment '服务器ID'
        primary key,
    NAME                  varchar(200)               not null comment '服务器名称',
    MAX_CONCURRENT_NUM    int                        null comment '服务器最大并发数',
    ADDRESS               varchar(20)                not null comment '服务器地址',
    DESCR                 varchar(500)               null comment '服务器描述',
    DATASOURCE_PORT       int                        null comment '服务器数据源端口',
    DEVELOP_PORT          int                        null comment '服务器开发端口',
    WORKSPACE_ID          varchar(30)                not null comment '服务器所属工作空间ID',
    CREATED_BY            varchar(30)                null,
    CREATION_DATE         datetime                   null,
    LAST_UPDATED_BY       varchar(30)                null,
    LAST_UPDATE_DATE      datetime                   null,
    OBJECT_VERSION_NUMBER int                        null,
    TENANT_ID             varchar(30)                null,
    STATE                 varchar(30) default 'INIT' not null comment '服务器状态 (ALIVE 活跃 / DEAD 死亡)'
)
    comment '服务器表';

create table dehoop.RES_TABLE_CYCLES
(
    ID                    varchar(30) not null
        primary key,
    TABLE_ID              varchar(30) null comment '表id',
    TOTAL_NUM             mediumtext  null comment '数据总条数',
    CREATED_BY            varchar(30) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(30) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(30) null,
    GEN_DATE              datetime    null comment '生成时间'
)
    comment '资产数据生命周期表';

create table dehoop.RES_TABLE_HISTORICAL
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    TOTAL_SIZE            mediumtext  not null comment '数据总量',
    ENVIRONMENT_TYPE      varchar(50) null comment '环境类型',
    GENERATE_DATE         datetime    not null comment '产生日期',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment '表历史数据';

create table dehoop.RES_TABLE_INFOS
(
    ID                    varchar(30)                not null comment '主键'
        primary key,
    NAME                  varchar(200)               not null comment '表名',
    DB_RESOURCE_ID        varchar(30)                null comment '数据源id',
    TYPE                  varchar(100)               not null comment '表类型',
    ENV_TYPE              varchar(50)                null comment '所在环境的类型',
    CREATED_BY            varchar(30)                null,
    CREATION_DATE         datetime                   null,
    LAST_UPDATED_BY       varchar(30)                null,
    LAST_UPDATE_DATE      datetime                   null,
    OBJECT_VERSION_NUMBER int                        null,
    TENANT_ID             varchar(30)                null,
    DESCR                 varchar(300)               null,
    ENV_ID                varchar(30)                null comment '环境id',
    DB_URL                varchar(500)               null comment '表 数据源的地址',
    DIRECTOR              varchar(30)                null,
    SITE_TYPE             varchar(30)                null comment '外部数据 内部数据(OUT_SITE/IN_SITE)',
    DATA_LENGTH           mediumtext                 null,
    TOTAL_ROWS            mediumtext                 null comment '表总行数',
    SOURCE_TYPE           varchar(100) default 'DEV' null comment '表的来源类型
数据开发 DEV
数据标签 TAG
维度建模 MODELING',
    BUSINESS_UNIT_ID      varchar(32)                null comment '存储空间',
    SOURCE_ID             varchar(32)                null comment '数据源ID',
    BUSINESS_PROCESS_ID   varchar(32)                null,
    DATA_FIELD            varchar(32)                null
)
    comment '资产表信息存储表';

create table dehoop.RES_TABLE_LINEAGES
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    WORK_ID               varchar(30)  null comment '作业id',
    FROM_TABLE_ID         varchar(30)  not null comment '来源表id',
    TO_TABLE_ID           varchar(30)  not null comment '去向表id',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null,
    WORK_TYPE             varchar(100) null comment '作业类型'
)
    comment '数据表血缘关系表';

create table dehoop.RES_TABLE_LOGS
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    CONTENT               varchar(500) null comment '日志内容',
    TABLE_ID              varchar(30)  null comment '表id',
    TYPE                  varchar(100) null comment '日志类型',
    EXECUTOR              varchar(30)  null comment '操作人',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null
)
    comment '表修改历史记录表';

create table dehoop.RES_WORKSPACES
(
    ID                    varchar(30)                  not null comment '工作空间ID'
        primary key,
    NAME                  varchar(200)                 not null comment '工作空间名称',
    DESCR                 varchar(500)                 null comment '工作空间描述',
    CREATOR               varchar(30)                  not null comment '工作空间创建者ID',
    STATE                 varchar(10) default 'ENABLE' not null comment '工作空间启用禁用状态（ENABLE/DISABLE）',
    CREATED_BY            varchar(30)                  null,
    CREATION_DATE         datetime                     null,
    LAST_UPDATED_BY       varchar(30)                  null,
    LAST_UPDATE_DATE      datetime                     null,
    OBJECT_VERSION_NUMBER int                          null,
    TENANT_ID             varchar(30)                  null
)
    comment '工作空间表';

create table dehoop.SCH_FLOW_INSTANCE_QUEUES
(
    ID                    varchar(30)              not null comment '主键ID'
        primary key,
    FROM_FLOW_INSTANCE_ID varchar(50)              not null comment '上游依赖工作流实例id',
    TO_FLOW_INSTANCE_ID   varchar(50)              not null comment '下游依赖工作流实例id',
    HAS_PUSHED            varchar(30) default 'NO' null comment '此依赖关系是否已经运行过,YES未发布运行过 NO为未运行',
    TYPE                  varchar(50)              null comment '依赖类型，为自依赖或工作流之间的依赖
SELF 自依赖
OTHER 其他工作流的依赖',
    CREATED_BY            varchar(30)              null comment '创建者',
    CREATION_DATE         datetime                 null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)              null comment '更新者',
    LAST_UPDATE_DATE      datetime                 null comment '更新时间',
    OBJECT_VERSION_NUMBER int                      null comment '数据版本',
    TENANT_ID             varchar(30)              null comment '租户id'
)
    comment '工作流发布运行时的依赖关系';

create table dehoop.SCH_FLOW_WORKS
(
    ID                    varchar(32)                  not null
        primary key,
    WORK_ID               varchar(32)                  null comment '作业id',
    FLOW_ID               varchar(32)                  null comment '工作流id',
    VERSION_ID            varchar(32)                  null comment '版本id',
    IS_PUBLISHING         varchar(32) default 'ENABLE' null comment '作业是否处于发布中状态',
    CREATED_BY            varchar(32)                  null,
    CREATION_DATE         datetime                     null,
    LAST_UPDATED_BY       varchar(32)                  null,
    LAST_UPDATE_DATE      datetime                     null,
    OBJECT_VERSION_NUMBER int                          null,
    TENANT_ID             varchar(32)                  null,
    TYPE                  varchar(50)                  null,
    PROJECT_ID            varchar(32)                  null,
    ENV_ID                varchar(32)                  null,
    DIRECTOR              varchar(32)                  null
)
    comment '绑定版本表';

create table dehoop.SCH_WORK_INSTANCES
(
    ID                    varchar(32) not null
        primary key,
    WORK_ID               varchar(32) null comment '作业id',
    FLOW_ID               varchar(32) null comment '工作流id',
    STATE                 varchar(30) null comment '实例状态',
    EXECUTE_ID            varchar(32) null comment '执行id',
    FLOW_INSTANCE_ID      varchar(32) null comment '工作流实例id',
    CREATED_BY            varchar(32) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(32) null,
    LAST_UPDATE_DATE      datetime    null,
    TENANT_ID             varchar(32) null,
    OBJECT_VERSION_NUMBER int         null,
    START_DATE            datetime    null,
    END_DATE              datetime    null,
    TYPE                  varchar(50) null,
    WORK_INSTANCE_ID      varchar(32) null,
    PROJECT_ID            varchar(32) null,
    ENV_ID                varchar(32) null,
    VERSION_ID            varchar(32) null,
    PLAN_DATE             datetime    null,
    DIRECTOR              varchar(32) null comment '实例负责人',
    FLINK_JOB_ID          varchar(60) null comment '实时作业 flink返回的jobId'
)
    comment '调度中作业实例表';

create table dehoop.SCH_WORK_INSTANCE_QUEUES
(
    ID                    varchar(30) not null comment '主键id'
        primary key,
    FROM_WORK_INSTANCE_ID varchar(50) null comment '上游依赖的实例id',
    TO_WORK_INSTANCE_ID   varchar(50) null comment '下游作业实例id',
    TO_WORK_ID            varchar(50) null comment '下游作业id',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '上次更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment '作业实例的依赖关系';

create table dehoop.SCH_WORK_JOBS
(
    ID                    varchar(32) not null
        primary key,
    WORK_ID               varchar(32) null comment '作业id',
    FLOW_ID               varchar(32) null comment '工作流id',
    JOB_ID                mediumtext  not null comment 'PowerJobId',
    CREATED_BY            varchar(32) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(32) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(32) null,
    TYPE                  varchar(50) null,
    FLOW_INSTANCE_ID      varchar(32) null,
    INSTANCE_ID           mediumtext  null comment 'powerjob jobId对应的instanceId'
)
    comment 'powerjob定时器操作记录';

create table dehoop.SCH_WORK_QUEUES
(
    ID                    varchar(32) not null
        primary key,
    EXECUTE_ID            varchar(32) null,
    VERSION_ID            varchar(32) null,
    FLOW_INSTANCE_ID      varchar(32) null,
    WORK_INDEX            int         null,
    CREATED_BY            varchar(32) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(32) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(32) null
)
    comment '作业执行队列';

create table dehoop.SCH_WORK_STATES
(
    ID                    varchar(32)  not null
        primary key,
    BUSINESS_TYPE         varchar(200) null comment '资源类型',
    BUSINESS_ID           varchar(32)  null comment '资源id',
    OPT_TYPE              varchar(200) null comment '操作方式',
    COMMENT               varchar(500) null comment '评论',
    STATE                 varchar(50)  null comment '状态',
    FLOW_ID               varchar(32)  null comment '工作流id',
    CREATED_BY            varchar(32)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(32)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(32)  null,
    ENV_ID                varchar(32)  null comment '环境id',
    PROJECT_ID            varchar(32)  null
)
    comment '作业资源状态表';

create table dehoop.SCH_WORK_VERSIONS
(
    ID                    varchar(32)  not null
        primary key,
    VERSION_NAME          varchar(200) null comment '作业版本名称',
    WORK_ID               varchar(32)  null comment '作业id',
    WORK_NAME             varchar(200) null comment '作业名称',
    FLOW_ID               varchar(32)  null comment '工作流id',
    FLOW_NAME             varchar(200) null comment '工作流名称',
    WORK_FLOW_CONFIG      text         null comment '工作流配置',
    CONTROL_CONFIG        text         null comment '调度信息',
    FLOW_DEPENDENTS       text         null comment '工作流上下游依赖',
    WORK_SCRIPTS          text         null comment '作业脚本',
    RES_DEPENDENTS        text         null comment '资源依赖',
    WORK_PARAMS           text         null comment '资源参数',
    DIRECTOR              varchar(32)  null comment '负责人',
    PUBLISH_DATE          datetime     null comment '发布时间',
    TYPE                  varchar(200) null comment '类型',
    CREATED_BY            varchar(32)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(32)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(32)  null,
    WORK_INFO             text         null,
    ENV_ID                varchar(32)  null,
    WORK_TYPE             varchar(50)  null,
    SYNC_WORK_CONFIG      text         null comment '同步作业配置',
    BASELINE_ID           varchar(30)  null comment '基线ID',
    REAL_WORK_CONFIG      text         null comment '实时作业配置',
    REAL_NODES_CONFIG     text         null,
    ENTITY_CONFIG         text         null comment '实体的配置',
    ENTITY_FIELD_CONFIG   text         null comment '实体的字段配置',
    DDLV_CONFIG_INFO      text         null comment 'ddl可视化配置信息',
    DDLV_FIELD_INFO       text         null comment 'ddl可视化字段信息',
    SYNC_API_CONFIG       text         null comment 'api同步配置信息',
    ETL_CONFIG            text         null comment 'ETL配置信息'
)
    comment '作业版本表';

create table dehoop.SYS_ATTACHMENTS
(
    ID                    varchar(30)               not null comment '主键'
        primary key,
    NAME                  varchar(200)              not null comment '附件名称',
    TYPE                  varchar(20)               null comment '附件文件类型',
    SIZE                  bigint                    null comment '附件大小单位KB',
    CREATOR               varchar(30)               not null comment '附件创建者',
    CREATED_BY            varchar(30)               null,
    CREATION_DATE         datetime                  null,
    LAST_UPDATED_BY       varchar(30)               null,
    LAST_UPDATE_DATE      datetime                  null,
    OBJECT_VERSION_NUMBER int                       null,
    TENANT_ID             varchar(30)               null,
    SPACE                 varchar(30) default 'SYS' not null comment 'SYS 系统中文件  RES 资源中文件'
)
    comment '附件表';

create table dehoop.SYS_DEHOOP_CONFIG
(
    ID                    varchar(32)  not null comment '东海项目配置id'
        primary key,
    LOGO                  varchar(32)  null comment 'logo的attachmentid',
    THEME_COLOR           varchar(200) null comment '主题色',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
)
    comment '东海项目配置';

create table dehoop.SYS_LOOKUP_TYPE
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    TYPE                  varchar(200) not null comment 'lookup_type类型',
    NAME                  varchar(200) not null comment 'lookup_type名称',
    DESCR                 varchar(500) null comment 'lookup_type描述',
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null
)
    comment 'look_type表';

create table dehoop.SYS_LOOKUP_VALUE
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    NAME                  varchar(100) not null comment 'look_value名称',
    TYPE                  varchar(100) not null comment 'look_value类型',
    DESCR                 varchar(500) null comment 'look_value描述',
    CODE                  varchar(100) not null comment 'look_value对应code',
    VALUE_ORDER           int          not null comment 'look_value排序',
    GROUP_NAME            varchar(30)  null,
    CREATED_BY            varchar(30)  null,
    CREATION_DATE         datetime     null,
    LAST_UPDATED_BY       varchar(30)  null,
    LAST_UPDATE_DATE      datetime     null,
    OBJECT_VERSION_NUMBER int          null,
    TENANT_ID             varchar(30)  null
)
    comment 'look_value表';

create table dehoop.SYS_UPDATE_LOG
(
    ID                    varchar(30)  null,
    TITLE                 varchar(255) null comment '标题',
    VERSION               varchar(255) null comment '版本',
    UPDATE_DATE           datetime     null comment '更新时间',
    ICON_ID               varchar(100) null comment '图标',
    INFORMATION           varchar(500) null comment '基本信息',
    CONTENT               longtext     null comment '内容',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
);

create table dehoop.SYS_VERIFICATION_CODE
(
    ID                    varchar(30)                    not null comment '主键'
        primary key,
    TYPE                  varchar(100)                   not null comment '验证码使用类型',
    LINK                  varchar(200)                   not null comment '联系方式',
    CODE                  varchar(10)                    not null comment '验证码code',
    STATE                 varchar(10) default 'NOT_USED' not null comment '验证码状态 (USED 使用过/ NOT_USED 未使用过)',
    CREATED_BY            varchar(30)                    null,
    CREATION_DATE         datetime                       null,
    LAST_UPDATED_BY       varchar(30)                    null,
    LAST_UPDATE_DATE      datetime                       null,
    OBJECT_VERSION_NUMBER int                            null,
    TENANT_ID             varchar(30)                    null
)
    comment '验证码表';

create table dehoop.TAG_CATEGORIES
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    NAME                  varchar(200) not null comment '类目名称',
    DESCR                 varchar(500) null comment '类目描述',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id',
    PROJECT_ID            varchar(32)  null comment '项目ID'
)
    comment '标签类目表';

create table dehoop.TAG_CATEGORY_LABEL
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    CATEGORY_ID           varchar(30) not null comment '类目名称',
    LABEL_ID              varchar(30) not null comment '类目描述',
    CREATED_BY            varchar(30) null comment '创建者',
    CREATION_DATE         datetime    null comment '创建时间',
    LAST_UPDATED_BY       varchar(30) null comment '更新者',
    LAST_UPDATE_DATE      datetime    null comment '更新时间',
    OBJECT_VERSION_NUMBER int         null comment '数据版本',
    TENANT_ID             varchar(30) null comment '租户id'
)
    comment '标签类目与标签关联表';

create table dehoop.TAG_CUSTOM_CLAUSE
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    FIELD                 varchar(255) null comment '字段名',
    FILED_OPTION          varchar(255) null comment '选择条件',
    FILED_VALUE           varchar(255) null comment '值',
    PARENT_ID             varchar(30)  null comment '父节点ID',
    HIERARCHY_ID          varchar(30)  null comment '层级ID',
    TAG_ID                varchar(30)  null comment '标签ID',
    CLAUSES_TYPE          varchar(50)  null comment '分类',
    FIELD_TYPE            varchar(50)  null comment '字段类型 tag/entity',
    NODE_LOGIC            varchar(255) null comment '节点逻辑符号 OR/AND',
    NODE_TYPE             varchar(255) null comment '节点类型 BRANCH/LEAF',
    SERIAL_NUMBER         int          null comment '顺序号',
    FIELD_TAG_ID          varchar(255) null comment '字段标签
',
    FIELD_HIERARCHY_ID    varchar(255) null comment '字段分层',
    CREATED_BY            varchar(30)  null comment '创建人',
    LAST_UPDATED_BY       varchar(30)  null comment '上次更新人',
    LAST_UPDATE_DATE      datetime     null comment '上次更新时间',
    CREATION_DATE         datetime     null comment '创建时间',
    OBJECT_VERSION_NUMBER int          null comment '版本',
    TENANT_ID             varchar(30)  null comment '租户ID'
);

create table dehoop.TAG_CUSTOM_HIERARCHY
(
    ID                     varchar(30)                not null comment '主键'
        primary key,
    HIERARCHY_NAME         varchar(255)               null comment '名称',
    HIERARCHY_ID           varchar(30)                null comment '层级ID',
    TAG_ID                 varchar(30)                null comment '标签ID',
    SERIAL_NUMBER          int                        null comment '序号',
    CLAUSE_SQL             varchar(800)               null comment '拼接的SQL',
    CREATED_BY             varchar(30)                null comment '创建人',
    LAST_UPDATED_BY        varchar(30)                null comment '上次更新人',
    LAST_UPDATE_DATE       datetime                   null comment '上次更新时间',
    CREATION_DATE          datetime                   null comment '创建时间',
    OBJECT_VERSION_NUMBER  int                        null comment '版本',
    TENANT_ID              varchar(30)                null comment '租户ID',
    DESCR                  varchar(500)               null comment '描述',
    CLAUSES_TYPE           varchar(255)               null comment '条件类型 attribute：属性/  index：指标',
    DATA_NUM               int         default 0      null comment '数据量',
    RUNNING_STATE          varchar(50) default 'INIT' null,
    HIERARCHY_NAME_EN_ABBR varchar(255)               null comment '英文缩写',
    IS_CREATED             tinyint                    null comment '是否已创建'
)
    comment '自定义表层级';

create table dehoop.TAG_CUSTOM_LABLES
(
    ID                    varchar(30)                 not null comment '主键',
    NAME                  varchar(200)                not null comment '标签名称',
    EN_NAME               varchar(200)                not null comment '标签英文名称',
    DESCR                 varchar(500)                null comment '标签描述',
    TYPE                  varchar(200)                not null comment '标签类型',
    DST_TABLE             varchar(200)                not null comment '结果表名',
    MAIN_TABLE            varchar(200)                null comment '主表',
    LABEL_PK              varchar(200)                null comment '标签主键',
    RULE                  varchar(500)                null comment '标签规则',
    STORE_LIFE_CYCLE      int                         null comment '存储生命周期',
    STATE                 varchar(200) default 'INIT' null comment '标签状态
INIT 新建/SUBMITTED 已提交',
    RUNNING_STATE         varchar(200) default 'INIT' null comment 'INIT 未运行 / RUNNING运行中/SUCCEED 成功  / FAILED 失败
标签测试运行状态',
    CREATED_BY            varchar(30)                 null comment '创建者',
    LAST_UPDATE_DATE      datetime                    null comment '更新时间',
    LAST_UPDATED_BY       varchar(30)                 null comment '更新者',
    OBJECT_VERSION_NUMBER int                         null comment '数据版本',
    CREATION_DATE         datetime                    null comment '创建时间',
    TENANT_ID             varchar(30)                 null comment '租户id',
    DIRECTOR              varchar(32)                 null comment '负责人'
);

create table dehoop.TAG_EXECUTE_QUERY
(
    ID                    varchar(32) not null comment '主键'
        primary key,
    EXECUTE_ID            varchar(32) null comment '执行ID',
    QUERY_RESULT          longtext    null comment '查询结果',
    CREATED_BY            varchar(32) null,
    CREATION_DATE         datetime    null,
    LAST_UPDATED_BY       varchar(32) null,
    LAST_UPDATE_DATE      datetime    null,
    OBJECT_VERSION_NUMBER int         null,
    TENANT_ID             varchar(32) null,
    STATE                 varchar(50) null comment 'INIT 初始化 /OVER 插入结束',
    NUM                   int         null comment '数据量',
    TAG_ID                varchar(50) null
);

create table dehoop.TAG_FIGURE
(
    ID                    varchar(50)  not null comment '主键'
        primary key,
    NAME                  varchar(255) null comment '名称',
    ENTITY_ID             varchar(50)  null comment '实体ID',
    STATE                 varchar(255) null comment '状态',
    CALCULATION_MODE      varchar(50)  null comment '计算模式(Spark/Hive)',
    RUNNING_STATE         varchar(255) null comment '运行状态',
    CLAUSE_SQL            varchar(500) null comment '运行SQL',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id'
);

create table dehoop.TAG_FIGURE_CALCULATION
(
    ID                    varchar(50)  not null comment '主键'
        primary key,
    FIGURE_ID             varchar(255) null comment '画像ID',
    GRAPH_ID              varchar(50)  null comment '图形ID',
    HIERARCHY_ID          varchar(50)  null comment '分层ID',
    TYPE                  varchar(255) null comment '类型：FIGURE:画像/GRAPH:图形',
    RUNNING_STATE         varchar(255) null comment '运行状态 INIT/RUNNING/ABORT/OVER',
    CLAUSE_SQL            varchar(500) null comment '运行SQL',
    NUM                   int          null comment '数据量',
    ENTITY_NUM            int          null comment '实体数量',
    VERSION_ID            varchar(50)  null comment '版本',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id',
    COMPARE_HIERARCHY_ID  varchar(50)  null comment '比较标签'
);

create table dehoop.TAG_FIGURE_CLAUSE
(
    ID                    varchar(30)  not null comment '主键',
    FIELD                 varchar(255) null comment '字段名',
    FILED_OPTION          varchar(255) null comment '选择条件',
    FILED_VALUE           varchar(255) null comment '值',
    PARENT_ID             varchar(30)  null comment '父节点ID',
    FIGURE_ID             varchar(30)  null comment '标签画像ID',
    CLAUSES_TYPE          varchar(50)  null comment '分类',
    FIELD_TYPE            varchar(50)  null comment '字段类型 tag/entity',
    NODE_LOGIC            varchar(255) null comment '节点逻辑符号 OR/AND',
    NODE_TYPE             varchar(255) null comment '节点类型 BRANCH/LEAF',
    SERIAL_NUMBER         int          null comment '顺序号',
    FIELD_TAG_ID          varchar(255) null comment '字段标签
',
    FIELD_HIERARCHY_ID    varchar(255) null comment '字段分层',
    CREATED_BY            varchar(30)  null comment '创建人',
    LAST_UPDATED_BY       varchar(30)  null comment '上次更新人',
    LAST_UPDATE_DATE      datetime     null comment '上次更新时间',
    CREATION_DATE         datetime     null comment '创建时间',
    OBJECT_VERSION_NUMBER int          null comment '版本',
    TENANT_ID             varchar(30)  null comment '租户ID'
);

create table dehoop.TAG_FIGURE_GRAPH
(
    ID                    varchar(50)                  not null comment '主键'
        primary key,
    FIGURE_ID             varchar(50)                  null comment '标签画像ID',
    NAME                  varchar(255)                 null comment '名称',
    TYPE                  varchar(100)                 null comment '类型',
    TAG_ID                varchar(50)                  null comment '标签ID',
    COMPARE_TAG_ID        varchar(50)                  null comment '比对标签ID',
    GRAPH_TYPE            varchar(100)                 null comment '图形类型',
    STATE                 varchar(100)                 null comment '状态',
    RUNNING_STATE         varchar(100)                 null comment '运行状态',
    SERIAL_NUMBER         int         default 0        null comment '序号',
    CREATED_BY            varchar(30)                  null comment '创建者',
    CREATION_DATE         datetime                     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)                  null comment '更新者',
    LAST_UPDATE_DATE      datetime                     null comment '更新时间',
    OBJECT_VERSION_NUMBER int                          null comment '数据版本',
    TENANT_ID             varchar(30)                  null comment '租户id',
    TAG_STATE             varchar(32) default 'ENABLE' null comment '标签状态'
);

create table dehoop.TAG_HISTORICAL_DATA
(
    ID                    varchar(30)  not null comment '主键'
        primary key,
    NUM                   int          not null comment '数据量',
    GENERATE_DATE         datetime     not null comment '时间',
    LABEL_ID              varchar(30)  not null comment '标签ID',
    CREATED_BY            varchar(30)  null comment '创建者',
    CREATION_DATE         datetime     null comment '创建时间',
    LAST_UPDATED_BY       varchar(30)  null comment '更新者',
    LAST_UPDATE_DATE      datetime     null comment '更新时间',
    OBJECT_VERSION_NUMBER int          null comment '数据版本',
    TENANT_ID             varchar(30)  null comment '租户id',
    GENERATE_DAY          varchar(255) null comment '数据生成日期',
    HIERARCHY_ID          varchar(50)  null,
    DATA_SIZE             int          null
)
    comment '标签历史数据量';

create table dehoop.TAG_LABELS
(
    ID                    varchar(30)                 not null comment '主键'
        primary key,
    NAME                  varchar(200)                not null comment '标签名称',
    EN_NAME               varchar(200)                not null comment '标签英文名称',
    DESCR                 varchar(500)                null comment '标签描述',
    TYPE                  varchar(200)                not null comment '标签类型',
    DST_TABLE             varchar(200)                not null comment '结果表名',
    MAIN_TABLE            varchar(200)                null comment '主表',
    LABEL_PK              varchar(200)                null comment '标签主键',
    RULE                  varchar(500)                null comment '标签规则',
    STORE_LIFE_CYCLE      int                         null comment '存储生命周期',
    STATE                 varchar(200) default 'INIT' null comment '标签状态
INIT 新建/SUBMITTED 已提交',
    RUNNING_STATE         varchar(200) default 'INIT' null comment 'INIT 未运行 / SUCCEED 成功  / FAILED 失败
标签测试运行状态',
    CALCULATION_MODE      varchar(50)                 null comment 'HIVE/SPARKSQL',
    BUSINESS_UNIT_ID      varchar(50)                 null comment '存储空间',
    CREATED_BY            varchar(30)                 null comment '创建者',
    LAST_UPDATE_DATE      datetime                    null comment '更新时间',
    LAST_UPDATED_BY       varchar(30)                 null comment '更新者',
    OBJECT_VERSION_NUMBER int                         null comment '数据版本',
    CREATION_DATE         datetime                    null comment '创建时间',
    TENANT_ID             varchar(30)                 null comment '租户id',
    DIRECTOR              varchar(32)                 null comment '负责人',
    PROJECT_ID            varchar(32)                 null comment '项目ID'
)
    comment '标签表';

create table dehoop.TAG_SCH_JOBS
(
    ID                    varchar(30) not null comment '主键'
        primary key,
    JOB_ID                mediumtext  not null comment 'powerjob 工作id',
    LABEL_ID              varchar(30) not null comment '标签ID',
    CREATED_BY            varchar(30) null comment '创建人',
    OBJECT_VERSION_NUMBER int         null comment '版本',
    LAST_UPDATED_BY       varchar(30) null comment '上次更新人',
    LAST_UPDATE_DATE      datetime    null comment '上次更新时间',
    CREATION_DATE         datetime    null comment '创建时间',
    TENANT_ID             varchar(30) null comment '租户ID'
)
    comment '处于定时调度中的标签';



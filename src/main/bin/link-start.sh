#!/bin/bash

# 进入star项目
BASE_PATH=$(cd "$(dirname "$0")" || exit ; pwd)
cd "${BASE_PATH}" || exit

# 关闭原有进程
for metaResult in $(ps -e -o pid,command | grep dehoop-link)
do
    if [ "$metaResult" == java ]; then
      LINK_PID=${PRE_VAL}
    	break
	fi
	PRE_VAL=${metaResult}
done
if [ _"${LINK_PID}" != _ ];then
  kill -9 "${LINK_PID}"
fi

# 创建日志文件
LINK_LOG="${BASE_PATH}"/../log/dehoop-link.log
if [ ! -f "${LINK_LOG}" ]; then
    touch "${LINK_LOG}"
fi

# 启动项目
LINK_APP="${BASE_PATH}"/../lib/dehoop-link-0.0.1.jar
nohup java -jar -Xmx2048m "${LINK_APP}"  --spring.profiles.active=link --spring.config.additional-location="${BASE_PATH}"/../conf/application-link.yml >> "${LINK_LOG}" 2>&1 &
tail -f ${LINK_LOG}

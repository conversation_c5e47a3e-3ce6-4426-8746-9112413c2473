package com.definesys.dehoop.admin.moudle.modeling.dataDimension.pojo.dto;

import com.definesys.dehoop.admin.moudle.modeling.dataDimension.pojo.ModelingDataDimensionField;
import com.definesys.dehoop.admin.moudle.modeling.pojo.TableFieldPojo;
import com.definesys.dehoop.api.serializer.BooleanDeserializer;

import org.springframework.beans.BeanUtils;

import lombok.*;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 数据维度
 * @create date 2021/6/18 15:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ModelingDataDimensionFieldDto extends TableFieldPojo {

    private String id;

    private String moduleType;

    private String fieldName;

    private String fieldType;

    private String logicName;

    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isPrimaryKey;

    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isNotNull;

    private String entityId;

    @JsonProperty("lengthLimit")
    private Integer length;

    private Integer precision;

    private Integer scale;

    private int sequenceNumber;

    private String modelRelationship;

    private Boolean isSys;

    private Boolean isPartitionField;

    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean isIndependent;

    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean initialization;

    @JsonDeserialize(using = BooleanDeserializer.class)
    private Boolean fieldCreated;

    @JsonProperty("comment")
    private String comments;

    private Boolean isAssociated;

    private String measureUnitId;

    private String fieldStandardId;

    private String fieldStandardName;

    @Override
    public String getName() {
        return this.logicName;
    }

    @Override
    public String getType() {
        return this.fieldType;
    }

    @Override
    public void setName(String name) {
        this.setLogicName(name);
    }

    @Override
    public void setType(String type) {
        this.setFieldType(type);
    }

    public static ModelingDataDimensionFieldDto getInstance(
            ModelingDataDimensionField modelingDataDimensionField) {
        ModelingDataDimensionFieldDto modelingDataDimensionFieldDto =
                new ModelingDataDimensionFieldDto();
        BeanUtils.copyProperties(modelingDataDimensionField, modelingDataDimensionFieldDto);
        return modelingDataDimensionFieldDto;
    }

    public ModelingDataDimensionFieldDto(
            String fieldName, String logicName, String moduleType, String fieldType, Integer length) {
        this.moduleType = moduleType;
        this.fieldName = fieldName;
        this.logicName = logicName;
        this.fieldType = fieldType;
        this.length = length;
    }

    public ModelingDataDimensionFieldDto(
            String fieldName,
            String logicName,
            String moduleType,
            String fieldType,
            Integer length,
            Integer precision,
            Integer scale) {
        this.moduleType = moduleType;
        this.fieldName = fieldName;
        this.logicName = logicName;
        this.fieldType = fieldType;
        this.length = length;

        this.precision = precision;
        this.scale = scale;
    }
}

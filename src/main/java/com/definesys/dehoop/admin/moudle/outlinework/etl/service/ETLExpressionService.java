package com.definesys.dehoop.admin.moudle.outlinework.etl.service;

import com.definesys.dehoop.admin.moudle.outlinework.etl.dao.JobETLWorkNodeDao;
import com.definesys.dehoop.admin.moudle.outlinework.etl.dao.JobETLWorkNodeFieldDao;
import com.definesys.dehoop.admin.moudle.outlinework.etl.pojo.dto.JobETLFieldDto;
import com.definesys.dehoop.admin.moudle.outlinework.etl.pojo.dto.JobETLWorkNodeFieldDto;
import com.definesys.dehoop.admin.moudle.outlinework.etl.pojo.entity.JobETLWorkNodeField;

import com.xdap.motor.entity.SnowflakeIdWorker;
import com.xdap.motor.exception.CommonBizExceptionEnum;
import com.xdap.motor.utils.Assert;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年11月26日 18:23
 * @description ETL表达式节点
 */
@Service
@RequiredArgsConstructor
public class ETLExpressionService {

    private final JobETLWorkNodeDao jobETLWorkNodeDao;
    protected final JobETLWorkNodeFieldDao jobETLWorkNodeFieldDao;
    protected final SnowflakeIdWorker snowflakeIdWorker;

    /** 添加字段映射 */
    public void addFieldMapping(List<JobETLWorkNodeFieldDto> jobETLWorkNodeFieldDtoList) {
        Assert.isNotNull(jobETLWorkNodeFieldDtoList, CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        for (int i = 0; i < jobETLWorkNodeFieldDtoList.size(); i++) {
            jobETLWorkNodeFieldDtoList.get(i).setSequenceNumber(i);
        }

        List<JobETLWorkNodeField> jobETLWorkNodeFieldList =
                jobETLWorkNodeFieldDtoList.stream()
                        .map(e -> e.getEntity(snowflakeIdWorker.nextId()))
                        .collect(Collectors.toList());
        jobETLWorkNodeFieldDao.setJobETLWorkNodeField(jobETLWorkNodeFieldList);
    }

    /**
     * 获取字段列表
     *
     * @param nodeId 节点ID
     * @return 表达式字段列表
     */
    public List<JobETLFieldDto> queryFieldList(String nodeId) {
        return null;
    }

    /** 校验语法 */
    public void checkGrammar() {}

    /** 节点处理 */
    public void nodeProcess() {}
}

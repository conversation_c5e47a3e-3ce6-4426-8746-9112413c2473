package com.definesys.dehoop.admin.moudle.tenant.pojo.dto;

import com.definesys.dehoop.admin.moudle.user.pojo.dto.UserInfo;
import com.definesys.mpaas.query.json.MpaasDateTimeDeserializer;
import com.definesys.mpaas.query.json.MpaasDateTimeSerializer;

import lombok.Data;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@Data
public class TenantInfo {

    private String id;

    private String name;

    private String code;

    private String tenantKey;

    private String tenantSecret;

    private Boolean hasWorkNumLimit;

    private Integer workNumLimit;

    private List<UserInfo> tenantAdmins;

    private Integer userNum;

    private Integer proNum;

    private String descr;

    private UserInfo createBy;

    @JsonSerialize(using = MpaasDateTimeSerializer.class)
    @JsonDeserialize(using = MpaasDateTimeDeserializer.class)
    private Date creationDate;

    private String state;

    private String icon;

    /** 租户图标地址 */
    private String iconUrl;

    private String sysRole;
}

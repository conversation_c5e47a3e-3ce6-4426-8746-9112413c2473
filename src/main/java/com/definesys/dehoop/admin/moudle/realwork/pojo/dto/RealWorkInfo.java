package com.definesys.dehoop.admin.moudle.realwork.pojo.dto;

import com.definesys.dehoop.admin.moudle.realwork.pojo.JobRealWorkNodeConfigs;
import com.definesys.dehoop.api.serializer.DateTimeDeserializer;
import com.definesys.dehoop.api.serializer.DateTimeSerializer;

import lombok.Data;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@Data
public class RealWorkInfo {

    private String name;

    private String id;

    private String executeId;

    private String type;

    private String typeMeaning;

    private String director;

    private String directorName;

    private String workspaceId;

    private String workspaceName;

    private String descr;

    private List<JobRealWorkNodeConfigs> nodeList;

    private String webConfig;

    private String workScript;

    private String workId;

    private String versionName;

    private Boolean runTimeState;

    @JsonSerialize(using = DateTimeSerializer.class)
    @JsonDeserialize(using = DateTimeDeserializer.class)
    private Date startTime;

    @JsonSerialize(using = DateTimeSerializer.class)
    @JsonDeserialize(using = DateTimeDeserializer.class)
    private Date endTime;

    private Boolean autoRetryState;

    private String retryMode;

    private Integer retryNum;

    private Integer retryInterval;
}

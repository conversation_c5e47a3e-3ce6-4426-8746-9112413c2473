package com.definesys.dehoop.admin.moudle.project.service;

import com.definesys.dehoop.admin.moudle.businessunit.dao.BusinessUnitDao;
import com.definesys.dehoop.admin.moudle.businessunit.pojo.BusinessUnit;
import com.definesys.dehoop.admin.moudle.calengine.dao.CalEngineDao;
import com.definesys.dehoop.admin.moudle.calengine.pojo.CalEngineEntity;
import com.definesys.dehoop.admin.moudle.calengine.pojo.dto.EngineInfo;
import com.definesys.dehoop.admin.moudle.calengine.service.CalEngineService;
import com.definesys.dehoop.admin.moudle.connect.pojo.ConnectInfo;
import com.definesys.dehoop.admin.moudle.project.dao.ProjectDao;
import com.definesys.dehoop.admin.moudle.project.pojo.ProEnvironmentEntity;
import com.definesys.dehoop.admin.moudle.project.pojo.dto.EnvCalConfigInfo;
import com.definesys.dehoop.admin.moudle.project.pojo.dto.EnvResDto;
import com.definesys.dehoop.admin.moudle.project.pojo.dto.ProEnvironmentDto;
import com.definesys.dehoop.api.constant.ClusterTypeConstants;
import com.definesys.dehoop.api.exception.BusinessUnitExceptionEnum;
import com.definesys.dehoop.api.exception.CalEngineExceptionEnum;
import com.definesys.dehoop.api.exception.JobsExceptionEnum;
import com.definesys.dehoop.api.exception.ProjectExceptionEnum;

import com.xdap.motor.exception.XDapBizException;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class EnvConfigService {

    private final ProjectDao projectDao;

    private final CalEngineDao calEngineDao;

    private final BusinessUnitDao businessUnitDao;

    private final CalEngineService calEngineService;

    public EnvConfigService(
            ProjectDao projectDao,
            CalEngineDao calEngineDao,
            BusinessUnitDao businessUnitDao,
            CalEngineService calEngineService) {
        this.projectDao = projectDao;
        this.calEngineDao = calEngineDao;
        this.businessUnitDao = businessUnitDao;
        this.calEngineService = calEngineService;
    }

    /** 通过calId查询配置 */
    public EnvCalConfigInfo getEnvCalConfigByCalId(String calId, String databaseName) {

        EnvCalConfigInfo result = new EnvCalConfigInfo();

        // 转移配置信息
        EngineInfo engineInfo = calEngineService.getCalEngineById(calId);
        result.setEngineInfo(engineInfo);
        if (ClusterTypeConstants.DORIS.equals(engineInfo.getType())) {
            result.setDatabaseName(databaseName);
        } else {
            result.setDatabaseName(databaseName.toLowerCase());
        }
        return result;
    }

    /** 获取环境计算引擎配置信息 */
    public EnvCalConfigInfo getEnvCalConfig(String envId) {

        EnvCalConfigInfo result = new EnvCalConfigInfo();

        // 获取环境信息
        ProEnvironmentEntity environmentEntity = projectDao.queryProEnvById(envId);
        if (environmentEntity == null) {
            throw new XDapBizException(JobsExceptionEnum.ENV_IS_NOT_EXIST);
        }

        BusinessUnit businessUnit =
                businessUnitDao.getBusinessUintById(environmentEntity.getBusinessId());
        if (businessUnit == null) {
            throw new XDapBizException(BusinessUnitExceptionEnum.BUSINESS_UNIT_NOT_EXIST);
        }
        result.setCalId(businessUnit.getCalId());

        // 转移配置信息
        EngineInfo engineInfo = calEngineService.getCalEngineById(businessUnit.getCalId());

        result.setEngineInfo(engineInfo);

        if (ClusterTypeConstants.DORIS.equals(engineInfo.getType())) {

            result.setDatabaseName(businessUnit.getDatabaseName());
        } else {
            String resFilePath = environmentEntity.getResFilePath();
            if (resFilePath != null && !resFilePath.isEmpty()) {
                if (resFilePath.endsWith("/")) {
                    result.setHadoopPath(resFilePath);
                } else {
                    result.setHadoopPath(resFilePath + "/");
                }
            } else {
                result.setHadoopPath("/dehoop/");
            }
            result.setDatabaseName(businessUnit.getDatabaseName().toLowerCase());
        }

        return result;
    }

    /** 获取环境计算引擎配置信息 */
    public ConnectInfo getEnvCalConnectInfo(String envId) {

        // 从计算引擎中获取数据源链接信息
        ProEnvironmentEntity environmentEntity = projectDao.queryProEnvById(envId);
        if (environmentEntity == null) {
            throw new XDapBizException(JobsExceptionEnum.ENV_IS_NOT_EXIST);
        }

        BusinessUnit businessUnit =
                businessUnitDao.getBusinessUintById(environmentEntity.getBusinessId());
        if (businessUnit == null) {
            throw new XDapBizException(BusinessUnitExceptionEnum.BUSINESS_UNIT_NOT_EXIST);
        }

        CalEngineEntity calEngineEntity = calEngineDao.getCalEngineById(businessUnit.getCalId());
        if (calEngineEntity == null) {
            throw new XDapBizException(CalEngineExceptionEnum.CAL_ENGINE_NOT_EXIST);
        }

        ConnectInfo connectInfo = calEngineService.generateConnectInfo(calEngineEntity);
        connectInfo.setSchema(businessUnit.getDatabaseName());
        return connectInfo;
    }

    /**
     * 检查数据源是够还存在
     *
     * @return datasource 存在结果
     */
    public boolean checkoutDataSource(String projectId, String type, String... datasourceIdList) {

        ProEnvironmentEntity proEnvironmentEntity = projectDao.getEnvByProjectAndType(projectId, type);
        List<String> dataSourceIdList =
                projectDao.queryEnvDatasource(proEnvironmentEntity.getId()).stream()
                        .map(EnvResDto::getId)
                        .collect(Collectors.toList());

        for (String datasourceId : datasourceIdList) {
            if (!dataSourceIdList.contains(datasourceId)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 查询项目环境基本配置信息
     *
     * @param envId 项目环境ID
     * @return 项目基本配置
     */
    public ProEnvironmentDto getProEnvironmentEntity(String envId) {

        Optional<ProEnvironmentEntity> projectEnvInfo =
                Optional.ofNullable(projectDao.getProjectEnvById(envId));

        ProEnvironmentEntity proEnvironmentEntity =
                projectEnvInfo.orElseThrow(
                        () -> new XDapBizException(ProjectExceptionEnum.ENV_IS_NOT_EXIST));
        ProEnvironmentDto proEnvironmentDto = new ProEnvironmentDto();
        BeanUtils.copyProperties(proEnvironmentEntity, proEnvironmentDto);
        return proEnvironmentDto;
    }
}

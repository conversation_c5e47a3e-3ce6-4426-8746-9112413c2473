package com.definesys.dehoop.admin.moudle.modeling.measureunit.pojo.dto;

import com.definesys.dehoop.api.serializer.DateTimeSerializer;

import lombok.Data;

import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

/**
 * <AUTHOR>
 * @date 2022/1/7 16:46
 * @description
 */
@Data
public class MeasureUnitInfo {

    private String id;

    private String nameCn;

    private String nameEn;

    private String categoryId;

    private String creator;

    private String lastUpdater;

    private String lastUpdatedBy;

    private String createdBy;

    @JsonSerialize(using = DateTimeSerializer.class)
    private Date creationDate;

    @JsonSerialize(using = DateTimeSerializer.class)
    private Date lastUpdateDate;

    private String categoryName;
}

package com.definesys.dehoop.admin.moudle.realwork.pojo.dto;

import lombok.Data;

import java.util.List;

@Data
public class InputTableInfo {

    private String databaseType;

    private String databaseId;

    private String databaseName;

    private String tableName;

    private String encodeType;

    private List<ColumnInfo> columns;

    private String kafkaServer;

    private String kafkaTopic;

    private String zookeeperServer;

    private String dataFormat;

    private String dataFormatMeaning;

    private String synchronizationMode;

    private List<String> synchronousOperation;

    private String synchronizationStart;

    private String incrementalField;

    private String pollingTimeInterval;

    private String startTime;

    // 起始文件
    private String startFile;

    private String dataSourceId;

    private String topic;

    private String payload;

    private String qos;

    private String startScn;

    private String schema;

    private String lsn;

    /** kafka消费起点 */
    private String consumptionPoint;

    /** 指定时间戳 */
    private String specifiedTimestamp;

    /** 指定offsets */
    private String specifiedOffset;
}

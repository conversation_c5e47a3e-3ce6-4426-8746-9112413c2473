package com.definesys.dehoop.admin.moudle.outlinework.pojo;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.*;

import lombok.*;

/** 临时作业 */
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "JOB_TEMP_WORKS")
@Style
@NoArgsConstructor
@AllArgsConstructor
@TenantEnable
public class JobTempWorks extends XdapBasePojo {

    @Column("ID")
    private String id;

    @Column("NAME")
    private String name;

    @Column("TYPE")
    private String type;

    @Column("WORKSPACE_ID")
    private String workspaceId;

    @Column("PARENT_ID")
    private String parentId;

    @Column("DIRECTOR")
    private String director;

    @Column("DESCR")
    private String descr;

    @Column("PROJECT_ID")
    private String projectId;

    @Column("ENV_ID")
    private String envId;

    @Column("STATE")
    private String state;

    @Column("LOCKER")
    private String locker;

    @Column(type = ColumnType.JAVA)
    private String workType;
}

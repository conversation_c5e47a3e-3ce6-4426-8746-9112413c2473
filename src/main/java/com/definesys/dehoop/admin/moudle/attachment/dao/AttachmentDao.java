package com.definesys.dehoop.admin.moudle.attachment.dao;

import com.definesys.dehoop.admin.moudle.attachment.pojo.SysAttachments;
import com.definesys.mpaas.query.MpaasQueryFactory;
import com.definesys.mpaas.query.session.MpaasSession;

import com.xdap.motor.utils.StringUtils;

import org.springframework.stereotype.Repository;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Repository
public class AttachmentDao {

    private final MpaasQueryFactory sw;

    public AttachmentDao(MpaasQueryFactory sw) {

        this.sw = sw;
    }

    public void addAttachment(SysAttachments sysAttachments) {

        sw.buildQuery().doInsert(sysAttachments);
    }

    public SysAttachments getAttachment(String attachmentId) {

        if (StringUtils.isBlank(attachmentId)) {
            return null;
        }

        return sw.buildQuery().eq("ID", attachmentId).doQueryFirst(SysAttachments.class);
    }

    public List<SysAttachments> getMultipleAttachment(List<String> attachmentIdList) {

        if (CollectionUtils.isEmpty(attachmentIdList)) {
            return new ArrayList<>();
        }

        return sw.buildQuery().in("ID", attachmentIdList).doQuery(SysAttachments.class);
    }

    public void deleteAttachment(String attachmentId) {

        sw.buildQuery()
                .eq("ID", attachmentId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(SysAttachments.class);
    }
}

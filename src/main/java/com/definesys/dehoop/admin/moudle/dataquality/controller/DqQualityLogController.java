package com.definesys.dehoop.admin.moudle.dataquality.controller;

import com.definesys.dehoop.admin.moudle.dataquality.pojo.DqQualityLog;
import com.definesys.dehoop.admin.moudle.dataquality.pojo.dto.DqQualityLogDto;
import com.definesys.dehoop.admin.moudle.dataquality.pojo.vo.DqQualityReportVo;
import com.definesys.dehoop.admin.moudle.dataquality.service.DqQualityLogQueryService;
import com.definesys.dehoop.admin.moudle.dataquality.service.DqQualityLogService;
import com.definesys.dehoop.api.constant.DqQualityLogConstant;

import com.xdap.motor.annotation.RestWrapper;
import com.xdap.motor.vo.PageRespHelper;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年11月10日 20:21
 */
@RestController
@RequestMapping("/quality/log")
public class DqQualityLogController {

    private final DqQualityLogQueryService dqQualityLogQueryService;
    private final DqQualityLogService dqQualityLogService;

    @Resource private RedisTemplate<String, Object> redisTemplate;

    public DqQualityLogController(
            DqQualityLogQueryService dqQualityLogQueryService, DqQualityLogService dqQualityLogService) {
        this.dqQualityLogQueryService = dqQualityLogQueryService;
        this.dqQualityLogService = dqQualityLogService;
    }

    /** 数据分层添加接口 */
    @PostMapping("/queryDqQualityLog")
    @RestWrapper(msg = "查询成功")
    public PageRespHelper<DqQualityLogDto> queryDqQualityLog(
            @RequestBody DqQualityLogDto dqQualityLogDtoParam) {

        return dqQualityLogQueryService.queryDqQualityLog(dqQualityLogDtoParam);
    }

    /** 质量方案报告 */
    @GetMapping("/getDqQualityReport")
    @RestWrapper(msg = "查询成功")
    public DqQualityReportVo getDqQualityReport(@RequestParam String logId) {

        return dqQualityLogQueryService.getDqQualityReport(logId);
    }

    /** 质量方案报告 */
    @GetMapping("/addLog")
    @RestWrapper(msg = "查询成功")
    public void addLog() {

        DqQualityLogDto dqQualityLogDto = new DqQualityLogDto();

        dqQualityLogDto.setId("id");
        // 方案ID
        dqQualityLogDto.setSchemeId("schemeId");
        // 模式
        dqQualityLogDto.setMode(DqQualityLogConstant.MODE_MANUAL);
        // 执行人
        dqQualityLogDto.setExecutor("userId");

        String channel = "addLog";
        redisTemplate.convertAndSend(channel, dqQualityLogDto);
        // dqQualityLogService.addLog(dqQualityLogDto);

    }

    @GetMapping("/getDqQualityLog")
    @RestWrapper(msg = "查询成功")
    public DqQualityLog getDqQualityLog(String logId) {
        return dqQualityLogQueryService.getDqQualityLog(logId);
    }
}

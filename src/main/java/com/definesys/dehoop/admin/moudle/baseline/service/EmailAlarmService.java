package com.definesys.dehoop.admin.moudle.baseline.service;

import com.definesys.dehoop.admin.moudle.baseline.pojo.dto.AlarmModeInfos;
import com.definesys.dehoop.admin.moudle.notice.service.NoticeService;
import com.definesys.dehoop.admin.moudle.user.pojo.FndUsers;
import com.definesys.dehoop.api.constant.DehoopConstant;
import com.definesys.dehoop.api.properties.AlarmTypeProperties;

import com.xdap.motor.utils.StringUtils;

import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import freemarker.template.Template;

@Service
@Slf4j
@AllArgsConstructor
public class EmailAlarmService implements AlarmTypeService {

    private final NoticeService noticeService;

    private final FreeMarkerConfigurer freeMarkerConfigurer;

    private final AlarmTypeProperties alarmTypeProperties;

    @Override
    public AlarmModeInfos saveAlarmApp(AlarmModeInfos alarmModeInfos) {
        return alarmModeInfos;
    }

    @Override
    public String sendAlarm(AlarmModeInfos alarmModeInfos) {

        Map<String, String> params = alarmModeInfos.getParams();

        // 获取有邮箱的成员
        List<FndUsers> users = alarmModeInfos.getUsers();
        int size = alarmModeInfos.getTotalUserSize();
        users =
                users.stream()
                        .filter(e -> StringUtils.isNotBlank(e.getEmail()))
                        .collect(Collectors.toList());
        int totalSize = users.size();
        if (totalSize == 0) {
            log.error("邮箱告警无可告警用户");
            return DehoopConstant.ERROR;
        }

        // 获取模版
        Template mailTemplate;
        try {
            mailTemplate = freeMarkerConfigurer.getConfiguration().getTemplate("sendAlarmEmail.ftl");
        } catch (Exception e) {
            log.error("告警邮件模版获取失败" + e.getMessage(), e);
            return DehoopConstant.ERROR;
        }

        if (mailTemplate == null) {
            log.error("告警邮件模版为空");
            return DehoopConstant.ERROR;
        }

        int flag = 0;
        for (FndUsers user : users) {
            params.put("username", user.getUsername());

            // 生成模板
            String mailContent = null;
            try {
                mailContent = FreeMarkerTemplateUtils.processTemplateIntoString(mailTemplate, params);
            } catch (Exception e) {
                flag++;
                log.error("告警邮件模版生成失败" + e.getMessage(), e);
                continue;
            }

            try {
                sendAlarmMail(user, mailContent);
            } catch (Exception e) {
                flag++;
                log.error("告警邮件发送失败" + e.getMessage(), e);
            }
        }

        if (flag == 0 && size == totalSize) {
            return DehoopConstant.SUCCESS;
        } else if (flag == totalSize) {
            return DehoopConstant.ERROR;
        } else {
            return DehoopConstant.PARTIAL_SUCCESS;
        }
    }

    /**
     * 发送邮箱告警
     *
     * @param users 用户
     * @param content 内容
     */
    @SneakyThrows
    public void sendAlarmMail(FndUsers users, String content) {

        // 客户需求自定义基线告警发信人和平台名称

        String senderName = "得帆云";
        if (StringUtils.isNotBlank(alarmTypeProperties.getSenderName())) {
            senderName = alarmTypeProperties.getSenderName();
        }

        if (StringUtils.isNotBlank(alarmTypeProperties.getDehoopName())) {
            content = content.replace("【得帆云】【得帆数据中台】", alarmTypeProperties.getDehoopName());
        }

        // 原版：noticeService.sendNormalHtmlEmail(users.getEmail(), content, "基线告警", "得帆云");
        noticeService.sendNormalHtmlEmail(users.getEmail(), content, "基线告警", senderName);
    }
}

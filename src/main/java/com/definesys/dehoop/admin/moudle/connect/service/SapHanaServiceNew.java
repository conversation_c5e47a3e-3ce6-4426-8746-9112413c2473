package com.definesys.dehoop.admin.moudle.connect.service;

import com.definesys.dehoop.admin.moudle.connect.pojo.*;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.TableColumnInfo;
import com.definesys.dehoop.api.constant.DataBaseConstant;

import com.xdap.motor.exception.CommonBizExceptionEnum;
import com.xdap.motor.exception.XDapBizException;

import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.SapHanaSourceDTO;
import com.dtstack.dtcenter.loader.source.DataSourceType;

import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import static com.definesys.dehoop.admin.moudle.connect.service.DataBaseFactory.jdbcTemplateMap;
import static java.util.regex.Pattern.compile;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SapHanaServiceNew implements DataBaseService {

    @Override
    public List<String> getDataType() {
        List<String> dataTypes = new ArrayList<>();
        dataTypes.add("NVARCHAR");
        dataTypes.add("BIGINT");
        dataTypes.add("DOUBLE");
        dataTypes.add("TIMESTAMP");
        dataTypes.add("DATE");
        dataTypes.add("DECIMAL");
        return dataTypes;
    }

    /** 转换成sap hana */
    public static String transToSapHanaType(String columnType) {

        String typeStr = compile("\\(.*?\\)").matcher(columnType).replaceAll("");

        switch (typeStr.toLowerCase()) {
            case "float":
            case "float32":
            case "double":
            case "float64":
                return "double";
            case "uint32":
            case "uint64":
            case "int64":
            case "bigint":
            case "long":
                return "bigint";
            case "int":
            case "int32":
            case "int8":
            case "uint8":
            case "int16":
            case "uint16":
            case "tinyint":
            case "integer":
            case "smallint":
            case "mediumint":
                return "integer";
            case "longtext":
            case "string":
            case "varchar":
            case "varchar2":
            case "nvarchar":
            case "nvarchar2":
                return "nvarchar(1000)";
            case "timestamp":
                return "timestamp";
            case "date":
                return "date";
            default:
                return columnType.toLowerCase(Locale.ROOT);
        }
    }

    @Override
    public ISourceDTO getSource(ConnectInfo connectInfo) {

        return SapHanaSourceDTO.builder()
                .url(connectInfo.getJdbcUrl())
                .username(connectInfo.getUsername())
                .password(connectInfo.getPasswd())
                .poolConfig(PoolConfig.builder().build())
                .build();
    }

    @Override
    public IClient getClient() {

        return ClientCache.getClient(DataSourceType.SAP_HANA1.getVal());
    }

    @Override
    public List<String> queryTableNames(ConnectInfo connectInfo) {

        // 获取schema
        String schema = getClient().getCurrentDatabase(getSource(connectInfo));

        // 获取页数
        Integer page = connectInfo.getPage();
        if (page != null && page < 1) {
            page = 1;
        }

        SqlQueryDTO.SqlQueryDTOBuilder builder = SqlQueryDTO.builder();
        if (connectInfo.getTableName() != null) {
            builder.tableNamePattern(connectInfo.getTableName());
        }
        if (page != null) {
            builder.limit(page * 100);
        }

        List<String> tableNames =
                getClient().getTableList(getSource(connectInfo), builder.schema(schema).view(true).build());
        return tableNames.stream()
                .skip(page == null ? 0 : (page - 1) * 100L)
                .collect(Collectors.toList());
    }

    @Override
    public TableData queryTableData(ConnectInfo connectInfo) {
        if (connectInfo == null) {
            log.error(CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE.getMessage());
            throw new XDapBizException(CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        }

        TableData data = new TableData();
        List<List<String>> previewData =
                getClient()
                        .getPreview(
                                getSource(connectInfo),
                                SqlQueryDTO.builder().tableName(connectInfo.getTableName()).previewNum(10).build());

        // 封装返回结果
        data.setColumnList(previewData.get(0));
        previewData.remove(0);
        data.setDataList(previewData);
        return data;
    }

    @Override
    public void executeSql(ConnectInfo connectInfo, String sql) {

        if (connectInfo == null) {
            log.error(CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE.getMessage());
            throw new XDapBizException(CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        }

        try {
            // 执行sql
            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().sql(sql).build();
            getClient().executeSqlWithoutResultSet(getSource(connectInfo), sqlQueryDTO);

        } catch (Exception e) {

            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public TableInfo getDetailTableInfo(ConnectInfo connectInfo) {
        return new TableInfo();
    }

    @Override
    public TableColumn queryTableColumn(ConnectInfo connectInfo) {

        if (connectInfo == null) {
            throw new XDapBizException(CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        }

        TableColumn columns = new TableColumn();

        List<TableColumnInfo> tableColumnInfos = new ArrayList<>();

        List<ColumnMetaDTO> columnMetaDatas =
                getClient()
                        .getColumnMetaData(
                                getSource(connectInfo),
                                SqlQueryDTO.builder()
                                        .schema(connectInfo.getSchema())
                                        .tableName(connectInfo.getTableName())
                                        .build());

        for (ColumnMetaDTO columnMetaData : columnMetaDatas) {
            TableColumnInfo tableColumnInfo = new TableColumnInfo();
            tableColumnInfo.setField(columnMetaData.getKey());
            tableColumnInfo.setType(columnMetaData.getType());
            tableColumnInfos.add(tableColumnInfo);
        }

        columns.setTableColumnInfos(tableColumnInfos);

        return columns;
    }

    @Override
    public boolean checkDatabaseConnect(ConnectInfo connectInfo) {

        if (connectInfo == null) {
            return false;
        }

        SapHanaSourceDTO source = (SapHanaSourceDTO) getSource(connectInfo);
        return getClient().testCon(source);
    }

    @Override
    public void analyzeTableInfo(ConnectInfo connectInfo) {}

    @Override
    public JdbcTemplate getJdbcTemplate(ConnectInfo connectInfo) {
        JdbcTemplate jdbcTemplate = jdbcTemplateMap.get(connectInfo);
        if (jdbcTemplate == null) {

            DataSourceProperties dataSourceProperties = new DataSourceProperties();
            dataSourceProperties.setUrl(connectInfo.getJdbcUrl());
            dataSourceProperties.setDriverClassName(DataBaseConstant.sapHanaDriverName);
            dataSourceProperties.setUsername(connectInfo.getUsername());
            dataSourceProperties.setPassword(connectInfo.getPasswd());
            jdbcTemplate = new JdbcTemplate(dataSourceProperties.initializeDataSourceBuilder().build());
            jdbcTemplateMap.put(connectInfo, jdbcTemplate);
        }

        return jdbcTemplate;
    }

    @Override
    public List<Map<String, Object>> queryTableDataAsObject(ConnectInfo connectInfo) {

        return getClient()
                .executeQuery(
                        getSource(connectInfo), SqlQueryDTO.builder().sql(connectInfo.getSql()).build());
    }
}

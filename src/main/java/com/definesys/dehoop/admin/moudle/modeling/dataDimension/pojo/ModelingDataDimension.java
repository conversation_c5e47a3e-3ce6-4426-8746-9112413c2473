package com.definesys.dehoop.admin.moudle.modeling.dataDimension.pojo;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.Column;
import com.definesys.mpaas.query.annotation.Style;
import com.definesys.mpaas.query.annotation.Table;
import com.definesys.mpaas.query.annotation.TenantEnable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 模型维度
 * @create date 2021/6/17 16:35
 */
@EqualsAndHashCode(callSuper = true)
@Table(value = "MODELING_DATA_DIMENSION")
@Style
@Data
@TenantEnable
public class ModelingDataDimension extends XdapBasePojo {

    /** 主键 */
    private String id;

    /** 名称 */
    private String name;

    /** 表名 */
    @Column("TABLE_NAME")
    private String tableName;

    /** 颗粒度 */
    @Column("GRANULARITY")
    private String granularity;

    /** 维度类型 */
    private String type;

    /** 描述 */
    private String descr;

    /** 主键 */
    @Column("PRIMARY_KEY")
    private String primaryKey;

    /** 存储空间 */
    @Column("MEMORY_SPACE_ID")
    private String memorySpaceId;

    @Column("PROJECT_ID")
    private String projectId;

    private String state;

    /** 表是否初始化 */
    @Column("TABLE_CREATED")
    private Boolean tableCreated;

    @Column("PUBLISH_STATE")
    private String publishState;

    @Column("VERSION_ID")
    private String versionId;
}

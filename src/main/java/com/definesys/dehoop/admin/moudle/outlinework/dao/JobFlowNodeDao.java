package com.definesys.dehoop.admin.moudle.outlinework.dao;

import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobNodeBranch;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobNodeLoop;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobNodeMerge;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobNodeSubflow;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.dto.NodeBranchInfo;
import com.definesys.mpaas.query.MpaasQueryFactory;

import org.springframework.stereotype.Repository;

import lombok.AllArgsConstructor;

import java.util.List;

@Repository
@AllArgsConstructor
public class JobFlowNodeDao {

    private final MpaasQueryFactory sw;

    public void batchAddNodeBranch(List<JobNodeBranch> nodeBranchList) {

        if (nodeBranchList.isEmpty()) {
            return;
        }

        sw.buildQuery().doBatchInsert(nodeBranchList);
    }

    public void addNodeBranch(JobNodeBranch nodeBranch) {

        sw.buildQuery().doInsert(nodeBranch);
    }

    /**
     * 查询分支
     *
     * @param nodeId 分支节点ID
     * @param downNodeId 下游分支节点ID
     */
    public JobNodeBranch getNodeBranchByDownNodeId(String nodeId, String downNodeId) {

        return sw.buildQuery()
                .eq("NODE_ID", nodeId)
                .eq("DOWN_NODE_ID", downNodeId)
                .doQueryFirst(JobNodeBranch.class);
    }

    public List<JobNodeBranch> queryBranchByNodeId(String nodeId) {

        return sw.buildQuery().eq("NODE_ID", nodeId).doQuery(JobNodeBranch.class);
    }

    public List<JobNodeBranch> queryBranchByNodeId(List<String> nodeIds) {

        return sw.buildQuery().in("NODE_ID", nodeIds).doQuery(JobNodeBranch.class);
    }

    public void updateDownNodeId(String branchId, String downNodeId, String downNodeName) {

        sw.buildQuery()
                .eq("ID", branchId)
                .update("DOWN_NODE_ID", downNodeId)
                .update("DOWN_NODE_NAME", downNodeName)
                .doUpdate(JobNodeBranch.class);
    }

    public void updateDownIdByNodeId(String nodeId, String downNodeId, String downNodeName) {

        sw.buildQuery()
                .eq("NODE_ID", nodeId)
                .update("DOWN_NODE_ID", downNodeId)
                .update("DOWN_NODE_NAME", downNodeName)
                .doUpdate(JobNodeBranch.class);
    }

    public void updateDownIdById(String nodeId, String downNodeId, String downNodeName) {

        sw.buildQuery()
                .eq("ID", nodeId)
                .update("DOWN_NODE_ID", downNodeId)
                .update("DOWN_NODE_NAME", downNodeName)
                .doUpdate(JobNodeBranch.class);
    }

    public void deleteBranchByIdList(List<String> idList) {

        if (idList.isEmpty()) {
            return;
        }

        sw.buildQuery().in("ID", idList).doDelete(JobNodeBranch.class);
    }

    public void deleteBranchByNodeId(String nodeId) {

        sw.buildQuery().in("NODE_ID", nodeId).doDelete(JobNodeBranch.class);
    }

    public void updateBranchInfo(NodeBranchInfo nodeBranchInfo) {

        sw.buildQuery()
                .eq("ID", nodeBranchInfo.getId())
                .update("DESCR", nodeBranchInfo.getDescr())
                .update("SEQUENCE_NUMBER", nodeBranchInfo.getSequenceNumber())
                .update("CONDITIONS", nodeBranchInfo.getConditions())
                .update("NAME", nodeBranchInfo.getName())
                .update("DOWN_NODE_NAME", nodeBranchInfo.getDownNodeName())
                .update("DOWN_NODE_ID", nodeBranchInfo.getDownNodeId())
                .doUpdate(JobNodeBranch.class);
    }

    public void addNodeMergeConfig(JobNodeMerge jobNodeMerge) {

        sw.buildQuery().doInsert(jobNodeMerge);
    }

    public JobNodeMerge getMergeNodeConfigByNodeId(String nodeId) {

        return sw.buildQuery().eq("NODE_ID", nodeId).doQueryFirst(JobNodeMerge.class);
    }

    public void updateMergeNodeConfig(String id, String config, String finishStatus) {

        sw.buildQuery()
                .eq("ID", id)
                .update("CONFIG", config)
                .update("FINISH_STATUS", finishStatus)
                .doUpdate(JobNodeMerge.class);
    }

    public void addSubflow(JobNodeSubflow nodeSubflow) {

        sw.buildQuery().doInsert(nodeSubflow);
    }

    public JobNodeSubflow getSubflow(String nodeId) {

        return sw.buildQuery().eq("NODE_ID", nodeId).doQueryFirst(JobNodeSubflow.class);
    }

    public void updateSubflow(JobNodeSubflow jobNodeSubflow) {

        sw.buildQuery()
                .eq("ID", jobNodeSubflow.getId())
                .update("SUBFLOW_ID", jobNodeSubflow.getSubflowId())
                .update("SUBFLOW_INSTANCE_SUFFIX", jobNodeSubflow.getSubflowInstanceSuffix())
                .update("SUBFLOW_PARAM_CONFIG", jobNodeSubflow.getSubflowParamConfig())
                .doUpdate(JobNodeSubflow.class);
    }

    public void deleteSubflowByNodeId(String nodeId) {

        sw.buildQuery().eq("NODE_ID", nodeId).doDelete(JobNodeSubflow.class);
    }

    public void addNodeLoop(JobNodeLoop nodeLoop) {

        sw.buildQuery().doInsert(nodeLoop);
    }

    public void updateNodeLoop(JobNodeLoop nodeLoop) {

        sw.buildQuery().eq("ID", nodeLoop.getId()).doUpdate(nodeLoop);
    }

    public JobNodeLoop getNodeLoopByNodeId(String nodeId) {

        return sw.buildQuery().eq("NODE_ID", nodeId).doQueryFirst(JobNodeLoop.class);
    }

    public void deleteNodeLoopByNodeId(String nodeId) {

        sw.buildQuery().eq("NODE_ID", nodeId).doDelete(JobNodeLoop.class);
    }

    public void deleteNodeMergeByNodeId(String nodeId) {
        sw.buildQuery().eq("NODE_ID", nodeId).doDelete(JobNodeMerge.class);
    }
}

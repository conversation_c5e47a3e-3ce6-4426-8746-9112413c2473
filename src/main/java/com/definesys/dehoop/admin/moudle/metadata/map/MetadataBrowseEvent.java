package com.definesys.dehoop.admin.moudle.metadata.map;

import com.definesys.mpaas.common.adapter.UserProfile;

import org.springframework.context.ApplicationEvent;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class MetadataBrowseEvent extends ApplicationEvent {

    private final MethodArgument methodArgument;

    private final UserProfile userProfile;

    public MetadataBrowseEvent(
            Object source, MethodArgument methodArgument, UserProfile userProfile) {
        super(source);
        this.methodArgument = methodArgument;
        this.userProfile = userProfile;
    }
}

package com.definesys.dehoop.admin.moudle.modeling.dataField.dao;

import com.definesys.dehoop.admin.moudle.modeling.dataField.pojo.ModelingBusinessProcess;
import com.definesys.dehoop.admin.moudle.modeling.dataField.pojo.dto.ModelingBusinessProcessDto;
import com.definesys.mpaas.query.MpaasQueryFactory;
import com.definesys.mpaas.query.db.PageQueryResult;
import com.definesys.mpaas.query.session.MpaasSession;

import com.xdap.motor.utils.StringUtils;

import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年10月12日 15:12
 */
@Repository
public class ModelingBusinessProcessDao {

    private final MpaasQueryFactory sw;

    public ModelingBusinessProcessDao(MpaasQueryFactory sw) {

        this.sw = sw;
    }

    public void addModelingBusinessProcess(ModelingBusinessProcess modelingBusinessProcess) {
        sw.buildQuery().doInsert(modelingBusinessProcess);
    }

    public void deleteModelingBusinessProcess(String id) {
        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(ModelingBusinessProcess.class);
    }

    public void updateModelingBusinessProcess(ModelingBusinessProcess modelingBusinessProcess) {
        sw.buildQuery()
                .eq("ID", modelingBusinessProcess.getId())
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("NAME_EN", modelingBusinessProcess.getNameEn())
                .update("NAME_CN", modelingBusinessProcess.getNameCn())
                .update("NAME_EN_ABBR", modelingBusinessProcess.getNameEnAbbr())
                .update("DIRECTOR", modelingBusinessProcess.getDirector())
                .update("DESCR", modelingBusinessProcess.getDescr())
                .update("LAST_UPDATED_BY", MpaasSession.getCurrentUser())
                .update("LAST_UPDATE_DATE", new Date())
                .doUpdate(ModelingBusinessProcess.class);
    }

    public ModelingBusinessProcess getModelingBusinessProcessByName(
            ModelingBusinessProcess modelingBusinessProcess) {
        return sw.buildQuery()
                .or()
                .eq("NAME_EN", modelingBusinessProcess.getNameEn())
                .eq("NAME_EN_ABBR", modelingBusinessProcess.getNameEnAbbr())
                .eq("NAME_CN", modelingBusinessProcess.getNameCn())
                .conjuctionAnd()
                .doQueryFirst(ModelingBusinessProcess.class);
    }

    public ModelingBusinessProcess getModelingBusinessProcess(String id) {

        if (StringUtils.isBlank(id)) {
            return null;
        }
        return sw.buildQuery().eq("ID", id).doQueryFirst(ModelingBusinessProcess.class);
    }

    public ModelingBusinessProcessDto getModelingBusinessProcessDetail(String id) {
        return sw.buildQuery()
                .sql(
                        "select * from ( "
                                + "select  "
                                + " mdf.id, "
                                + "  mdf.NAME_CN nameCn, "
                                + "  mdf.NAME_EN nameEn, "
                                + " mdf.NAME_EN_ABBR nameEnAbbr, "
                                + "  mdf.DATA_FIELD dataField, "
                                + " mdf.descr, "
                                + " mdf.name_cn dataFieldNameCn ,mdf.NAME_EN dataFieldNameEn ,mdf.NAME_EN_ABBR dataFieldNameEnAbbr , "
                                + " mdf.DIRECTOR director, "
                                + " mdf.CREATED_BY createdBy, "
                                + " mdf.CREATION_DATE creationDate, "
                                + " mdf.LAST_UPDATE_DATE lastUpdateDate, "
                                + " mdf.LAST_UPDATED_BY lastUpdatedBy, "
                                + " duser.USERNAME directorName , "
                                + " duser.AVATAR directorAvatar , "
                                + " cuser.USERNAME createdByName, "
                                + " uuser.USERNAME lastUpdatedByName "
                                + "from MODELING_BUSINESS_PROCESS mdf  "
                                + "  left join FND_USERS duser on duser.id =  mdf.DIRECTOR "
                                + "  left join FND_USERS cuser on cuser.id = mdf.CREATED_BY "
                                + "  left join FND_USERS uuser on uuser.id = mdf.LAST_UPDATED_BY "
                                + ") alias ")
                .eq("id", id)
                .doQueryFirst(ModelingBusinessProcessDto.class);
    }

    public List<ModelingBusinessProcess> listModelingBusinessProcess(String searchWord) {
        return sw.buildQuery()
                .like("NAME_CN", searchWord)
                .orderBy("last_update_date", "DESC")
                .doQuery(ModelingBusinessProcess.class);
    }

    public PageQueryResult<ModelingBusinessProcessDto> listModelingBusinessProcessDto(
            ModelingBusinessProcessDto modelingBusinessProcessDto) {
        return sw.buildQuery()
                .sql(
                        "select * from ( select mbp.id ,\n"
                                + "                       mbp.NAME_EN  nameEn ,\n"
                                + "                       mbp.NAME_CN  nameCn ,\n"
                                + "                       mbp.NAME_CN   ,\n"
                                + "                       mbp.NAME_EN_ABBR  nameEnAbbr,\n"
                                + "                       mbp.director  director,\n"
                                + "                       mbp.descr  descr,\n"
                                + "                       mbp.DATA_FIELD  dataField,\n"
                                + "                       mbp.DATA_FIELD ,\n"
                                + "                       mbp.last_update_date  lastUpdateDate,\n"
                                + "                       mbp.CREATION_DATE creationDate,\n"
                                + "                       mbp.CREATION_DATE ,\n"
                                + "                       mbp.last_update_date ,\n"
                                + "                       mbp.name_cn dataFieldNameCn ,mbp.NAME_EN dataFieldNameEn ,mbp.NAME_EN_ABBR dataFieldNameEnAbbr\n"
                                + "                from MODELING_BUSINESS_PROCESS mbp\n"
                                + "                         left join MODELING_DATA_FIELD mdf on mdf.id = mbp.DATA_FIELD ) alias")
                .like("nameCn", modelingBusinessProcessDto.getSearchWord())
                .eq("DATA_FIELD", modelingBusinessProcessDto.getDataField())
                .orderBy("CREATION_DATE", "DESC")
                .doPageQuery(
                        modelingBusinessProcessDto.getPage(),
                        modelingBusinessProcessDto.getPageSize(),
                        ModelingBusinessProcessDto.class);
    }

    public List<ModelingBusinessProcess> queryModelingBusinessProcessByDataFieldList(
            String searchWord) {

        return sw.buildQuery().like("NAME_CN", searchWord).doQuery(ModelingBusinessProcess.class);
    }

    public List<ModelingBusinessProcess> queryModelingBusinessProcessByDataFieldId(
            List<String> DataFieldIdList) {
        return sw.buildQuery().in("DATA_FIELD", DataFieldIdList).doQuery(ModelingBusinessProcess.class);
    }

    /**
     * 按业务过程id查询业务过程
     *
     * @param idList id
     * @return 查询结果
     */
    public List<ModelingBusinessProcess> queryBusinessProcessById(List<String> idList) {

        if (idList.isEmpty()) {

            return new ArrayList<>();
        } else {
            return sw.buildQuery().in("ID", idList).doQuery(ModelingBusinessProcess.class);
        }
    }

    /**
     * 按数据域id查询业务过程
     *
     * @param dataFieldId 数据域id
     * @return 查询结果
     */
    public ModelingBusinessProcess getBusinessProcessByDataFieldId(String dataFieldId) {

        return sw.buildQuery()
                .eq("DATA_FIELD", dataFieldId)
                .doQueryFirst(ModelingBusinessProcess.class);
    }

    public List<ModelingBusinessProcess> queryBusinessProcessByDataFieldId(String dataFieldId) {

        return sw.buildQuery().eq("DATA_FIELD", dataFieldId).doQuery(ModelingBusinessProcess.class);
    }

    public void deleteBusinessProcessByDataFieldId(String dataFieldId) {

        sw.buildQuery()
                .eq("DATA_FIELD", dataFieldId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(ModelingBusinessProcess.class);
    }
}

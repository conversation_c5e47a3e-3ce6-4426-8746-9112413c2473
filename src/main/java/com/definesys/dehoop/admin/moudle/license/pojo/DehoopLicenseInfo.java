package com.definesys.dehoop.admin.moudle.license.pojo;

import org.apache.commons.beanutils.BeanMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DehoopLicenseInfo {

    private static final String FIELD_CUSTOMER_NAME;
    private static final String FIELD_TENANT_NUM;
    private static final String FIELD_ISSUED_TIME;
    private static final String FIELD_EXPIRED_TIME;
    private static final String FIELD_PRODUCT_LIST;

    static {
        FIELD_CUSTOMER_NAME = "customerName";
        FIELD_TENANT_NUM = "tenantNum";
        FIELD_ISSUED_TIME = "issuedTime";
        FIELD_EXPIRED_TIME = "expiredTime";
        FIELD_PRODUCT_LIST = "productList";
    }

    private String customerName;

    private Integer tenantNum;

    private Date issuedTime;

    private Date expiredTime;

    private String productList;

    public static DehoopLicenseInfo convertMapToLicenseInfo(Map<String, Object> extraMap) {
        DehoopLicenseInfo apaasLicenseInfo = new DehoopLicenseInfo();
        apaasLicenseInfo.setCustomerName((String) extraMap.get(FIELD_CUSTOMER_NAME));
        apaasLicenseInfo.setTenantNum(Integer.valueOf(extraMap.get(FIELD_TENANT_NUM).toString()));
        return apaasLicenseInfo;
    }

    public static DehoopLicenseInfo convertExtraToLicenseInfo(LicenseCheckModel lcm) {
        DehoopLicenseInfo dehoopLicenseInfo = new DehoopLicenseInfo();
        BeanMap beanMap = new BeanMap(lcm);
        dehoopLicenseInfo.setCustomerName((String) beanMap.get(FIELD_CUSTOMER_NAME));
        dehoopLicenseInfo.setTenantNum(Integer.valueOf(beanMap.get(FIELD_TENANT_NUM).toString()));
        dehoopLicenseInfo.setProductList((String) beanMap.get(FIELD_PRODUCT_LIST));
        return dehoopLicenseInfo;
    }
}

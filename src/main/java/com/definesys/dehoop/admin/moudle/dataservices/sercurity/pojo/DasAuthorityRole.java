package com.definesys.dehoop.admin.moudle.dataservices.sercurity.pojo;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.Column;
import com.definesys.mpaas.query.annotation.Style;
import com.definesys.mpaas.query.annotation.Table;

import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年09月18日 10:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Style
@AllArgsConstructor
@NoArgsConstructor
@Table(value = "DAS_API_AUTHORITY_ROLE")
public class DasAuthorityRole extends XdapBasePojo {

    private String id;

    @Column("ROLE_ID")
    private String roleId;

    @Column("ROLE_NAME")
    private String roleName;

    @Column("GROUP_ID")
    private String groupId;

    @Column("AUTHORITY_ID")
    private String authorityId;
}

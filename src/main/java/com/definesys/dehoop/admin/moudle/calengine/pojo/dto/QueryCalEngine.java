package com.definesys.dehoop.admin.moudle.calengine.pojo.dto;

import com.definesys.dehoop.admin.moudle.calengine.pojo.CalEngineEntity;

import lombok.Data;

import com.fasterxml.jackson.annotation.JsonInclude;

/** 查询返回对象 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueryCalEngine {

    private CalEngineEntity calEngineEntity;

    private DorisConf dorisConf;

    private Object engineInfo;

    private Object sftpConf;

    private Object yarnConf;

    private Object hdfsConf;

    private Object flinkConf;

    private Object sparkConf;

    private Object hiveServerConf;

    private Object ossConf;

    private Object s3Conf;

    // 引擎创建者头像地址
    private String creatorAvatarUrl;

    // 最后更新者头像地址
    private String updaterAvatarUrl;

    /** 集群类型，前端直接展示. */
    private String clusterType;
}

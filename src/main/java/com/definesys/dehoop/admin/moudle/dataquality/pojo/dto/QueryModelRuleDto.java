package com.definesys.dehoop.admin.moudle.dataquality.pojo.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/3 17:10
 * @description 查询规则对象
 */
@Data
public class QueryModelRuleDto {

    private String id;

    private String name;

    private String descr;

    private String type;

    private String typeMeaning;

    private String qualityModelId;

    private String level;

    private String levelMeaning;

    private Integer weight;

    private String tableAssetId;

    private String businessUnitName;

    private String tableName;

    private String state;

    private List<String> checkField;

    private String unionNull;

    private String valueRange;

    private String unionRepeat;

    private RegulationCheckConfigDto regulationCheckConfig;

    private String creationDate;

    private List<CheckFieldInfoDto> checkFieldInfo;
}

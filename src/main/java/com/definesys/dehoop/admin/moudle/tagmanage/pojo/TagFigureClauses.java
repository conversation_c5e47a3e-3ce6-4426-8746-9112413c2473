package com.definesys.dehoop.admin.moudle.tagmanage.pojo;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.Column;
import com.definesys.mpaas.query.annotation.Style;
import com.definesys.mpaas.query.annotation.Table;
import com.definesys.mpaas.query.annotation.TenantEnable;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 标签自定义字段
 * @create date 2021/6/16 16:54
 */
@Data
@Table(value = "TAG_FIGURE_CLAUSE")
@Style
@TenantEnable
public class TagFigureClauses extends XdapBasePojo {

    private String id;

    /** 字段名称 */
    @Column("FIELD")
    private String field;

    /** 选项 */
    @Column("FILED_OPTION")
    private String filedOption;

    /** 值 */
    @Column("FILED_VALUE")
    private String filedValue;

    @Column("FIELD_HIERARCHY_ID")
    private String fieldHierarchyId;

    @Column("FIELD_TAG_ID")
    private String fieldTagId;

    /** 标签ID */
    @Column("FIGURE_ID")
    private String figureId;

    /** 父节点 */
    @Column("PARENT_ID")
    private String parentId;

    /** 分类 指标、属性 */
    @Column("CLAUSES_TYPE")
    private String clausesType;

    /** 字段类型 属性、标签 */
    @Column("FIELD_TYPE")
    private String fieldType;

    @Column("NODE_LOGIC")
    private String nodeLogic;

    /** 节点类型 */
    @Column("NODE_TYPE")
    private String nodeType;

    @Column("SERIAL_NUMBER")
    private Integer serialNumber;
}

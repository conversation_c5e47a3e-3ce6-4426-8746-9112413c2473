package com.definesys.dehoop.admin.moudle.modeling.dataModel.dao;

import com.definesys.dehoop.admin.moudle.modeling.dataField.pojo.ModelingBusinessProcess;
import com.definesys.dehoop.admin.moudle.modeling.dataModel.pojo.ModelingDataModelTable;
import com.definesys.dehoop.admin.moudle.modeling.dataModel.pojo.ModelingDataModelTableRelations;
import com.definesys.mpaas.query.MpaasQueryFactory;
import com.definesys.mpaas.query.db.PageQueryResult;
import com.definesys.mpaas.query.session.MpaasSession;

import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年10月12日 15:12
 */
@Repository
public class ModelingDataModelTableDao {

    private final MpaasQueryFactory sw;

    public ModelingDataModelTableDao(MpaasQueryFactory sw) {

        this.sw = sw;
    }

    public void addModelingDataModel(List<ModelingDataModelTable> modelingDataModel) {

        if (modelingDataModel.isEmpty()) {
            return;
        }

        sw.buildQuery().doBatchInsert(modelingDataModel);
    }

    public void addModelingDataModel(ModelingDataModelTable dataModelTable) {

        sw.buildQuery().doInsert(dataModelTable);
    }

    public List<ModelingDataModelTable> getModelingDataModelByBusinessProcessId(
            String businessProcessId) {
        return sw.buildQuery().eq("MODEL_ID", businessProcessId).doQuery(ModelingDataModelTable.class);
    }

    public void deleteModelingBusinessProcess(String id) {
        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(ModelingBusinessProcess.class);
    }

    public void deleteModelingBusinessProcessByModelId(String modelId) {
        sw.buildQuery()
                .eq("MODEL_ID", modelId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(ModelingDataModelTable.class);
    }

    public void updateModelingBusinessProcess(ModelingBusinessProcess modelingBusinessProcess) {
        sw.buildQuery()
                .eq("ID", modelingBusinessProcess.getId())
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("NAME_EN", modelingBusinessProcess.getNameEn())
                .update("NAME_CN", modelingBusinessProcess.getNameCn())
                .update("NAME_EN_ABBR", modelingBusinessProcess.getNameEnAbbr())
                .update("DIRECTOR", modelingBusinessProcess.getDirector())
                .update("DESCR", modelingBusinessProcess.getDescr())
                .doUpdate(ModelingBusinessProcess.class);
    }

    public ModelingBusinessProcess getModelingBusinessProcessByName(
            ModelingBusinessProcess modelingBusinessProcess) {
        return sw.buildQuery()
                .or()
                .eq("NAME_EN", modelingBusinessProcess.getNameEn())
                .eq("NAME_EN_ABBR", modelingBusinessProcess.getNameEnAbbr())
                .eq("NAME_CN", modelingBusinessProcess.getNameCn())
                .doQueryFirst(ModelingBusinessProcess.class);
    }

    public ModelingBusinessProcess getModelingBusinessProcess(String id) {
        return sw.buildQuery().eq("ID", id).doQueryFirst(ModelingBusinessProcess.class);
    }

    public List<ModelingBusinessProcess> listModelingBusinessProcess(String searchWord) {
        return sw.buildQuery()
                .like("NAME_CN", searchWord)
                .orderBy("last_update_date", "DESC")
                .doQuery(ModelingBusinessProcess.class);
    }

    public List<ModelingBusinessProcess> queryModelingBusinessProcessByDataFieldList(
            String searchWord) {

        return sw.buildQuery().like("NAME_CN", searchWord).doQuery(ModelingBusinessProcess.class);
    }

    public List<ModelingBusinessProcess> queryModelingBusinessProcessByDataFieldId(
            List<String> DataFieldIdList) {
        return sw.buildQuery().in("DATA_FIELD", DataFieldIdList).doQuery(ModelingBusinessProcess.class);
    }

    public ModelingDataModelTable getDataModelTable(
            String modelTableId, String type, String modelId) {

        return sw.buildQuery()
                .eq("MODEL_ID", modelId)
                .eq("TYPE", type)
                .eq("MODEL_TABLE_ID", modelTableId)
                .doQueryFirst(ModelingDataModelTable.class);
    }

    public ModelingDataModelTable getDataModelTable(String type, String modelId) {

        return sw.buildQuery()
                .eq("MODEL_ID", modelId)
                .eq("TYPE", type)
                .doQueryFirst(ModelingDataModelTable.class);
    }

    public void updateModelTable(String id, Integer positionX, Integer positionY, Boolean loaded) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("LOADED", loaded)
                .update("POSITION_X", positionX)
                .update("POSITION_Y", positionY)
                .doUpdate(ModelingDataModelTable.class);
    }

    public void addBatchModelTable(List<ModelingDataModelTable> modelTables) {
        if (modelTables.isEmpty()) {
            return;
        }

        sw.buildQuery().doBatchInsert(modelTables);
    }

    public List<ModelingDataModelTable> queryModelTableByIds(List<String> tableId, String type) {

        if (tableId.isEmpty()) {

            return new ArrayList<>();
        }

        return sw.buildQuery()
                .in("MODEL_TABLE_ID", tableId)
                .eq("TYPE", type)
                .doQuery(ModelingDataModelTable.class);
    }

    public void addDataModelTableRelation(ModelingDataModelTableRelations tableRelations) {

        sw.buildQuery().doInsert(tableRelations);
    }

    public void deleteTableRelation(String srcId, String srcType, String dstId, String dstType) {

        sw.buildQuery()
                .eq("SRC_ID", srcId)
                .eq("SRC_TYPE", srcType)
                .eq("DST_ID", dstId)
                .eq("DST_TYPE", dstType)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(ModelingDataModelTableRelations.class);
    }

    public void deleteTableRelation(List<String> idList) {

        if (idList.isEmpty()) {
            return;
        }

        sw.buildQuery()
                .in("ID", idList)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(ModelingDataModelTableRelations.class);
    }

    public List<ModelingDataModelTableRelations> queryTableRelation(List<String> idList) {

        if (idList.isEmpty()) {

            return new ArrayList<>();
        }
        return sw.buildQuery().in("ID", idList).doQuery(ModelingDataModelTableRelations.class);
    }

    public void updateTableRelationName(String id, String name) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("RELATION_NAME", name)
                .doUpdate(ModelingDataModelTableRelations.class);
    }

    public void updateTableRelation(ModelingDataModelTableRelations tableRelations) {

        sw.buildQuery()
                .eq("ID", tableRelations.getId())
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doUpdate(tableRelations);
    }

    public ModelingDataModelTableRelations getTableRelationById(String id) {

        return sw.buildQuery().eq("ID", id).doQueryFirst(ModelingDataModelTableRelations.class);
    }

    public ModelingDataModelTableRelations getTableRelation(
            String srcId, String srcType, String dstId, String dstType) {

        return sw.buildQuery()
                .eq("SRC_ID", srcId)
                .eq("SRC_TYPE", srcType)
                .eq("DST_ID", dstId)
                .eq("DST_TYPE", dstType)
                .doQueryFirst(ModelingDataModelTableRelations.class);
    }

    public ModelingDataModelTableRelations getTableRelationByName(
            String srcId, String srcType, String relationName) {

        return sw.buildQuery()
                .eq("SRC_ID", srcId)
                .eq("SRC_TYPE", srcType)
                .eq("RELATION_NAME", relationName)
                .doQueryFirst(ModelingDataModelTableRelations.class);
    }

    public PageQueryResult<ModelingDataModelTableRelations> pageQueryTableRelation(
            String id, String type, Integer page, Integer pageSize) {

        return sw.buildQuery()
                .eq("SRC_ID", id)
                .eq("SRC_TYPE", type)
                .doPageQuery(page, pageSize, ModelingDataModelTableRelations.class);
    }

    /**
     * 查询来源表的关联关系
     *
     * @param srcId
     * @return
     */
    public List<ModelingDataModelTableRelations> queryTableRelation(String srcId) {

        return sw.buildQuery().in("SRC_ID", srcId).doQuery(ModelingDataModelTableRelations.class);
    }

    public void deleteModelTable(String modelTableId, String type) {

        sw.buildQuery()
                .eq("MODEL_TABLE_ID", modelTableId)
                .eq("TYPE", type)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(ModelingDataModelTable.class);
    }

    public void deleteModelTableRelationBySrc(String srcId, String srcType) {

        sw.buildQuery()
                .eq("SRC_ID", srcId)
                .eq("SRC_TYPE", srcType)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(ModelingDataModelTableRelations.class);
    }

    public void deleteModelTableRelationByDst(String dstId, String dstType) {

        sw.buildQuery()
                .eq("DST_ID", dstId)
                .eq("DST_TYPE", dstType)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(ModelingDataModelTableRelations.class);
    }
}

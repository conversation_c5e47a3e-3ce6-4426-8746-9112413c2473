package com.definesys.dehoop.admin.moudle.outlinework.ddlvisual.pojo.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/12 10:16
 * @description
 */
@Data
public class JobDdlvReqDto {

    private String id;

    private String workId;

    private String workType;

    private String optType;

    private String tableName;

    private String comments;

    private String storeType;

    private String delimiterBy;

    private String tableType;

    private String storeLocation;

    private String dataModel;

    private String barrelingType;

    private String barrelingNum;

    private Integer replicationNum;

    private String executeId;

    private String script;

    private String name;

    private String flowId;

    private String parentId;

    private List<JobDdlvFieldsInfoDto> fields;
}

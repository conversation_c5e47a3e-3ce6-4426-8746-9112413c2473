package com.definesys.dehoop.admin.moudle.modeling.dataIndicator.pojo.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ModelingDataIndicatorTreeDto {

    /** 原子指标 */
    private List<ModelingDataIndicatorNodeDto> atomIndicator;

    /** 统计条件 */
    private List<ModelingDataIndicatorNodeDto> statisticalCondition;

    /** 统计周期 */
    private List<ModelingDataIndicatorNodeDto> statisticalPeriod;

    /** 派生指标 */
    private List<ModelingDataIndicatorNodeDto> deriveIndicator;
}

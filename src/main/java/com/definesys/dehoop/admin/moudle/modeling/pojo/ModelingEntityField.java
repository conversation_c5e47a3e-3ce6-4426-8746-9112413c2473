package com.definesys.dehoop.admin.moudle.modeling.pojo;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.Column;
import com.definesys.mpaas.query.annotation.Style;
import com.definesys.mpaas.query.annotation.Table;
import com.definesys.mpaas.query.annotation.TenantEnable;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @create date 2021/6/18 14:56
 */
@Table(value = "MODELING_ENTITY_FIELD")
@Style
@Data
@TenantEnable
public class ModelingEntityField extends XdapBasePojo {

    private String id;

    @Column("FIELD_NAME")
    private String fieldName;

    @Column("FIELD_TYPE")
    private String fieldType;

    @Column("MODULE_TYPE")
    private String moduleType;

    @Column("LOGIC_NAME")
    private String logicName;

    private Integer length;

    @Column("PRECISIONS")
    private Integer precisions;

    @Column("SCALES")
    private Integer scales;

    @Column("COMMENTS")
    private String comments;

    @Column("IS_PRIMARY_KEY")
    private Boolean isPrimaryKey = false;

    @Column("IS_NOT_NULL")
    private Boolean isNotNull = false;

    @Column("ENTITY_ID")
    private String entityId;

    @Column("SEQUENCE_NUMBER")
    private Integer sequenceNumber;

    @Column("FIELD_CREATED")
    private Boolean fieldCreated;

    @Column("PUBLISH_STATE")
    private String publishState;

    /** 模型关系、模型类型 */
    @Column("MODEL_RELATIONSHIP")
    private String modelRelationship;

    @Column("MEASURE_UNIT_ID")
    private String measureUnitId;

    @Column("IS_SYS")
    private Boolean isSys;

    @Column("IS_PARTITION_FIELD")
    private Boolean isPartitionField;

    @Column("FIELD_STANDARD_ID")
    private String fieldStandardId;

    @Column("FIELD_STANDARD_NAME")
    private String fieldStandardName;

    public Integer getPrecision() {
        return precisions;
    }

    public void setPrecision(Integer precisions) {
        this.precisions = precisions;
    }

    public Integer getScale() {
        return scales;
    }

    public void setScale(Integer scales) {
        this.scales = scales;
    }

    public String getComment() {
        return comments;
    }

    public void setComment(String comments) {
        this.comments = comments;
    }

    public Boolean getPrimaryKey() {
        return isPrimaryKey;
    }

    public void setPrimaryKey(Boolean primaryKey) {
        isPrimaryKey = primaryKey != null && primaryKey;
    }

    public void setIsPrimaryKey(Boolean primaryKey) {
        isPrimaryKey = primaryKey != null && primaryKey;
    }

    public Boolean getNotNull() {
        return isNotNull;
    }

    public void setNotNull(Boolean notNull) {
        isNotNull = notNull != null && notNull;
    }

    public void setIsNotNull(Boolean notNull) {
        isNotNull = notNull != null && notNull;
    }

    public Boolean getFieldCreated() {
        return fieldCreated;
    }

    public void setFieldCreated(Boolean fieldCreated) {
        this.fieldCreated = fieldCreated != null && fieldCreated;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ModelingEntityField that = (ModelingEntityField) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}

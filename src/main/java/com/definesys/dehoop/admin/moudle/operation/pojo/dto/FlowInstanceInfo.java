package com.definesys.dehoop.admin.moudle.operation.pojo.dto;

import com.definesys.dehoop.api.serializer.DateDeserializer;
import com.definesys.dehoop.api.serializer.DateSerializer;
import com.definesys.mpaas.query.annotation.Column;
import com.definesys.mpaas.query.json.MpaasDateTimeDeserializer;
import com.definesys.mpaas.query.json.MpaasDateTimeSerializer;

import lombok.Data;

import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@Data
public class FlowInstanceInfo {

    @Column("flowId")
    private String flowId;

    @Column("flowName")
    private String flowName;

    private String directorName;

    @JsonDeserialize(using = MpaasDateTimeDeserializer.class)
    @JsonSerialize(using = MpaasDateTimeSerializer.class)
    @Column("startDate")
    private Date startDate;

    @JsonDeserialize(using = MpaasDateTimeDeserializer.class)
    @JsonSerialize(using = MpaasDateTimeSerializer.class)
    @Column("endDate")
    private Date endDate;

    private String director;

    @JsonDeserialize(using = MpaasDateTimeDeserializer.class)
    @JsonSerialize(using = MpaasDateTimeSerializer.class)
    @Column("PLAN_DATE")
    private Date planDate;

    private String state;

    private String stateMeaning;

    @Column("projectId")
    private String projectId;

    @Column("flowInstanceId")
    private String flowInstanceId;

    @JsonDeserialize(using = DateDeserializer.class)
    @JsonSerialize(using = DateSerializer.class)
    @Column("executeDate")
    private Date executeDate;
}

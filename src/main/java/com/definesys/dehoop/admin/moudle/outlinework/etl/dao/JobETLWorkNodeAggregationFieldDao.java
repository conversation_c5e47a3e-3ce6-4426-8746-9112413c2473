package com.definesys.dehoop.admin.moudle.outlinework.etl.dao;

import com.definesys.dehoop.admin.moudle.outlinework.etl.pojo.entity.JobETLWorkNodeAggregationField;
import com.definesys.mpaas.query.MpaasQueryFactory;
import com.definesys.mpaas.query.session.MpaasSession;

import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年11月26日 18:17
 */
@Repository
public class JobETLWorkNodeAggregationFieldDao {

    private final MpaasQueryFactory sw;

    public JobETLWorkNodeAggregationFieldDao(MpaasQueryFactory sw) {
        this.sw = sw;
    }

    public void addJobETLWorkNodeAggregationField(
            JobETLWorkNodeAggregationField jobETLWorkNodeAggregationField) {

        sw.buildQuery().doInsert(jobETLWorkNodeAggregationField);
    }

    public void addJobETLWorkNodeAggregationField(
            List<JobETLWorkNodeAggregationField> jobETLWorkNodeAggregationField) {

        sw.buildQuery().doInsert(jobETLWorkNodeAggregationField);
    }

    public void deleteJobETLWorkNodeAggregationFieldByNodeId(String nodeId) {
        sw.buildQuery()
                .eq("NODE_ID", nodeId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(JobETLWorkNodeAggregationField.class);
    }

    public void deleteJobETLWorkNodeAggregationField(List<String> idList) {
        sw.buildQuery()
                .in("ID", idList)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(JobETLWorkNodeAggregationField.class);
    }

    public void updateJobETLWorkNodeAggregationField(
            JobETLWorkNodeAggregationField jobETLWorkNodeAggregationField) {
        sw.buildQuery()
                .eq("id", jobETLWorkNodeAggregationField.getId())
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("FIELD_NAME", jobETLWorkNodeAggregationField.getFieldName())
                .update("FIELD_TYPE", jobETLWorkNodeAggregationField.getFieldType())
                .update("DESCR", jobETLWorkNodeAggregationField.getDescr())
                .update("SOURCE_NODE_ID", jobETLWorkNodeAggregationField.getSourceNodeId())
                .update("SOURCE_FIELD_NAME", jobETLWorkNodeAggregationField.getSourceFieldName())
                .update("IS_GROUP_FIELD", jobETLWorkNodeAggregationField.getIsGroupField())
                .update("AGGREGATE_FUNCTION", jobETLWorkNodeAggregationField.getAggregateFunction())
                .update("SEQUENCE_NUMBER", jobETLWorkNodeAggregationField.getSequenceNumber())
                .doUpdate(JobETLWorkNodeAggregationField.class);
    }

    /**
     * 批量查询
     *
     * @param nodeId 节点ID
     * @return 字段信息列表
     */
    public List<JobETLWorkNodeAggregationField> queryJobETLWorkNodeAggregationFieldByNodeId(
            String nodeId) {
        return sw.buildQuery().eq("node_id", nodeId).doQuery(JobETLWorkNodeAggregationField.class);
    }
}

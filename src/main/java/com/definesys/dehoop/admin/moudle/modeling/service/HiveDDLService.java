package com.definesys.dehoop.admin.moudle.modeling.service;

import com.definesys.dehoop.admin.moudle.calengine.pojo.dto.EngineConnectInfo;
import com.definesys.dehoop.admin.moudle.connect.pojo.ConnectInfo;
import com.definesys.dehoop.admin.moudle.connect.pojo.TableColumn;
import com.definesys.dehoop.admin.moudle.connect.pojo.TableInfo;
import com.definesys.dehoop.admin.moudle.connect.service.TableService;
import com.definesys.dehoop.admin.moudle.modeling.pojo.HiveDDL;
import com.definesys.dehoop.admin.moudle.modeling.pojo.TableFieldPojo;
import com.definesys.dehoop.api.constant.DehoopConstant;
import com.definesys.dehoop.api.enums.DataBaseType;
import com.definesys.dehoop.api.exception.DataSourceExceptionEnum;
import com.definesys.dehoop.api.utils.DataBaseUtils;

import com.xdap.motor.exception.XDapBizException;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: hive ddl
 * @create date 2021/6/21 12:24
 */
@Slf4j
@Service
public class HiveDDLService {

    private final TableService tableService;

    private final ModelingService modelingService;

    public HiveDDLService(TableService tableService, ModelingService modelingService) {
        this.tableService = tableService;
        this.modelingService = modelingService;
    }

    public static String[] generateHiveDDL(HiveDDL hiveDDL, TableFieldPojo... fieldDtoList) {

        switch (hiveDDL.getType()) {
            case CREATE:
                return new String[] {generateCreateHiveDDL(hiveDDL, fieldDtoList)};
            case ALTER:
                // 主键更新 问题待处理
                return generateAlterHiveDDL(hiveDDL, fieldDtoList).toArray(new String[] {});
        }
        return new String[] {};
    }

    public static String generateCreateHiveDDL(HiveDDL hiveDDL, TableFieldPojo... fieldDtoList) {
        String createSQL =
                "CREATE TABLE %table_name% ( %fieldSQL% ) PARTITIONED BY (%partitionField%) ROW FORMAT DELIMITED FIELDS TERMINATED BY '\\001' MAP KEYS TERMINATED BY '/t' STORED AS TEXTFILE";

        String fieldSQL =
                Stream.of(fieldDtoList)
                        .peek(
                                field -> {
                                    if ("int".equalsIgnoreCase(field.getType())) field.setType(field.getType());
                                    else if (field.getLength() != null)
                                        field.setType(field.getType() + "(" + field.getLength() + ")");
                                    else if (field.getPrecision() != null)
                                        field.setType(
                                                field.getType()
                                                        + "("
                                                        + field.getPrecision()
                                                        + ","
                                                        + field.getScale()
                                                        + ")");
                                    else field.setType(field.getType());
                                })
                        .filter(field -> field.getIsPartitionField() == null || !field.getIsPartitionField())
                        .map(
                                field ->
                                        "`"
                                                + field.getName()
                                                + "`"
                                                + " "
                                                + field.getType()
                                                + " "
                                                //   + (field.getIsPrimaryKey()!=null &&
                                                // field.getIsPrimaryKey()   ? " PRIMARY KEY" : "")
                                                //   + (field.getIsNotNull() != null &&
                                                // field.getIsNotNull() ? " NOT NULL" : "")
                                                + (field.getComment() != null
                                                        ? " COMMENT '" + field.getComment() + "'"
                                                        : ""))
                        .collect(Collectors.joining(","));

        String partitionFieldSQL =
                Stream.of(fieldDtoList)
                        .filter(field -> field.getIsPartitionField() != null && field.getIsPartitionField())
                        .map(field -> field.getName() + " string")
                        .collect(Collectors.joining(","));

        return createSQL
                .replaceAll("%table_name%", hiveDDL.getTableName())
                .replaceAll("%fieldSQL%", fieldSQL)
                .replaceAll("%partitionField%", partitionFieldSQL);
    }

    public static List<String> generateAlterHiveDDL(HiveDDL hiveDDL, TableFieldPojo... fieldDtoList) {
        String alterSQLTemp = "alter table %table_name% add columns(%fieldSQL%) ";

        List<String> fieldSQL =
                Stream.of(fieldDtoList)
                        .peek(
                                field -> {
                                    if ("INT".equalsIgnoreCase(field.getType())) field.setType(field.getType());
                                    else if (field.getLength() != null)
                                        field.setType(field.getType() + "(" + field.getLength() + ")");
                                    else if (field.getPrecision() != null)
                                        field.setType(
                                                field.getType()
                                                        + "("
                                                        + field.getPrecision()
                                                        + ","
                                                        + field.getScale()
                                                        + ")");
                                    else field.setType(field.getType());
                                })
                        .map(
                                field ->
                                        "`"
                                                + field.getName()
                                                + "`"
                                                + " "
                                                + field.getType()
                                                + " "
                                                + (field.getComment() != null
                                                        ? " COMMENT '" + field.getComment() + "'"
                                                        : ""))
                        .collect(Collectors.toList());

        //        Optional<TableFieldPojo> primaryKeyField =
        //
        // Stream.of(fieldDtoList).filter(TableFieldPojo::getIsPrimaryKey).findFirst();
        //        String currentPrimaryKey =
        // primaryKeyField.map(TableFieldPojo::getName).orElse(null);
        /*if(primaryKeyField.isPresent())
        fieldSQL = fieldSQL+ " , primary key(" + currentPrimaryKey + ") disable novalidate " ;*/

        List<String> alterSQLList = new ArrayList<>();
        fieldSQL.forEach(
                v ->
                        alterSQLList.add(
                                alterSQLTemp
                                        .replaceAll("%table_name%", hiveDDL.getTableName())
                                        .replaceAll("%fieldSQL%", v)));
        return alterSQLList;
    }

    public static ConnectInfo getModuleConnectInfo(
            EngineConnectInfo engineConnectInfo, String dbName) {

        ConnectInfo hiveServer = new ConnectInfo();
        BeanUtils.copyProperties(engineConnectInfo, hiveServer);
        String url = DataBaseUtils.getJdbcUrl(engineConnectInfo.getJdbcUrl(), dbName);
        hiveServer.setJdbcUrl(url);
        hiveServer.setDbType("Hive");
        hiveServer.setDataBaseName(dbName);
        hiveServer.setDataBaseType(DataBaseType.Hive);
        return hiveServer;
    }

    /** 获取表信息接口 */
    public TableInfo getTableInfo(ConnectInfo connectInfo) {

        TableInfo result = new TableInfo();
        TableColumn tableColumns = tableService.getTableColumns(connectInfo);
        result.setTableColumnInfos(tableColumns.getTableColumnInfos());
        result.setPartitionColumnInfos(tableColumns.getPartitionColumnInfos());

        return result;
    }

    public static Connection getConnection(ConnectInfo connectInfo) {
        Connection connection = null;
        try {
            Class.forName("org.apache.hive.jdbc.HiveDriver");
            DriverManager.setLoginTimeout(DehoopConstant.hiveLoginTimeout);
            connection =
                    DriverManager.getConnection(
                            connectInfo.getJdbcUrl(), connectInfo.getUsername(), connectInfo.getPasswd());

        } catch (Exception e) {
            throw new XDapBizException(DataSourceExceptionEnum.GET_TABLE_INFO_ERROR);
        }
        return connection;
    }

    /**
     * 关闭
     *
     * @param connection 句柄
     */
    public static void closeConnection(Connection connection) {
        try {
            if (connection != null) connection.close();

        } catch (SQLException throwables) {
            log.error(throwables.getMessage());
        }
    }

    public void dropHiveTagTable(String entityId, String... tableNames) {
        ConnectInfo connectInfo = modelingService.getModuleConnectInfoProd(entityId);
        dropHiveTagTable(connectInfo, tableNames);
    }

    /** 删除生成的hive表 */
    public void dropHiveTagTable(ConnectInfo connectInfo, String... tableNames) {

        Connection connection = null;
        try {
            connection = HiveDDLService.getConnection(connectInfo);
            Statement statement = connection.createStatement();
            for (String tableName : tableNames) statement.execute("drop table if exists " + tableName);

        } catch (SQLException throwables) {
            log.error(throwables.getMessage());
        } finally {
            HiveDDLService.closeConnection(connection);
        }
    }
}

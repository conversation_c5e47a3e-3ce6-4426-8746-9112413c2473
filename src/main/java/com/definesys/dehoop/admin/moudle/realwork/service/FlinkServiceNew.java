package com.definesys.dehoop.admin.moudle.realwork.service;

import com.definesys.dehoop.admin.moudle.calengine.pojo.dto.EngineInfo;
import com.definesys.dehoop.admin.moudle.calengine.service.CalEngineService;
import com.definesys.dehoop.admin.moudle.connect.pojo.ConnectInfo;
import com.definesys.dehoop.admin.moudle.connect.pojo.TableInfo;
import com.definesys.dehoop.admin.moudle.connect.service.DataBaseFactory;
import com.definesys.dehoop.admin.moudle.connect.service.HiveServiceNew;
import com.definesys.dehoop.admin.moudle.datasource.dao.DatasourceDao;
import com.definesys.dehoop.admin.moudle.datasource.pojo.DatasourceEntity;
import com.definesys.dehoop.admin.moudle.executor.units.DehoopExpressionParser;
import com.definesys.dehoop.admin.moudle.log.dao.LogDao;
import com.definesys.dehoop.admin.moudle.log.pojo.JobWorkLogs;
import com.definesys.dehoop.admin.moudle.log.service.LogServiceNew;
import com.definesys.dehoop.admin.moudle.monitor.realWork.event.RealWorkRetryEvent;
import com.definesys.dehoop.admin.moudle.monitor.yarn.YarnApplicationService;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobWorkScripts;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.PartitionValue;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.TableColumnInfo;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.dto.*;
import com.definesys.dehoop.admin.moudle.project.pojo.dto.EnvCalConfigInfo;
import com.definesys.dehoop.admin.moudle.project.service.EnvConfigService;
import com.definesys.dehoop.admin.moudle.realwork.dao.RealWorkDao;
import com.definesys.dehoop.admin.moudle.realwork.pojo.JobRealWorkNodeConfigs;
import com.definesys.dehoop.admin.moudle.realwork.pojo.JobRealWorks;
import com.definesys.dehoop.admin.moudle.realwork.pojo.RealWorkReqDto;
import com.definesys.dehoop.admin.moudle.realwork.pojo.dto.*;
import com.definesys.dehoop.admin.moudle.schedule.dao.ScheduleDao;
import com.definesys.dehoop.admin.moudle.schedule.pojo.SchWorkInstances;
import com.definesys.dehoop.admin.moudle.schedule.pojo.SchWorkVersions;
import com.definesys.dehoop.api.constant.RealWorkConstants;
import com.definesys.dehoop.api.constant.WorkRunStatusConstants;
import com.definesys.dehoop.api.constant.WorkTypeConstants;
import com.definesys.dehoop.api.entity.JdbcInfo;
import com.definesys.dehoop.api.enums.DataBaseType;
import com.definesys.dehoop.api.exception.CalEngineExceptionEnum;
import com.definesys.dehoop.api.exception.OperationExceptionEnum;
import com.definesys.dehoop.api.exception.RealWorkExceptionEnum;
import com.definesys.dehoop.api.properties.ChunJunProperties;
import com.definesys.dehoop.api.utils.AesUtils;
import com.definesys.dehoop.api.utils.EncryptUtils;
import com.definesys.dehoop.api.utils.JdbcUtils;
import com.definesys.mpaas.common.adapter.UserProfile;
import com.definesys.mpaas.query.session.MpaasSession;

import com.xdap.motor.exception.CommonBizExceptionEnum;
import com.xdap.motor.exception.XDapBizException;
import com.xdap.motor.utils.Assert;
import com.xdap.motor.utils.StringUtils;

import com.dtstack.dtcenter.loader.dto.MqttConnectInfo;

import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import org.apache.logging.log4j.util.Strings;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import groovy.json.StringEscapeUtils;

import static com.definesys.dehoop.admin.moudle.realwork.service.RealWorkService.*;
import static java.util.regex.Pattern.compile;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/** flink 服务层 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlinkServiceNew {

    private final RealWorkDao realWorkDao;

    private final DatasourceDao datasourceDao;

    private final ScheduleDao scheduleDao;

    private final LogDao logDao;

    private final HiveServiceNew hiveServiceNew;

    private final LogServiceNew logServiceNew;

    private final EnvConfigService envConfigService;

    private final CalEngineService calEngineService;

    private final ChunJunProperties chunJunProperties;

    private final YarnApplicationService yarnApplicationService;

    private final ApplicationContext applicationContext;

    /** 测试环境执行flink作业 */
    public void executeDevRealWork(String realWorkId, String executeId) {

        // 传入值校验
        Assert.isNotEmpty(realWorkId, CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        Assert.isNotEmpty(executeId, CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        // 检查实时作业是否存在
        JobRealWorks realWork = realWorkDao.getRealWork(realWorkId);
        if (realWork == null) {
            throw new XDapBizException(RealWorkExceptionEnum.REAL_WORK_IS_NOT_EXIST);
        }

        // 判断作业运行状态
        if (WorkRunStatusConstants.RUN_LOADING.equals(realWork.getRunningStatus())
                || WorkRunStatusConstants.RUNNING.equals(realWork.getRunningStatus())) {
            throw new XDapBizException(RealWorkExceptionEnum.REAL_WORK_IS_RUNNING);
        }

        // 获取计算引擎配置信息
        EnvCalConfigInfo envCalConfig = envConfigService.getEnvCalConfig(realWork.getEnvId());
        if (!calEngineService.checkChunJunConfig(envCalConfig.getEngineInfo())) {
            throw new XDapBizException(CalEngineExceptionEnum.CAL_ENGINE_INFO_NOT_COMPLETE);
        }

        // 检查是否是空节点
        List<String> nodeIdList = JSON.parseArray(realWork.getFlowConfig(), String.class);
        if (nodeIdList.isEmpty()) {
            throw new XDapBizException(RealWorkExceptionEnum.REAL_NODE_IS_EMPTY);
        }

        logServiceNew.addLog(realWorkId, executeId, "", WorkRunStatusConstants.DOING);
        realWorkDao.updateRealWorkRunningStatusAndExecuteId(
                realWorkId, WorkRunStatusConstants.RUN_LOADING, executeId);

        // 获取节点信息
        List<JobRealWorkNodeConfigs> jobRealWorkNodeConfigs =
                realWorkDao.getRealWorkNodeList(nodeIdList);

        // 解析首尾节点字段信息
        Map<String, JobRealWorkNodeConfigs> nodesConfigMap = new HashMap<>();
        jobRealWorkNodeConfigs.forEach(
                e -> {
                    nodesConfigMap.put(e.getNodeId(), e);
                });

        JobRealWorkNodeConfigs startNode = nodesConfigMap.get(nodeIdList.get(0));
        JobRealWorkNodeConfigs endNode = nodesConfigMap.get(nodeIdList.get(nodeIdList.size() - 1));

        try {
            realWorkDao.updateRealWorkRunningStatus(realWorkId, WorkRunStatusConstants.RUNNING);
            executeRealWork(
                    executeId,
                    startNode,
                    endNode,
                    jobRealWorkNodeConfigs,
                    envCalConfig.getEngineInfo(),
                    realWork.getName(),
                    null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            logServiceNew.addLog(realWorkId, executeId, e.getMessage(), WorkRunStatusConstants.OVER);
            realWorkDao.updateRealWorkRunningStatus(realWorkId, WorkRunStatusConstants.FAILED);
        }
    }

    /** 测试环境执行flinkSql作业 */
    public void executeDevFlinkSqlWork(RealWorkReqDto realWorkReqDto) {

        Assert.isNotEmpty(realWorkReqDto.getId(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        Assert.isNotEmpty(realWorkReqDto.getExecuteId(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        String realWorkId = realWorkReqDto.getId();
        String executeId = realWorkReqDto.getExecuteId();

        // 检查实时作业是否存在
        JobRealWorks realWork = realWorkDao.getRealWork(realWorkId);
        if (realWork == null) {
            throw new XDapBizException(RealWorkExceptionEnum.REAL_WORK_IS_NOT_EXIST);
        }

        // 判断作业运行状态
        if (WorkRunStatusConstants.RUN_LOADING.equals(realWork.getRunningStatus())
                || WorkRunStatusConstants.RUNNING.equals(realWork.getRunningStatus())) {
            throw new XDapBizException(RealWorkExceptionEnum.REAL_WORK_IS_RUNNING);
        }

        // 获取计算引擎配置信息
        EnvCalConfigInfo envCalConfig = envConfigService.getEnvCalConfig(realWork.getEnvId());
        if (!calEngineService.checkChunJunConfig(envCalConfig.getEngineInfo())) {
            throw new XDapBizException(CalEngineExceptionEnum.CAL_ENGINE_INFO_NOT_COMPLETE);
        }

        // 检测flinkSql运行sql是否为空
        if (StringUtils.isBlank(realWorkReqDto.getWorkScript())) {
            throw new XDapBizException(RealWorkExceptionEnum.FLINK_SQL_IS_NULL);
        }

        // flinkSql进入启动运行中状态
        logServiceNew.addLog(realWorkId, executeId, "", WorkRunStatusConstants.DOING);
        realWorkDao.updateRealWorkRunningStatusAndExecuteId(
                realWorkId, WorkRunStatusConstants.RUN_LOADING, executeId);

        try {
            realWorkDao.updateRealWorkRunningStatus(realWorkId, WorkRunStatusConstants.RUNNING);
            executeFlinkSql(
                    executeId,
                    realWorkReqDto.getWorkScript(),
                    realWork.getName(),
                    envCalConfig.getEngineInfo());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            logServiceNew.addLog(realWorkId, executeId, e.getMessage(), WorkRunStatusConstants.OVER);
            realWorkDao.updateRealWorkRunningStatus(realWorkId, WorkRunStatusConstants.FAILED);
        }
    }

    @Async
    public void executeProdRealWork(
            SchWorkVersions workVersion,
            String workInstanceId,
            String executeId,
            String checkpointPath,
            UserProfile userProfile) {

        MpaasSession.setUserProfile(userProfile);

        // 更新实例状态为启动运行中
        scheduleDao.updateWorkInstanceStateAndStartDate(
                workInstanceId, WorkRunStatusConstants.RUN_LOADING, new Date());

        // 自动重试起始次数， -1代表不需要自动重试， 0代表需要自动重试
        int retryNum = -1;
        JobRealWorks realWork = null;
        try {

            // 获取实时作业的版本
            if (workVersion == null) {
                throw new XDapBizException(OperationExceptionEnum.WORK_VERSION_NOT_EXIST);
            }

            // 获取实时作业信息
            realWork = JSON.parseObject(workVersion.getRealWorkConfig(), JobRealWorks.class);
            if (realWork == null) {
                throw new XDapBizException(OperationExceptionEnum.WORK_VERSION_ERROR);
            }

            if (realWork.getAutoRetryState() != null
                    && realWork.getAutoRetryState()
                    && realWork.getRetryNum() > 0) {
                retryNum = 0;
            }
            logServiceNew.addLogWithRetryNum(
                    realWork.getId(), executeId, "", WorkRunStatusConstants.DOING, retryNum);

            // 获取计算引擎配置信息 (实时作业发布后依然使用开发环境的计算引擎)
            EnvCalConfigInfo envCalConfig = envConfigService.getEnvCalConfig(realWork.getEnvId());
            if (!calEngineService.checkChunJunConfig(envCalConfig.getEngineInfo())) {
                throw new XDapBizException(CalEngineExceptionEnum.CAL_ENGINE_INFO_NOT_COMPLETE);
            }

            // 查询实时作业每个节点
            List<String> nodeIdList = JSON.parseArray(realWork.getFlowConfig(), String.class);
            if (nodeIdList.isEmpty()) {
                throw new XDapBizException(RealWorkExceptionEnum.REAL_NODE_IS_EMPTY);
            }

            // 解析节点
            List<JobRealWorkNodeConfigs> jobRealWorkNodeConfigs =
                    JSON.parseArray(workVersion.getRealNodesConfig(), JobRealWorkNodeConfigs.class);
            Map<String, JobRealWorkNodeConfigs> nodesConfigMap = new HashMap<>();
            jobRealWorkNodeConfigs.forEach(
                    e -> {
                        nodesConfigMap.put(e.getNodeId(), e);
                    });

            JobRealWorkNodeConfigs startNode = nodesConfigMap.get(nodeIdList.get(0));
            JobRealWorkNodeConfigs endNode = nodesConfigMap.get(nodeIdList.get(nodeIdList.size() - 1));

            // 执行实时作业
            scheduleDao.updateWorkInstanceState(workInstanceId, WorkRunStatusConstants.RUNNING);
            executeRealWork(
                    executeId,
                    startNode,
                    endNode,
                    jobRealWorkNodeConfigs,
                    envCalConfig.getEngineInfo(),
                    realWork.getName(),
                    checkpointPath);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            logServiceNew.addLog(
                    realWork == null ? null : realWork.getId(),
                    executeId,
                    e.getMessage(),
                    WorkRunStatusConstants.OVER);

            // 未配置重试机制时retryNum = -1
            if (retryNum == -1) {
                scheduleDao.endRealWorkInstanceByExecuteId(executeId, WorkRunStatusConstants.FAILED);
            } else {
                log.error("实时作业执行发布失败, executeId: {}, 开始自动重试", executeId);
                RealWorkRetryEvent realWorkRetryEvent =
                        new RealWorkRetryEvent(this, executeId, WorkRunStatusConstants.FAILED);
                applicationContext.publishEvent(realWorkRetryEvent);
            }
        }
    }

    @Async
    public void executeProdFlinkSqlWork(
            String versionId, String workInstanceId, String executeId, UserProfile userProfile) {

        MpaasSession.setUserProfile(userProfile);

        // 更新实例状态为启动运行中
        scheduleDao.updateWorkInstanceStateAndStartDate(
                workInstanceId, WorkRunStatusConstants.RUN_LOADING, new Date());

        JobRealWorks realWork = null;
        try {

            // 获取实时作业的版本
            SchWorkVersions workVersion = scheduleDao.getWorkVersion(versionId);
            if (workVersion == null) {
                throw new XDapBizException(OperationExceptionEnum.WORK_VERSION_NOT_EXIST);
            }

            // 获取实时作业信息
            realWork = JSON.parseObject(workVersion.getRealWorkConfig(), JobRealWorks.class);
            if (realWork == null) {
                throw new XDapBizException(OperationExceptionEnum.WORK_VERSION_ERROR);
            }

            // 获取计算引擎配置信息 (实时作业发布后依然使用开发环境的计算引擎)
            EnvCalConfigInfo envCalConfig = envConfigService.getEnvCalConfig(realWork.getEnvId());
            if (!calEngineService.checkChunJunConfig(envCalConfig.getEngineInfo())) {
                throw new XDapBizException(CalEngineExceptionEnum.CAL_ENGINE_INFO_NOT_COMPLETE);
            }

            // 查询实时作业执行脚本
            JobWorkScripts jobWorkScripts =
                    JSON.parseObject(workVersion.getWorkScripts(), JobWorkScripts.class);
            if (jobWorkScripts == null || StringUtils.isBlank(jobWorkScripts.getContent())) {
                throw new XDapBizException(RealWorkExceptionEnum.FLINK_SQL_IS_NULL);
            }

            logServiceNew.addLog(realWork.getId(), executeId, "", WorkRunStatusConstants.DOING);
            scheduleDao.updateWorkInstanceState(workInstanceId, WorkRunStatusConstants.RUNNING);

            // 执行flinkSql
            executeFlinkSql(
                    executeId, jobWorkScripts.getContent(), realWork.getName(), envCalConfig.getEngineInfo());

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            logServiceNew.addLog(
                    realWork == null ? null : realWork.getId(),
                    executeId,
                    e.getMessage(),
                    WorkRunStatusConstants.OVER);
            scheduleDao.updateWorkInstanceStateAndEndDate(
                    workInstanceId, WorkRunStatusConstants.FAILED, new Date());
        }
    }

    // 实际执行实时作业
    private void executeRealWork(
            String executeId,
            JobRealWorkNodeConfigs startNode,
            JobRealWorkNodeConfigs endNode,
            List<JobRealWorkNodeConfigs> jobRealWorkNodeConfigs,
            EngineInfo engineInfo,
            String realWorkName,
            String checkpointPath) {

        // 初始化基础参数
        StringBuilder source = new StringBuilder();
        StringBuilder sink = new StringBuilder();

        // 过滤条件
        StringBuilder dataFilter = new StringBuilder();

        // 字符串替换
        StringBuilder dataReplace = new StringBuilder();

        InputTableInfo inputTableInfo = JSON.parseObject(startNode.getConfig(), InputTableInfo.class);
        OutputTableInfo outputTableInfo = JSON.parseObject(endNode.getConfig(), OutputTableInfo.class);
        Map<String, String> columnMapping = new HashMap<>();
        outputTableInfo
                .getColumnMapping()
                .forEach(
                        e -> {
                            e.setFromColumnType(e.getFromColumnType());
                            columnMapping.put(e.getFromColumn(), e.getFromColumnType());
                        });
        List<String> usedColumn =
                outputTableInfo.getColumnMapping().stream()
                        .map(ColumnMapping::getFromColumn)
                        .collect(Collectors.toList());

        // 记录开始节点
        String type;
        if ("INPUT_KAFKA".equals(startNode.getType())
                || "OUTPUT_KAFKA".equals(endNode.getType())
                || "INPUT_MQTT".equals(startNode.getType())
                || "OUTPUT_MQTT".equals(endNode.getType())) {
            type = "SQL";
        } else {
            type = "JSON";
        }
        String finalType = type;
        Map<String, String> hiveColMapping = new HashMap<>();
        StringBuilder sqlFiled = new StringBuilder();

        ChunJunContentDto content = new ChunJunContentDto();

        ChunJunReaderParameterDto binlogParameter = new ChunJunReaderParameterDto();

        // 遍历执行节点配置
        for (JobRealWorkNodeConfigs node : jobRealWorkNodeConfigs) {

            // 获取当前节点
            if (node.getConfig() == null || node.getConfig().isEmpty()) {
                throw new XDapBizException(RealWorkExceptionEnum.REAL_NODE_CONFIG_IS_ERROR);
            }

            switch (node.getType()) {
                case "INPUT_KAFKA":

                    // 解析字段信息
                    List<String> fromColumns = new ArrayList<>();
                    outputTableInfo
                            .getColumnMapping()
                            .forEach(
                                    e -> {
                                        fromColumns.add(
                                                translateKeyWork(e.getFromColumn()) + ' ' + e.getFromColumnType());
                                    });
                    String fromColumnsStr = Strings.join(fromColumns, ',');
                    // 拼接链接信息
                    source
                            .append("CREATE TABLE source ")
                            .append(" (\n ")
                            .append(fromColumnsStr)
                            .append(" ) WITH ( \n")
                            .append("'connector' = 'kafka-x'\n")
                            .append(",'scan.startup.mode'='")
                            .append(inputTableInfo.getConsumptionPoint().toLowerCase());

                    if ("specific-offsets".equalsIgnoreCase(inputTableInfo.getConsumptionPoint())) {
                        source
                                .append("'\n,'scan.startup.specific-offsets'='")
                                .append(inputTableInfo.getSpecifiedOffset());

                    } else if ("timestamp".equalsIgnoreCase(inputTableInfo.getConsumptionPoint())) {
                        source
                                .append("'\n,'scan.startup.timestamp-millis'='")
                                .append(inputTableInfo.getSpecifiedTimestamp());
                    }

                    source
                            .append("'\n,'properties.group.id'='dehoopGroup'")
                            .append("\n,'topic'='")
                            .append(inputTableInfo.getKafkaTopic())
                            .append("'\n,'properties.bootstrap.servers'='")
                            .append(inputTableInfo.getKafkaServer())
                            // 并行度 默认null
                            .append("'\n,'scan.parallelism'= '1'\n");
                    // 支持多类型数据格式
                    if (RealWorkConstants.REAL_WORK_KAFKA_CSV.equals(inputTableInfo.getDataFormat())) {
                        source.append("\n,'format'='csv'\n,'csv.ignore-parse-errors' = 'true'\n");
                    } else if (RealWorkConstants.REAL_WORK_CANAL_JSON.equals(
                            inputTableInfo.getDataFormat())) {
                        source.append("\n,'format'='canal-json'\n, 'canal-json.ignore-parse-errors'='true'\n");
                    } else {
                        source.append(
                                "\n,'format'='json'\n,'json.fail-on-missing-field'='false'\n,'json.ignore-parse-errors'='true'\n");
                    }
                    source.append(");");
                    break;
                case "INPUT_MQTT":

                    // 解析字段信息
                    List<String> mqttFromColumns = new ArrayList<>();
                    outputTableInfo
                            .getColumnMapping()
                            .forEach(
                                    e -> {
                                        mqttFromColumns.add(
                                                translateKeyWork(e.getFromColumn())
                                                        + ' '
                                                        + compile("\\(.*?\\)")
                                                                .matcher(e.getFromColumnType())
                                                                .replaceAll("")
                                                                .toUpperCase());
                                    });
                    String MqttfromColumnsStr = Strings.join(mqttFromColumns, ',');
                    // 获取数据源
                    DatasourceEntity mqttDatasource =
                            datasourceDao.getDatasourceById(inputTableInfo.getDataSourceId());
                    MqttConnectInfo mqttConnectInfo =
                            JSON.parseObject(mqttDatasource.getConnectInfo(), MqttConnectInfo.class);

                    // 拼接链接信息
                    source
                            .append("CREATE TABLE source ")
                            .append(" (\n ")
                            .append(MqttfromColumnsStr)
                            .append(" ) WITH ( \n")
                            .append("'connector' = 'emqx-x'\n")
                            .append(
                                    ",'broker'='tcp://"
                                            + mqttConnectInfo.getHost()
                                            + ":"
                                            + mqttConnectInfo.getPort()
                                            + "'\n")
                            .append(",'topic'='" + inputTableInfo.getTopic() + "'\n")
                            .append(",'qos'='" + inputTableInfo.getQos() + "'")
                            .append(",'username'='" + mqttConnectInfo.getUsername() + "'")
                            .append(",'password'='" + AesUtils.decrypt(mqttConnectInfo.getPasswd()) + "'")
                            .append(",'isCleanSession'='" + mqttConnectInfo.isCleanSession() + "'")
                            .append(",'clientId'='" + mqttConnectInfo.getClientId() + "'")
                            .append(",'connectionTimeout'='" + mqttConnectInfo.getConnectionTimeout() + "'")
                            .append(",'keepAliveInterval'='" + mqttConnectInfo.getKeepAliveInterval() + "'")
                            .append(",'isAutomaticReconnect'='" + mqttConnectInfo.isAutomaticReconnect() + "'")
                            .append(",'maxReconnectDelay'='" + mqttConnectInfo.getMaxReconnectDelay() + "'")
                            .append(",'willTopic'='" + mqttConnectInfo.getTopic() + "'")
                            .append(",'willPayload'='" + mqttConnectInfo.getPayload() + "'")
                            .append(",'willQos'='" + mqttConnectInfo.getQos() + "'")
                            .append(",'format'='" + "json" + "'")
                            .append(",'isWillRetained'='" + mqttConnectInfo.isRetained() + "'")
                            .append(");");
                    break;
                case "INPUT_MYSQL":

                    // 解析当前节点信息
                    inputTableInfo = JSON.parseObject(node.getConfig(), InputTableInfo.class);
                    // 判断节点配置是否完善
                    if (inputTableInfo.getDatabaseId() == null || inputTableInfo.getDatabaseId().isEmpty()) {
                        throw new XDapBizException(RealWorkExceptionEnum.OUTPUT_NOT_CONFIG_DB);
                    }
                    // 获取数据源信息
                    DatasourceEntity datasourceById =
                            datasourceDao.getDatasourceById(inputTableInfo.getDatabaseId());
                    ConnectInfo connInfo =
                            DataBaseFactory.toConnectInfo(
                                    datasourceById.getConnectInfo(), DataBaseType.MySQL.name());
                    connInfo.setTableName(inputTableInfo.getTableName());

                    switch (finalType) {
                        case "SQL":
                            source =
                                    DataBaseFactory.getDatabase(DataBaseType.MySQL)
                                            .getSource(connInfo, inputTableInfo, outputTableInfo.getColumnMapping());

                            break;
                        case "JSON":
                            ChunJunReaderDto binlogReader =
                                    DataBaseFactory.getDatabase(DataBaseType.MySQL)
                                            .getReader(connInfo, inputTableInfo, outputTableInfo.getColumnMapping());

                            // content
                            content.setReader(binlogReader);
                            break;
                    }

                    break;
                case "OUTPUT_MYSQL":

                    // 判断节点配置是否完善
                    if (outputTableInfo.getDataSourceId() == null
                            || outputTableInfo.getDataSourceId().isEmpty()) {
                        throw new XDapBizException(RealWorkExceptionEnum.OUTPUT_NOT_CONFIG_DB);
                    }
                    // 获取数据源信息
                    DatasourceEntity datasource =
                            datasourceDao.getDatasourceById(outputTableInfo.getDataSourceId());
                    ConnectInfo connectInfo =
                            DataBaseFactory.toConnectInfo(datasource.getConnectInfo(), DataBaseType.MySQL.name());
                    connectInfo.setTableName(outputTableInfo.getTableName());

                    switch (finalType) {
                        case "SQL":
                            sink =
                                    DataBaseFactory.getDatabase(DataBaseType.MySQL)
                                            .getSink(connectInfo, outputTableInfo, inputTableInfo.getColumns());
                            break;
                        case "JSON":
                            content.setWriter(
                                    DataBaseFactory.getDatabase(DataBaseType.MySQL)
                                            .getWriter(connectInfo, outputTableInfo));
                            break;
                    }
                    break;

                case "OUTPUT_HIVE":
                    // 解析节点信息
                    if (outputTableInfo.getDataSourceId() == null
                            || outputTableInfo.getDataSourceId().isEmpty()) {
                        throw new XDapBizException(RealWorkExceptionEnum.OUTPUT_NOT_CONFIG_DB);
                    }
                    DatasourceEntity hiveDatasource =
                            datasourceDao.getDatasourceById(outputTableInfo.getDataSourceId());
                    ConnectInfo hiveConnectInfo =
                            DataBaseFactory.toConnectInfo(
                                    hiveDatasource.getConnectInfo(), DataBaseType.Hive.name());
                    hiveConnectInfo.setTableName(outputTableInfo.getTableName());

                    // toColumns  顺序需要调整
                    List<TableColumnInfo> tableHiveColumnInfos =
                            DataBaseFactory.getDatabase(DataBaseType.Hive)
                                    .queryTableColumn(hiveConnectInfo)
                                    .getTableColumnInfos();
                    List<String> toHiveColumns = new ArrayList<>();
                    List<ColumnMapping> hiveMappings = outputTableInfo.getColumnMapping();
                    // hiveColMapping key:去向字段,value:来源字段
                    hiveMappings.forEach(e -> hiveColMapping.put(e.getToColumn(), e.getFromColumn()));
                    for (TableColumnInfo column : tableHiveColumnInfos) {
                        if (hiveColMapping.get(column.getField()) != null) {
                            sqlFiled.append(hiveColMapping.get(column.getField())).append(",");
                        }
                    }
                    sqlFiled.deleteCharAt(sqlFiled.lastIndexOf(","));
                    TableInfo tableDetail = hiveServiceNew.getDetailTableInfo(hiveConnectInfo);

                    switch (finalType) {
                        case "SQL":
                            for (int i = 0; i < tableHiveColumnInfos.size(); i++) {
                                // ${字段 类型}
                                toHiveColumns.add(
                                        tableHiveColumnInfos.get(i).getField()
                                                + " "
                                                + compile("\\(.*?\\)")
                                                        .matcher(tableHiveColumnInfos.get(i).getType())
                                                        .replaceAll("")
                                                        .toUpperCase());
                            }

                            // 拼接链接信息
                            sink.append("CREATE TABLE sink ")
                                    .append(" ( ")
                                    .append(Strings.join(toHiveColumns, ','))
                                    .append(") WITH (")
                                    .append("'connector' = 'hdfs-x'\n,'path' = '")
                                    .append(tableDetail.getLocation())
                                    .append("'\n,'file-name' = '")
                                    .append(
                                            (tableDetail.getPartitionColumnInfos() != null
                                                            && !tableDetail.getPartitionColumnInfos().isEmpty())
                                                    ? tableDetail.getPartitionColumnInfos().get(0).getField()
                                                            + "="
                                                            + outputTableInfo.getPartition()
                                                    : "")
                                    .append("'\n,'properties.fs.defaultFS' = '")
                                    .append(tableDetail.getHiveHdfsUrl())
                                    .append("'\n,'properties.fs.hdfs.impl.disable.cache' = 'true'")
                                    .append("\n,'default-fs' = '")
                                    .append(tableDetail.getHiveHdfsUrl())
                                    .append(
                                            "'\n,'properties.fs.hdfs.impl' = 'org.apache.hadoop.hdfs.DistributedFileSystem'")
                                    .append("\n,'field-delimiter' = '")
                                    .append(tableDetail.getFieldSplitBy())
                                    .append("','encoding' = 'utf-8'")
                                    .append("\n,'write-mode' = '")
                                    .append(outputTableInfo.getWriteMode())
                                    .append("'\n,'properties.dfs.client.use.datanode.hostname' = 'true'")
                                    .append("\n,'properties.dfs.datanode.use.datanode.hostname' = 'true'")
                                    .append("\n,'next-check-rows' = '")
                                    .append(
                                            outputTableInfo.getCheckRows() == null ? 5 : outputTableInfo.getCheckRows())
                                    .append("'\n,'next-sync-second' = '")
                                    .append(chunJunProperties.getNextSyncSecond())
                                    .append("'\n,'max-file-size' = '0'")
                                    .append("\n,'file-type' = '")
                                    .append(tableDetail.getFileType())
                                    .append("'\n")
                                    .append(");");

                            break;
                        case "JSON":
                            content.setWriter(
                                    DataBaseFactory.getDatabase(DataBaseType.Hive)
                                            .getWriter(hiveConnectInfo, outputTableInfo));
                            break;
                    }

                    // 创建分区
                    if (outputTableInfo.getPartition() != null) {
                        AtomicReference<String> targetDir =
                                new AtomicReference<>(tableDetail.getHiveTableLocation());
                        List<PartitionValue> partitionValues =
                                JSON.parseArray(outputTableInfo.getPartition(), PartitionValue.class);
                        partitionValues.forEach(
                                e -> targetDir.set(targetDir + "/" + e.getField() + "=" + e.getValue()));
                        // 判断分区是否已经存在
                        try {
                            List<String> partitions = new ArrayList<>();
                            partitionValues.forEach(
                                    e -> partitions.add(e.getField() + "='" + e.getValue() + "'"));
                            String createPartitionSql =
                                    "alter table "
                                            + outputTableInfo.getTableName()
                                            + " add partition("
                                            + Strings.join(partitions, ',')
                                            + ")";
                            log.debug("自动创建分区:" + createPartitionSql);
                            hiveServiceNew.executeSql(hiveConnectInfo, createPartitionSql);
                        } catch (Exception e) {
                            log.error("分区已经被创建");
                        }
                    }
                    break;
                case "DATA_FILTER":
                    // 数据过滤
                    List<DataFilter> dataFilters = JSON.parseArray(node.getConfig(), DataFilter.class);

                    dataFilters.forEach(
                            filter -> {
                                String columnValue;
                                if (RealWorkConstants.COLUMN.equals(filter.getFilterType())) {
                                    columnValue =
                                            translateColumnValue(
                                                    columnMapping.get(filter.getFilterColumn()),
                                                    filter.getCustomValue(),
                                                    filter.getCompareOperate());
                                } else {
                                    columnValue = filter.getValueColumn();
                                }

                                String metaSql =
                                        filter.getFilterColumn()
                                                + filterTranslateOperate(filter.getCompareOperate())
                                                + " ( "
                                                + columnValue
                                                + " ) ";
                                if ("START".equals(filter.getOperate())) {
                                    dataFilter.append(metaSql);
                                } else if ("AND".equals(filter.getOperate())) {
                                    dataFilter.append(" and ( ").append(metaSql).append(" ) ");
                                } else if ("OR".equals(filter.getOperate())) {
                                    dataFilter.append(" or ( ").append(metaSql).append(" ) ");
                                } else {
                                    dataFilter.append(metaSql);
                                }
                            });

                    break;
                case "DATA_TRANSLATE":
                    // 数据替换
                    List<DataTranslate> dataTranslates =
                            JSON.parseArray(node.getConfig(), DataTranslate.class);
                    Map<String, DataTranslate> replaceColumnsMap =
                            dataTranslates.stream()
                                    .collect(
                                            Collectors.toMap(DataTranslate::getColumn, dataTranslate -> dataTranslate));

                    dataReplace.append("select ");

                    if ("OUTPUT_HIVE".equals(endNode.getType())) {
                        hiveColMapping.forEach(
                                (to, from) -> {
                                    DataTranslate dataTranslate = replaceColumnsMap.get(from);
                                    if (dataTranslate != null) {
                                        dataReplace.append(
                                                replaceTranslateOperate(
                                                        dataTranslate.getMatchType(),
                                                        dataTranslate.getColumn(),
                                                        dataTranslate.getOriginValue(),
                                                        dataTranslate.getNewValue(),
                                                        dataTranslate.getSensitiveStatus()));
                                    } else {
                                        dataReplace.append(from);
                                    }
                                    dataReplace.append(",");
                                });

                    } else {
                        inputTableInfo
                                .getColumns()
                                .forEach(
                                        column -> {
                                            if (usedColumn.contains(column.getColumn())) {
                                                DataTranslate dataTranslate = replaceColumnsMap.get(column.getColumn());
                                                if (dataTranslate != null) {
                                                    dataReplace.append(
                                                            replaceTranslateOperate(
                                                                    dataTranslate.getMatchType(),
                                                                    dataTranslate.getColumn(),
                                                                    dataTranslate.getOriginValue(),
                                                                    dataTranslate.getNewValue(),
                                                                    dataTranslate.getSensitiveStatus()));
                                                } else {
                                                    dataReplace.append(column.getColumn());
                                                }
                                                dataReplace.append(",");
                                            }
                                        });
                    }

                    dataReplace.deleteCharAt(dataReplace.length() - 1);
                    break;
                case "OUTPUT_KAFKA":
                    OutputTableInfo outputTableInfoKafka =
                            JSON.parseObject(node.getConfig(), OutputTableInfo.class);
                    // 解析字段顺序
                    List<String> outKafkaFromColumns = new ArrayList<>();
                    List<ColumnMapping> kafkaColumnMapping = outputTableInfoKafka.getColumnMapping();
                    kafkaColumnMapping.forEach(
                            e ->
                                    outKafkaFromColumns.add(
                                            translateKeyWork(e.getToColumn()) + ' ' + e.getToColumnType()));
                    String outKafkaFromColumnsStr = Strings.join(outKafkaFromColumns, ',');
                    switch (finalType) {
                        case "SQL":
                            if ("INPUT_MYSQL".equals(startNode.getType())
                                    || "INPUT_ORACLE".equals(startNode.getType())
                                    || "INPUT_SQLSERVER".equals(startNode.getType())) {

                                // 拼接链接信息
                                sink.append("CREATE TABLE sink ")
                                        .append(" (\n ")
                                        .append(outKafkaFromColumnsStr)
                                        .append(",\nPRIMARY KEY (")
                                        .append(kafkaColumnMapping.get(0).getToColumn())
                                        .append(") NOT ENFORCED")
                                        .append(" ) WITH ( \n")
                                        .append("'connector' = 'upsert-kafka-x'\n")
                                        .append(",'topic'='")
                                        .append(outputTableInfoKafka.getKafkaTopic())
                                        .append("'\n,'properties.bootstrap.servers'='")
                                        .append(outputTableInfoKafka.getKafkaServer());
                                // 支持多类型数据格式
                                if (RealWorkConstants.REAL_WORK_KAFKA_CSV.equals(
                                        outputTableInfoKafka.getDataFormat())) {
                                    sink.append(
                                            "'\n,'key.format' = 'csv'\n,'value.format' = 'csv'\n,'key.csv.ignore-parse-errors'='false'\n,'value.csv.ignore-parse-errors' = 'true'\n");
                                } else if (RealWorkConstants.REAL_WORK_CANAL_JSON.equals(
                                        outputTableInfoKafka.getDataFormat())) {
                                    sink.append(
                                            "'\n,'key.format' = 'canal-json'\n, 'key.format' = 'canal-json'\n,'key.canal-json.ignore-parse-errors' = 'true'\n,'key.canal-json.ignore-parse-errors' = 'true'\n");
                                } else {
                                    sink.append(
                                            "'\n,'key.format' = 'json'\n,'value.format' = 'json'\n,'key.json.fail-on-missing-field' = 'false'\n,'value.json.ignore-parse-errors' = 'true'\n");
                                }
                                sink.append(");");

                            } else {
                                // 拼接链接信息
                                sink.append("CREATE TABLE sink ")
                                        .append(" (\n ")
                                        .append(outKafkaFromColumnsStr)
                                        .append(" ) WITH ( \n")
                                        .append("'connector' = 'kafka-x'\n")
                                        .append(",'scan.startup.mode'='latest-offset'\n")
                                        // what group ？
                                        .append(",'properties.group.id'='test-consumer-group'\n")
                                        .append(",'topic'='")
                                        .append(outputTableInfoKafka.getKafkaTopic())
                                        .append("'\n,'properties.bootstrap.servers'='")
                                        .append(outputTableInfoKafka.getKafkaServer())
                                        // 并行度 默认null
                                        .append("'\n,'scan.parallelism'= '1'\n");
                                // 支持多类型数据格式
                                if (RealWorkConstants.REAL_WORK_KAFKA_CSV.equals(
                                        outputTableInfoKafka.getDataFormat())) {
                                    sink.append("\n,'format'='csv'\n,'csv.ignore-parse-errors' = 'true'\n");
                                } else if (RealWorkConstants.REAL_WORK_CANAL_JSON.equals(
                                        outputTableInfoKafka.getDataFormat())) {
                                    sink.append(
                                            "\n,'format'='canal-json'\n, 'canal-json.ignore-parse-errors'='true'\n");
                                } else {
                                    sink.append(
                                            "\n,'format'='json'\n,'json.fail-on-missing-field'='false'\n,'json.ignore-parse-errors'='true'\n");
                                }
                                sink.append(");");
                            }

                            break;

                        case "JSON":
                            List<ChunJunColumnDto> kafkaColumns = new ArrayList<>();
                            kafkaColumnMapping.forEach(
                                    mapping -> {
                                        ChunJunColumnDto columnDto = new ChunJunColumnDto();
                                        columnDto.setName(mapping.getToColumn());
                                        columnDto.setType(mapping.getToColumnType());
                                    });

                            // parameter
                            ChunJunWriterParameterDto kafkaParameter = new ChunJunWriterParameterDto();
                            kafkaParameter.setTopic(outputTableInfoKafka.getKafkaTopic());
                            kafkaParameter.setCodec(outputTableInfoKafka.getDataFormat());
                            kafkaParameter.setColumn(kafkaColumns);

                            Map<String, String> producerSettings = new HashMap<>();
                            producerSettings.put("auto.commit.enable", "false");
                            producerSettings.put("bootstrap.servers", outputTableInfoKafka.getKafkaServer());
                            kafkaParameter.setProducerSettings(producerSettings);

                            // writer
                            ChunJunWriterDto kafkaWriter = new ChunJunWriterDto();
                            kafkaWriter.setName("kafkawriter");
                            Map<String, String> table = new HashMap<>();
                            table.put("tableName", "sinkTable");
                            kafkaWriter.setTable(table);

                            content.setWriter(kafkaWriter);

                            break;
                    }

                    break;

                case "OUTPUT_DORIS":
                    OutputTableInfo outputTableInfoDoris =
                            JSON.parseObject(node.getConfig(), OutputTableInfo.class);

                    // 解析字段顺序
                    List<String> outDorisOutColumns = new ArrayList<>();
                    List<ColumnMapping> dorisColumnMapping = outputTableInfoDoris.getColumnMapping();
                    for (ColumnMapping metaMapping : dorisColumnMapping) {
                        outDorisOutColumns.add(
                                translateKeyWork(metaMapping.getToColumn())
                                        + " "
                                        + compile("\\(.*?\\)").matcher(metaMapping.getToColumnType()).replaceAll(""));
                    }
                    String outDorisOutColumnsStr = Strings.join(outDorisOutColumns, ',');

                    // 获取doris数据源信息
                    DatasourceEntity dorisDataSource =
                            datasourceDao.getDatasourceById(outputTableInfoDoris.getDataSourceId());
                    ConnectInfo dorisConnectInfo =
                            DataBaseFactory.toConnectInfo(
                                    dorisDataSource.getConnectInfo(), DataBaseType.Doris.name());

                    String jdbcUrl = dorisConnectInfo.getJdbcUrl();
                    JdbcInfo jdbcInfo = JdbcUtils.parseJdbcUrl(jdbcUrl);
                    if (jdbcInfo == null) {
                        throw new XDapBizException(RealWorkExceptionEnum.REAL_NODE_CONFIG_IS_ERROR);
                    }
                    String feNodes = jdbcInfo.getHost() + ":" + dorisConnectInfo.getDorisHttpPort();

                    switch (finalType) {
                        case "SQL":
                            sink.append("CREATE TABLE sink ")
                                    .append(" ( ")
                                    .append(outDorisOutColumnsStr)
                                    .append(") WITH (")
                                    .append("'connector'='doris-x'\n,")
                                    .append("'feNodes' = '")
                                    .append(feNodes)
                                    .append("'\n,'url'='")
                                    .append(jdbcUrl)
                                    .append("'\n,'username'='")
                                    .append(dorisConnectInfo.getUsername())
                                    .append("'\n,'password'='")
                                    .append(dorisConnectInfo.getPasswd())
                                    .append("'\n,'table-name' = '")
                                    .append(outputTableInfoDoris.getTableName())
                                    // 批量写数据条数，默认：1024
                                    .append("'\n,'sink.buffer-flush.max-rows' = '1024'")
                                    // 批量写时间间隔，默认：10000毫秒
                                    .append("\n,'sink.buffer-flush.interval' = '5000'")
                                    // 写入结果的并行度，默认：null
                                    .append("\n,'sink.parallelism' = '1'")
                                    .append(");");
                            break;

                        case "JSON":
                            List<ChunJunColumnDto> dorisColumns = new ArrayList<>();
                            dorisColumnMapping.forEach(
                                    column -> {
                                        ChunJunColumnDto dorisColumn = new ChunJunColumnDto();
                                        dorisColumn.setName(column.getToColumn());
                                        dorisColumn.setType(
                                                compile("\\(.*?\\)").matcher(column.getToColumnType()).replaceAll(""));
                                        dorisColumns.add(dorisColumn);
                                    });

                            ChunJunWriterDto dorisWriter = new ChunJunWriterDto();
                            ChunJunWriterParameterDto dorisParameter = new ChunJunWriterParameterDto();

                            // parameter
                            dorisParameter.setColumn(dorisColumns);
                            dorisParameter.setFeNodes(Collections.singletonList(feNodes));
                            dorisParameter.setDatabase(jdbcInfo.getDbName());
                            dorisParameter.setTable(outputTableInfoDoris.getTableName());
                            dorisParameter.setUsername(dorisConnectInfo.getUsername());
                            dorisParameter.setPassword(dorisConnectInfo.getPasswd());
                            dorisParameter.setUrl(jdbcUrl);

                            // writer
                            dorisWriter.setName("doriswriter");
                            Map<String, String> table = new HashMap<>();
                            table.put("tableName", "sinkTable");
                            dorisWriter.setTable(table);
                            dorisWriter.setParameter(dorisParameter);

                            content.setWriter(dorisWriter);
                            break;
                    }
                    break;
                case "OUTPUT_MQTT":
                    OutputTableInfo outputTableInfoMqtt =
                            JSON.parseObject(node.getConfig(), OutputTableInfo.class);

                    // 解析字段顺序
                    List<String> outMattOutColumns = new ArrayList<>();
                    List<ColumnMapping> mqttColumnMapping = outputTableInfoMqtt.getColumnMapping();
                    for (ColumnMapping metaMapping : mqttColumnMapping) {
                        outMattOutColumns.add(
                                translateKeyWork(metaMapping.getToColumn())
                                        + " "
                                        + compile("\\(.*?\\)").matcher(metaMapping.getToColumnType()).replaceAll(""));
                    }
                    String outMqttOutColumnsStr = Strings.join(outMattOutColumns, ',');

                    // 获取doris数据源信息
                    DatasourceEntity mqttDataSource =
                            datasourceDao.getDatasourceById(outputTableInfoMqtt.getDataSourceId());
                    MqttConnectInfo mqttConnectInfo1 =
                            JSON.parseObject(mqttDataSource.getConnectInfo(), MqttConnectInfo.class);

                    switch (finalType) {
                        case "SQL":
                            sink.append("CREATE TABLE sink ")
                                    .append(" ( ")
                                    .append(outMqttOutColumnsStr)
                                    .append(") WITH (")
                                    .append("'connector' = 'emqx-x'\n")
                                    .append(
                                            ",'broker'='tcp://"
                                                    + mqttConnectInfo1.getHost()
                                                    + ":"
                                                    + mqttConnectInfo1.getPort()
                                                    + "'\n")
                                    .append(",'topic'='" + outputTableInfoMqtt.getTopic() + "'\n")
                                    .append(",'qos'='" + outputTableInfoMqtt.getQos() + "'")
                                    .append(",'username'='" + mqttConnectInfo1.getUsername() + "'")
                                    .append(",'password'='" + AesUtils.decrypt(mqttConnectInfo1.getPasswd()) + "'")
                                    .append(",'isCleanSession'='" + mqttConnectInfo1.isCleanSession() + "'")
                                    .append(",'clientId'='" + mqttConnectInfo1.getClientId() + "'")
                                    .append(",'connectionTimeout'='" + mqttConnectInfo1.getConnectionTimeout() + "'")
                                    .append(",'keepAliveInterval'='" + mqttConnectInfo1.getKeepAliveInterval() + "'")
                                    .append(
                                            ",'isAutomaticReconnect'='" + mqttConnectInfo1.isAutomaticReconnect() + "'")
                                    .append(",'maxReconnectDelay'='" + mqttConnectInfo1.getMaxReconnectDelay() + "'")
                                    .append(",'willTopic'='" + mqttConnectInfo1.getTopic() + "'")
                                    .append(",'willPayload'='" + mqttConnectInfo1.getPayload() + "'")
                                    .append(",'willQos'='" + mqttConnectInfo1.getQos() + "'")
                                    .append(",'format'='" + "json" + "'")
                                    .append(",'isWillRetained'='" + mqttConnectInfo1.isRetained() + "'")
                                    .append(");");
                            break;
                    }
                    break;
                case "OUTPUT_CLICKHOUSE":
                    // 判断节点配置是否完善
                    if (outputTableInfo.getDataSourceId() == null
                            || outputTableInfo.getDataSourceId().isEmpty()) {
                        throw new XDapBizException(RealWorkExceptionEnum.OUTPUT_NOT_CONFIG_DB);
                    }
                    // 获取数据源信息
                    DatasourceEntity ckDatasource =
                            datasourceDao.getDatasourceById(outputTableInfo.getDataSourceId());
                    ConnectInfo ckConnectInfo =
                            DataBaseFactory.toConnectInfo(
                                    ckDatasource.getConnectInfo(), DataBaseType.ClickHouse.name());
                    ckConnectInfo.setTableName(outputTableInfo.getTableName());

                    switch (finalType) {
                        case "SQL":

                            // 解析字段信息 字段需要注意排序否则flink无法识别字段类型
                            List<TableColumnInfo> tableColumnInfos =
                                    DataBaseFactory.getDatabase(DataBaseType.ClickHouse)
                                            .queryTableColumn(ckConnectInfo)
                                            .getTableColumnInfos();
                            List<String> toColumns = new ArrayList<>();
                            List<ColumnMapping> mappings = outputTableInfo.getColumnMapping();
                            Map<String, String> colMapping = new HashMap<>();
                            mappings.forEach(
                                    e -> {
                                        colMapping.put(e.getFromColumn(), e.getToColumn());
                                    });
                            inputTableInfo
                                    .getColumns()
                                    .forEach(
                                            e -> {
                                                String s = colMapping.get(e.getColumn());
                                                if (s == null) {
                                                    throw new XDapBizException(
                                                            RealWorkExceptionEnum.COLUMN_TYPE_MAPPING_ERROR);
                                                }
                                                tableColumnInfos.forEach(
                                                        v -> {
                                                            if (s.equals(v.getField())) {

                                                                toColumns.add(
                                                                        v.getField()
                                                                                + ' '
                                                                                + compile("\\(.*?\\)")
                                                                                        .matcher(v.getType())
                                                                                        .replaceAll("")
                                                                                        .toUpperCase());
                                                            }
                                                        });
                                            });
                            String toColumnsStr = Strings.join(toColumns, ',');
                            // 拼接链接信息
                            sink.append("CREATE TABLE sink ")
                                    .append(" ( ")
                                    .append(toColumnsStr)
                                    .append(") WITH (")
                                    .append("'connector'='clickhouse-x'\n,'url'='")
                                    .append(ckConnectInfo.getJdbcUrl())
                                    .append("'\n,'table-name'='")
                                    .append(ckConnectInfo.getTableName())
                                    .append("'\n,'username'='")
                                    .append(ckConnectInfo.getUsername())
                                    .append("'\n,'password'='")
                                    .append(ckConnectInfo.getPasswd())
                                    // 批量写数据条数，默认：1024
                                    .append("'\n,'sink.buffer-flush.max-rows' = '1024'")
                                    // 批量写时间间隔，默认：10000毫秒
                                    .append("\n,'sink.buffer-flush.interval' = '5000'")
                                    // 写入结果的并行度，默认：null
                                    .append("\n,'sink.parallelism' = '1'")
                                    .append(");");
                            break;
                        case "JSON":

                            // column list
                            List<ColumnMapping> ckColumnMapping = outputTableInfo.getColumnMapping();
                            List<ChunJunColumnDto> ckInputColumnDtos = new ArrayList<>();
                            List<ChunJunColumnDto> ckOutputColumnDtos = new ArrayList<>();
                            ckColumnMapping.forEach(
                                    mapping -> {
                                        ChunJunColumnDto inputColumn = new ChunJunColumnDto();
                                        inputColumn.setName(mapping.getFromColumn());
                                        inputColumn.setType(mapping.getFromColumnType());
                                        if (inputColumn.getType().contains("(")) {
                                            inputColumn.setType(
                                                    compile("\\(.*?\\)")
                                                            .matcher(inputColumn.getType())
                                                            .replaceAll("")
                                                            .toUpperCase());
                                        }
                                        ckInputColumnDtos.add(inputColumn);

                                        ChunJunColumnDto outputColumn = new ChunJunColumnDto();
                                        outputColumn.setName(mapping.getToColumn());
                                        outputColumn.setType(mapping.getToColumnType());
                                        if (outputColumn.getType().contains("(")) {
                                            outputColumn.setType(
                                                    compile("\\(.*?\\)")
                                                            .matcher(outputColumn.getType())
                                                            .replaceAll("")
                                                            .toUpperCase());
                                        }
                                        ckOutputColumnDtos.add(outputColumn);
                                    });

                            ChunJunWriterDto cklWriter = new ChunJunWriterDto();
                            ChunJunWriterParameterDto ckParameter = new ChunJunWriterParameterDto();
                            ChunJunWriterConnectionDto ckConnection = new ChunJunWriterConnectionDto();

                            // connection
                            ckConnection.setJdbcUrl(ckConnectInfo.getJdbcUrl());
                            ckConnection.setTable(Collections.singletonList(ckConnectInfo.getTableName()));

                            // parameter
                            binlogParameter.setColumn(ckInputColumnDtos);
                            ckParameter.setColumn(ckOutputColumnDtos);
                            ckParameter.setConnection(Collections.singletonList(ckConnection));
                            ckParameter.setWriteMode("insert");
                            ckParameter.setBatchSize(2);
                            ckParameter.setUsername(ckConnectInfo.getUsername());
                            ckParameter.setPassword(ckConnectInfo.getPasswd());

                            // writer
                            Map<String, String> ckOutputTransformer = new HashMap<>();
                            ckOutputTransformer.put("tableName", "sinkTable");
                            cklWriter.setTable(ckOutputTransformer);
                            cklWriter.setParameter(ckParameter);
                            cklWriter.setName("clickhousewriter");

                            content.setWriter(cklWriter);
                            break;
                    }
                    break;

                case "INPUT_ORACLE":
                    // 解析当前节点信息
                    inputTableInfo = JSON.parseObject(node.getConfig(), InputTableInfo.class);
                    // 判断节点配置是否完善
                    if (inputTableInfo.getDatabaseId() == null || inputTableInfo.getDatabaseId().isEmpty()) {
                        throw new XDapBizException(RealWorkExceptionEnum.OUTPUT_NOT_CONFIG_DB);
                    }
                    // 获取数据源信息
                    DatasourceEntity logminerDatasource =
                            datasourceDao.getDatasourceById(inputTableInfo.getDatabaseId());
                    ConnectInfo logminerConn =
                            DataBaseFactory.toConnectInfo(
                                    logminerDatasource.getConnectInfo(), DataBaseType.Oracle.name());
                    logminerConn.setTableName(inputTableInfo.getTableName());

                    switch (finalType) {
                        case "SQL":
                            source =
                                    DataBaseFactory.getDatabase(DataBaseType.Oracle)
                                            .getSource(logminerConn, inputTableInfo, outputTableInfo.getColumnMapping());
                            break;

                        case "JSON":

                            // content
                            content.setReader(
                                    DataBaseFactory.getDatabase(DataBaseType.Oracle)
                                            .getReader(logminerConn, inputTableInfo, outputTableInfo.getColumnMapping()));
                            break;
                    }
                    break;

                case "OUTPUT_ORACLE":

                    // 判断节点配置是否完善
                    if (outputTableInfo.getDataSourceId() == null
                            || outputTableInfo.getDataSourceId().isEmpty()) {
                        throw new XDapBizException(RealWorkExceptionEnum.OUTPUT_NOT_CONFIG_DB);
                    }

                    // 获取数据源信息
                    DatasourceEntity outputOracle =
                            datasourceDao.getDatasourceById(outputTableInfo.getDataSourceId());
                    ConnectInfo outputOracleConnectInfo =
                            DataBaseFactory.toConnectInfo(
                                    outputOracle.getConnectInfo(), DataBaseType.Oracle.name());
                    outputOracleConnectInfo.setTableName(outputTableInfo.getTableName());

                    switch (finalType) {
                        case "SQL":
                            outputOracleConnectInfo.setSchema(outputTableInfo.getSchema());
                            sink =
                                    DataBaseFactory.getDatabase(DataBaseType.Oracle)
                                            .getSink(
                                                    outputOracleConnectInfo, outputTableInfo, inputTableInfo.getColumns());
                            break;
                        case "JSON":
                            content.setWriter(
                                    DataBaseFactory.getDatabase(DataBaseType.Oracle)
                                            .getWriter(outputOracleConnectInfo, outputTableInfo));
                            break;
                    }
                    break;

                case "INPUT_SQLSERVER":
                    // 解析当前节点信息
                    inputTableInfo = JSON.parseObject(node.getConfig(), InputTableInfo.class);
                    // 判断节点配置是否完善
                    if (inputTableInfo.getDatabaseId() == null || inputTableInfo.getDatabaseId().isEmpty()) {
                        throw new XDapBizException(RealWorkExceptionEnum.OUTPUT_NOT_CONFIG_DB);
                    }
                    // 获取数据源信息
                    DatasourceEntity sqlserverDatasource =
                            datasourceDao.getDatasourceById(inputTableInfo.getDatabaseId());
                    ConnectInfo sqlserverConn =
                            DataBaseFactory.toConnectInfo(
                                    sqlserverDatasource.getConnectInfo(), DataBaseType.SqlServer.name());
                    sqlserverConn.setTableName(inputTableInfo.getTableName());

                    switch (finalType) {
                        case "SQL":
                            source =
                                    DataBaseFactory.getDatabase(DataBaseType.SqlServer)
                                            .getSource(sqlserverConn, inputTableInfo, outputTableInfo.getColumnMapping());
                            break;

                        case "JSON":
                            ChunJunReaderDto sqlserverReader =
                                    DataBaseFactory.getDatabase(DataBaseType.SqlServer)
                                            .getReader(sqlserverConn, inputTableInfo, outputTableInfo.getColumnMapping());

                            content.setReader(sqlserverReader);
                            break;
                    }
                    break;

                case "OUTPUT_SQLSERVER":

                    // 判断节点配置是否完善
                    if (outputTableInfo.getDataSourceId() == null
                            || outputTableInfo.getDataSourceId().isEmpty()) {
                        throw new XDapBizException(RealWorkExceptionEnum.OUTPUT_NOT_CONFIG_DB);
                    }

                    // 获取数据源信息
                    DatasourceEntity outputSqlserver =
                            datasourceDao.getDatasourceById(outputTableInfo.getDataSourceId());
                    ConnectInfo outputSqlserverConnectInfo =
                            DataBaseFactory.toConnectInfo(
                                    outputSqlserver.getConnectInfo(), DataBaseType.SqlServer.name());
                    outputSqlserverConnectInfo.setTableName(outputTableInfo.getTableName());

                    switch (finalType) {
                        case "SQL":
                            sink =
                                    DataBaseFactory.getDatabase(DataBaseType.SqlServer)
                                            .getSink(
                                                    outputSqlserverConnectInfo, outputTableInfo, inputTableInfo.getColumns());
                            break;
                        case "JSON":
                            ChunJunWriterDto sqlserverWriter =
                                    DataBaseFactory.getDatabase(DataBaseType.SqlServer)
                                            .getWriter(outputSqlserverConnectInfo, outputTableInfo);
                            content.setWriter(sqlserverWriter);
                            break;
                    }
                    break;
                case "INPUT_POSTGRESQL":
                    // 解析当前节点信息
                    inputTableInfo = JSON.parseObject(node.getConfig(), InputTableInfo.class);
                    // 判断节点配置是否完善
                    if (inputTableInfo.getDatabaseId() == null || inputTableInfo.getDatabaseId().isEmpty()) {
                        throw new XDapBizException(RealWorkExceptionEnum.OUTPUT_NOT_CONFIG_DB);
                    }
                    // 获取数据源信息
                    DatasourceEntity postgresqlDatasource =
                            datasourceDao.getDatasourceById(inputTableInfo.getDatabaseId());
                    ConnectInfo postgresqlConn =
                            DataBaseFactory.toConnectInfo(
                                    postgresqlDatasource.getConnectInfo(), DataBaseType.SqlServer.name());
                    postgresqlConn.setTableName(inputTableInfo.getTableName());
                    postgresqlConn.setSchema(inputTableInfo.getSchema());

                    switch (finalType) {
                        case "SQL":
                            throw new XDapBizException(RealWorkExceptionEnum.SYNC_TYPE_IS_INVALID);
                        case "JSON":
                            content.setReader(
                                    DataBaseFactory.getDatabase(DataBaseType.PostgreSQL)
                                            .getReader(
                                                    postgresqlConn, inputTableInfo, outputTableInfo.getColumnMapping()));
                            break;
                    }
                    break;

                case "OUTPUT_POSTGRESQL":

                    // 判断节点配置是否完善
                    if (outputTableInfo.getDataSourceId() == null
                            || outputTableInfo.getDataSourceId().isEmpty()) {
                        throw new XDapBizException(RealWorkExceptionEnum.OUTPUT_NOT_CONFIG_DB);
                    }

                    // 获取数据源信息
                    DatasourceEntity outputPostgre =
                            datasourceDao.getDatasourceById(outputTableInfo.getDataSourceId());
                    ConnectInfo outputPostgreConnectInfo =
                            DataBaseFactory.toConnectInfo(
                                    outputPostgre.getConnectInfo(), DataBaseType.SqlServer.name());
                    outputPostgreConnectInfo.setTableName(outputTableInfo.getTableName());

                    switch (finalType) {
                        case "SQL":
                            throw new XDapBizException(RealWorkExceptionEnum.SYNC_TYPE_IS_INVALID);
                        case "JSON":
                            content.setWriter(
                                    DataBaseFactory.getDatabase(DataBaseType.PostgreSQL)
                                            .getWriter(outputPostgreConnectInfo, outputTableInfo));
                            break;
                    }
                    break;
            }
        }

        // 来源是pgsql和mqtt的实时作业目前未支持checkpoint
        boolean enableCheckPoint;
        switch (startNode.getType()) {
            case "INPUT_POSTGRESQL":
            case "INPUT_MQTT":
                enableCheckPoint = false;
                break;
            default:
                enableCheckPoint = true;
        }

        if ("SQL".equals(type)) {
            StringBuilder sql = new StringBuilder();

            String select = "select * ";
            if ("OUTPUT_HIVE".equals(endNode.getType())) {
                select = "select " + sqlFiled;
            }

            sql.append(source)
                    .append("\n")
                    .append(sink)
                    .append("insert into sink\n")
                    .append(dataReplace.length() == 0 ? select : dataReplace)
                    .append(" from source ")
                    .append(dataFilter.length() == 0 ? "" : " where " + dataFilter);

            // 执行job
            logServiceNew.executeSqlCommand(
                    engineInfo,
                    WorkTypeConstants.CHUNJUN,
                    sql.toString(),
                    executeId,
                    checkpointPath,
                    realWorkName,
                    WorkTypeConstants.REAL_WORK,
                    enableCheckPoint);
        }

        if ("JSON".equals(type)) {

            StringBuilder sql = new StringBuilder();

            // 没有字符串替换，添加select
            if (dataReplace.indexOf("select") == -1) {
                if ("".equals(sqlFiled.toString())) {
                    {
                        sqlFiled =
                                new StringBuilder(
                                        Strings.join(
                                                outputTableInfo.getColumnMapping().stream()
                                                        .map(columnName -> String.format("`%s`", columnName.getFromColumn()))
                                                        .collect(Collectors.toList()),
                                                ','));
                    }
                }
                sql.append("select ").append(sqlFiled);
            }
            sql.append(dataReplace)
                    .append(" from sourceTable ")
                    .append(dataFilter.length() == 0 ? "" : "where " + dataFilter);
            Map<String, String> transformSql = new HashMap<>();
            transformSql.put("transformSql", String.valueOf(sql));
            content.setTransformer(transformSql);

            ChunJunSpeedDto speed = new ChunJunSpeedDto();
            ChunJunRestoreDto restore = new ChunJunRestoreDto();
            ChunJunSettingDto setting = new ChunJunSettingDto();
            ChunJunJobDto job = new ChunJunJobDto();
            speed.setChannel(1);
            speed.setBytes(0L);
            setting.setSpeed(speed);
            restore.setIsStream(true);
            setting.setRestore(restore);
            job.setContent(Collections.singletonList(content));
            job.setSetting(setting);

            // 写入模式
            String writeMode = "insert";
            String appendMode = outputTableInfo.getAppendMode();
            if (Objects.nonNull(appendMode)) {
                ChunJunWriterParameterDto parameter = content.getWriter().getParameter();

                switch (appendMode.toLowerCase()) {
                    case "overwrite":
                        parameter.setPreSql(
                                Collections.singletonList(
                                        EncryptUtils.base64Encode("truncate table " + outputTableInfo.getTableName())));
                        break;

                    case "merge":
                        writeMode = "update";
                        break;
                }
                parameter.setWriteMode(writeMode);
            }

            // 可执行json
            StringBuilder json =
                    new StringBuilder(
                            JSON.toJSONString(job, SerializerFeature.DisableCircularReferenceDetect));
            json.insert(0, "{\"job\":").append("}");
            String jsonString = StringEscapeUtils.unescapeJava(json.toString());

            // 执行job
            logServiceNew.executeJsonCommand(
                    engineInfo,
                    WorkTypeConstants.CHUNJUN,
                    jsonString,
                    executeId,
                    checkpointPath,
                    realWorkName,
                    WorkTypeConstants.REAL_WORK,
                    enableCheckPoint);
        }
    }

    // 实际执行flinkSql
    public void executeFlinkSql(
            String executeId, String scripts, String workName, EngineInfo engineInfo) {

        // 处理脚本
        DehoopExpressionParser dehoopExpressionParser = DehoopExpressionParser.getInstance();
        String paramExpressionSQL =
                dehoopExpressionParser.parseExpression(
                        DehoopExpressionParser.commentsFilter(scripts), String.class);

        logServiceNew.executeFlinkSqlCommand(
                engineInfo,
                WorkTypeConstants.CHUNJUN,
                paramExpressionSQL,
                executeId,
                workName,
                WorkTypeConstants.FLINK_SQL);
    }

    public void stopDevRealWork(String realWorkId) {

        Assert.isNotEmpty(realWorkId, CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        JobRealWorks realWork = realWorkDao.getRealWork(realWorkId);
        if (realWork == null) {
            throw new XDapBizException(RealWorkExceptionEnum.REAL_WORK_IS_NOT_EXIST);
        }

        if (!WorkRunStatusConstants.RUNNING.equals(realWork.getRunningStatus())) {
            return;
        }

        JobWorkLogs workLog = logDao.getWorkLog(realWork.getExecuteId());
        if (workLog != null
                && StringUtils.isNotBlank(workLog.getYarnApplicationId())
                && !WorkRunStatusConstants.OVER.equals(workLog.getState())) {
            // 获取计算引擎配置信息
            EnvCalConfigInfo envCalConfig = envConfigService.getEnvCalConfig(realWork.getEnvId());
            yarnApplicationService.killApplication(workLog.getYarnApplicationId(), envCalConfig);
        }

        realWorkDao.updateRealWorkRunningStatus(realWorkId, WorkRunStatusConstants.NO_RUNNING);
    }

    public void stopProdRealWork(String workInstanceId) {

        Assert.isNotEmpty(workInstanceId, CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        SchWorkInstances workInstance = scheduleDao.getSchWorkInstance(workInstanceId);

        if (workInstance != null && WorkRunStatusConstants.RUNNING.equals(workInstance.getState())) {

            JobWorkLogs workLog = logDao.getWorkLog(workInstance.getExecuteId());
            if (workLog != null
                    && StringUtils.isNotBlank(workLog.getYarnApplicationId())
                    && !WorkRunStatusConstants.OVER.equals(workLog.getState())) {
                // 获取计算引擎配置信息
                EnvCalConfigInfo envCalConfig = envConfigService.getEnvCalConfig(workInstance.getEnvId());
                yarnApplicationService.killApplication(workLog.getYarnApplicationId(), envCalConfig);
            }

            scheduleDao.updateWorkInstanceStateAndEndDate(
                    workInstance.getWorkInstanceId(), WorkRunStatusConstants.CANCELED, new Date());
        }
    }

    public static String translateKeyWork(String word) {

        List<String> keyWords = Arrays.asList("select", "sql", "date", "type");
        if (keyWords.contains(word)) {
            return "`" + word + "`";
        }
        return word;
    }
}

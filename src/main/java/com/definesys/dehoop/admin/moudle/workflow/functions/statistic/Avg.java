package com.definesys.dehoop.admin.moudle.workflow.functions.statistic;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;

public class Avg {

    public Double invoke(Double... values) {

        return BigDecimal.valueOf(
                        Arrays.stream(values).mapToDouble(Double::doubleValue).average().getAsDouble())
                .setScale(2, RoundingMode.DOWN)
                .doubleValue();
    }

    public Double invoke(Integer... values) {

        return BigDecimal.valueOf(
                        Arrays.stream(values).mapToInt(Integer::intValue).average().getAsDouble())
                .setScale(2, RoundingMode.DOWN)
                .doubleValue();
    }
}

package com.definesys.dehoop.admin.moudle.metadata.task.dao;

import com.definesys.dehoop.admin.moudle.metadata.task.pojo.AstMetaDataTask;
import com.definesys.dehoop.api.constant.DehoopConstant;
import com.definesys.dehoop.api.constant.MetadataTaskConstant;
import com.definesys.mpaas.query.MpaasQueryFactory;
import com.definesys.mpaas.query.db.PageQueryResult;
import com.definesys.mpaas.query.session.MpaasSession;

import org.springframework.stereotype.Repository;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
@AllArgsConstructor
public class MetadataDao {

    private final MpaasQueryFactory sw;

    public void addMetaDataTask(AstMetaDataTask astMetaDataTask) {

        sw.buildQuery().doInsert(astMetaDataTask);
    }

    public void updateMetaDataTask(AstMetaDataTask astMetaDataTask) {

        sw.buildQuery()
                .eq("ID", astMetaDataTask.getId())
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doUpdate(astMetaDataTask);
    }

    public void updateTaskInfo(AstMetaDataTask astMetaDataTask) {

        sw.buildQuery()
                .eq("ID", astMetaDataTask.getId())
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("NAME", astMetaDataTask.getName())
                .update("DESCR", astMetaDataTask.getDescr())
                .update("PARENT_ID", astMetaDataTask.getParentId())
                .doUpdate(astMetaDataTask);
    }

    public void deleteMetaDataById(String id) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(AstMetaDataTask.class);
    }

    public void deleteMetaDataByParentId(String parentId) {

        sw.buildQuery()
                .eq("PARENT_ID", parentId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(AstMetaDataTask.class);
    }

    public AstMetaDataTask getMetadataTaskById(String id) {

        if (id == null || id.isEmpty()) {
            return null;
        }

        return sw.buildQuery().eq("ID", id).doQueryFirst(AstMetaDataTask.class);
    }

    public void updateFolderName(String id, String name) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("NAME", name)
                .doUpdate(AstMetaDataTask.class);
    }

    public void deleteMetadataTask(List<String> metadataTaskIdList) {

        if (metadataTaskIdList.isEmpty()) {
            return;
        }
        sw.buildQuery()
                .in("ID", metadataTaskIdList)
                .eq("STATUS", DehoopConstant.DISABLE)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(AstMetaDataTask.class);
    }

    public void updateMetadataTaskStatus(String id, String status) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("STATUS", status)
                .doUpdate(AstMetaDataTask.class);
    }

    public PageQueryResult<AstMetaDataTask> pageQueryMetadata(
            String folderId, String searchWord, Integer page, Integer pageSize) {

        return sw.buildQuery()
                .eq("PARENT_ID", folderId)
                .like("NAME", searchWord)
                .doPageQuery(page, pageSize, AstMetaDataTask.class);
    }

    public PageQueryResult<AstMetaDataTask> pageQueryMetadata(
            String folderId,
            String searchWord,
            String creator,
            String status,
            String periodType,
            Date lastExecuteStartDate,
            Date lastExecuteEndDate,
            Integer page,
            Integer pageSize) {

        return sw.buildQuery()
                .eq("PARENT_ID", folderId)
                .eq("CREATED_BY", creator)
                .eq("STATUS", status)
                .eq("PERIOD_TYPE", periodType)
                .like("NAME", searchWord)
                .gt("LAST_EXECUTE_DATE", lastExecuteStartDate)
                .lt("LAST_EXECUTE_DATE", lastExecuteEndDate)
                .orderBy("ID", "desc")
                .doPageQuery(page, pageSize, AstMetaDataTask.class);
    }

    public List<AstMetaDataTask> queryTaskByName(String searchWord) {

        return sw.buildQuery()
                .eq("TYPE", MetadataTaskConstant.TASK)
                .like("NAME", searchWord)
                .doQuery(AstMetaDataTask.class);
    }

    public List<AstMetaDataTask> queryFolder() {

        return sw.buildQuery().eq("TYPE", MetadataTaskConstant.FOLDER).doQuery(AstMetaDataTask.class);
    }

    public List<AstMetaDataTask> queryTaskByIdList(List<String> idList) {

        if (idList.isEmpty()) {
            return new ArrayList<>();
        }

        return sw.buildQuery().in("ID", idList).doQuery(AstMetaDataTask.class);
    }

    public void updateLastExecuteDate(String id, Date lastExecuteDate) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("LAST_EXECUTE_DATE", lastExecuteDate)
                .doUpdate(AstMetaDataTask.class);
    }

    public void updateJobId(String id, Long jobId) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("JOB_ID", jobId)
                .doUpdate(AstMetaDataTask.class);
    }

    public AstMetaDataTask getFolderByName(String name) {

        return sw.buildQuery()
                .eq("NAME", name)
                .eq("TYPE", MetadataTaskConstant.FOLDER)
                .doQueryFirst(AstMetaDataTask.class);
    }

    public List<AstMetaDataTask> queryTaskByParentIdAndSate(String parentId, String state) {

        return sw.buildQuery()
                .eq("PARENT_ID", parentId)
                .eq("STATUS", state)
                .eq("TYPE", MetadataTaskConstant.TASK)
                .doQuery(AstMetaDataTask.class);
    }

    public AstMetaDataTask getTaskByNameAndParentId(String name, String parentId) {

        return sw.buildQuery()
                .eq("NAME", name)
                .eq("PARENT_ID", parentId)
                .eq("TYPE", MetadataTaskConstant.TASK)
                .doQueryFirst(AstMetaDataTask.class);
    }
}

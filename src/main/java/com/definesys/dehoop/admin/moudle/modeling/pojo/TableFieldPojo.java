package com.definesys.dehoop.admin.moudle.modeling.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 表字段
 * @create date 2021/6/21 11:52
 */
@Data
public class TableFieldPojo {

    private String id;

    private String name;

    private String type;

    private Integer length;

    private Integer precision;

    private Integer scale;

    private String comment;

    private int sequenceNumber;

    private Boolean isPrimaryKey;

    private Boolean isNotNull;

    /** 是否是系统字段 */
    private Boolean isSys;

    /** 是否石分区字段 */
    private Boolean isPartitionField = false;
}

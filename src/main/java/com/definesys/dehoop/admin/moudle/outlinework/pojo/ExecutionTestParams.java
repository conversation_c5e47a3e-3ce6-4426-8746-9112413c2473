package com.definesys.dehoop.admin.moudle.outlinework.pojo;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.Style;
import com.definesys.mpaas.query.annotation.Table;

import lombok.*;

@Builder
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "EXECUTION_TEST_PARAMS")
@Style
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionTestParams extends XdapBasePojo {

    private String id;

    private String workId;

    private String paramName;

    private String paramValue;
}

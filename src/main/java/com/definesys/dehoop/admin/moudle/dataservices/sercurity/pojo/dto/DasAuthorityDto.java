package com.definesys.dehoop.admin.moudle.dataservices.sercurity.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年09月18日 16:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DasAuthorityDto {

    private String id;

    private String groupId;

    private String subjectId;

    private String subjectName;

    private String subjectType;

    private Integer sequenceNumber;

    private String scope;

    private Boolean retrieve;

    private Boolean edit;

    private Boolean deletion;

    private Boolean publish;

    private String avatarUrl;

    private Boolean isDefault;
}

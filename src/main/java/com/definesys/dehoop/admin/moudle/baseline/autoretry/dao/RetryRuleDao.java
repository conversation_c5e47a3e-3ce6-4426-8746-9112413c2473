package com.definesys.dehoop.admin.moudle.baseline.autoretry.dao;

import com.definesys.dehoop.admin.moudle.baseline.autoretry.pojo.CtrRetryRuleWork;
import com.definesys.dehoop.admin.moudle.baseline.autoretry.pojo.CtrRetryRules;
import com.definesys.dehoop.admin.moudle.baseline.autoretry.pojo.dto.RetryRuleReqDto;
import com.definesys.dehoop.admin.moudle.baseline.autoretry.pojo.dto.SimpleOutlineWorkInfoDto;
import com.definesys.mpaas.query.MpaasQueryFactory;
import com.definesys.mpaas.query.db.PageQueryResult;
import com.definesys.mpaas.query.session.MpaasSession;

import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class RetryRuleDao {

    private final MpaasQueryFactory sw;

    public RetryRuleDao(MpaasQueryFactory sw) {
        this.sw = sw;
    }

    public void addRetryRule(CtrRetryRules ctrRetryRules) {

        sw.buildQuery().doInsert(ctrRetryRules);
    }

    public void deleteRetryRule(String id) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(CtrRetryRules.class);
    }

    public void deleteRetryRuleWork(String retryRuleId) {
        sw.buildQuery()
                .eq("RETRY_RULE_ID", retryRuleId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(CtrRetryRuleWork.class);
    }

    public void batchDeleteRetryRuleWork(List<String> retryRuleIdList) {

        if (retryRuleIdList == null || retryRuleIdList.isEmpty()) {

            return;
        }

        sw.buildQuery()
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .in("RETRY_RULE_ID", retryRuleIdList)
                .doDelete(CtrRetryRuleWork.class);
    }

    public void batchDelete(List<String> idList) {

        sw.buildQuery()
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .in("ID", idList)
                .doDelete(CtrRetryRules.class);
    }

    public void updateRetryRule(RetryRuleReqDto retryRuleReqDto) {

        sw.buildQuery()
                .eq("ID", retryRuleReqDto.getId())
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("NAME", retryRuleReqDto.getName())
                .update("RETRY_WAY", retryRuleReqDto.getRetryWay())
                .update("RETRY_POLICY", retryRuleReqDto.getRetryPolicy())
                .update("RETRY_INTERVAL", retryRuleReqDto.getRetryInterval())
                .update("MAX_RETRY_TIMES", retryRuleReqDto.getMaxRetryTimes())
                .doUpdate(CtrRetryRules.class);
    }

    public CtrRetryRules getRetryRuleById(String id) {

        return sw.buildQuery().eq("ID", id).doQueryFirst(CtrRetryRules.class);
    }

    public CtrRetryRules getRetryRuleByName(String name, String projectId) {

        return sw.buildQuery()
                .eq("PROJECT_ID", projectId)
                .eq("NAME", name)
                .doQueryFirst(CtrRetryRules.class);
    }

    public PageQueryResult<CtrRetryRules> pageQueryRetryRule(
            String projectId, String searchWord, Integer page, Integer pageSize) {

        return sw.buildQuery()
                .eq("PROJECT_ID", projectId)
                .like("NAME", searchWord)
                .doPageQuery(page, pageSize, CtrRetryRules.class);
    }

    public void addRetryRuleWork(CtrRetryRuleWork ctrRetryRuleWork) {

        sw.buildQuery().doInsert(ctrRetryRuleWork);
    }

    public void deleteRetryRuleWorkByWorkId(String workId) {

        sw.buildQuery()
                .eq("WORK_ID", workId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(CtrRetryRuleWork.class);
    }

    public CtrRetryRuleWork getRetryRuleWorkByWorkId(String workId) {

        return sw.buildQuery().eq("WORK_ID", workId).doQueryFirst(CtrRetryRuleWork.class);
    }

    public PageQueryResult<SimpleOutlineWorkInfoDto> pageQueryRuleWork(
            String retryRuleId, String searchWord, Integer page, Integer pageSize) {

        searchWord = "%".concat(searchWord).concat("%");

        return sw.buildQuery()
                .sql(
                        "select *\n"
                                + "from (select crrw.ID            id,\n"
                                + "             crrw.RETRY_RULE_ID retryRuleId,\n"
                                + "             jow.ID             workId,\n"
                                + "             jow.NAME           workName,\n"
                                + "             jow.FLOW_ID        flowId,\n"
                                + "             jow.TYPE           workType,\n"
                                + "             jow.DIRECTOR       director\n"
                                + "      from CTR_RETRY_RULE_WORK crrw,\n"
                                + "           JOB_OUTLINE_WORKS jow\n"
                                + "      where crrw.WORK_ID = jow.ID\n"
                                + "        and crrw.RETRY_RULE_ID = #retryRuleId \n"
                                + "        and jow.NAME like #searchWord \n"
                                + "        and crrw.TENANT_ID = #tenantId ) alia")
                .setVar("retryRuleId", retryRuleId)
                .setVar("searchWord", searchWord)
                .setVar("tenantId", MpaasSession.getUserProfile().getTenantId())
                .doPageQuery(page, pageSize, SimpleOutlineWorkInfoDto.class);
    }

    /**
     * 更新重试规则与作业绑定关系
     *
     * @param retryRuleId 重试规则id
     * @param workId 作业id
     * @param workState 作业状态
     */
    public void updateRuleWorkState(String retryRuleId, String workId, String workState) {

        sw.buildQuery()
                .eq("RETRY_RULE_ID", retryRuleId)
                .eq("WORK_ID", workId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("WORK_STATE", workState)
                .doUpdate(CtrRetryRuleWork.class);
    }
}

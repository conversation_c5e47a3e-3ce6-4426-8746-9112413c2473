package com.definesys.dehoop.admin.moudle.packagemanage.service;

import com.definesys.dehoop.admin.moudle.schedule.dao.ScheduleDao;
import com.definesys.dehoop.admin.moudle.schedule.pojo.SchWorkStates;
import com.definesys.dehoop.admin.moudle.schedule.pojo.SchWorkVersions;
import com.definesys.dehoop.api.constant.WorkPublishStatusConstants;
import com.definesys.dehoop.api.constant.WorkTypeConstants;
import com.definesys.dehoop.api.enums.OutlineWorksType;
import com.definesys.dehoop.api.exception.JobsExceptionEnum;

import com.xdap.motor.exception.XDapBizException;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class PublishStateDefaultService implements PublishStateService {

    protected final ScheduleDao scheduleDao;

    @Override
    public void submit(String id) {

        // 如果作业的状态是待发布或者已发布则不可再次提交
        SchWorkStates schWorkState = scheduleDao.getSchWorkState(WorkTypeConstants.WORK, id);
        if (WorkPublishStatusConstants.UN_PUBLISH.equals(schWorkState.getState())) {
            throw new XDapBizException(JobsExceptionEnum.WORK_IN_SUBMITTED_OR_PUBLISHED);
        }
        scheduleDao.updateWorkState(schWorkState.getId(), WorkPublishStatusConstants.SUBMITTED);
    }

    @Override
    public SchWorkVersions generateVersion(String id) {
        return new SchWorkVersions();
    }

    @Override
    public OutlineWorksType getNodeType() {
        return OutlineWorksType.DEFAULT_TYPE;
    }

    @Override
    public void rollbackConfigVersion(String version) {}
}

package com.definesys.dehoop.admin.moudle.outlinework.work;

import com.definesys.dehoop.admin.moudle.executor.ExecutableNode;
import com.definesys.dehoop.admin.moudle.executor.flow.ExecutableFlowBase;
import com.definesys.dehoop.admin.moudle.executor.job.Job;
import com.definesys.dehoop.admin.moudle.executor.job.JobProps;
import com.definesys.dehoop.admin.moudle.executor.utils.Props;
import com.definesys.dehoop.admin.moudle.outlinework.service.WorkNodeService;
import com.definesys.dehoop.api.enums.OutlineWorksType;

import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import ch.qos.logback.classic.Logger;

@Service
public class WorkNodeDefaultService extends WorkNodeService {

    @Override
    public OutlineWorksType getNodeType() {
        return OutlineWorksType.WORK;
    }

    @Override
    public Job getJob() {
        return new Job() {
            @Override
            public String getId() {
                return null;
            }

            @Override
            public void run() throws Exception {}

            @Override
            public void cancel() throws Exception {}

            @Override
            public double getProgress() throws Exception {
                return 0;
            }

            @Override
            public Props getJobGeneratedProperties() {
                return null;
            }

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public void setJobProps(JobProps jobProps) {}

            @Override
            public void setLog(Logger logger) {}

            @Override
            public void setNode(ExecutableNode node) {}

            @Override
            public void setFlow(ExecutableFlowBase flow) {}

            @Override
            public void setApplicationContext(ApplicationContext applicationContext) {}
        };
    }
}

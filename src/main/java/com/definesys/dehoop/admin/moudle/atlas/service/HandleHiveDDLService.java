package com.definesys.dehoop.admin.moudle.atlas.service;

import com.definesys.dehoop.admin.moudle.atlas.pojo.dto.AtlasColumnDto;
import com.definesys.dehoop.admin.moudle.atlas.pojo.dto.ParseSqlResultDto;
import com.definesys.dehoop.admin.moudle.atlas.utils.AtlasUtils;
import com.definesys.dehoop.admin.moudle.connect.pojo.ConnectInfo;
import com.definesys.dehoop.admin.moudle.connect.pojo.TableColumn;
import com.definesys.dehoop.admin.moudle.connect.pojo.TableInfo;
import com.definesys.dehoop.admin.moudle.connect.service.DataBaseFactory;
import com.definesys.dehoop.admin.moudle.connect.service.DataBaseService;
import com.definesys.dehoop.admin.moudle.log.dao.LogDao;
import com.definesys.dehoop.admin.moudle.metadata.map.MetaDataRank;
import com.definesys.dehoop.admin.moudle.outlinework.ddlvisual.pojo.dto.JobDdlvFieldsInfoDto;
import com.definesys.dehoop.admin.moudle.tableasset.dao.TableAssetDao;
import com.definesys.dehoop.admin.moudle.tableasset.pojo.ResTableCycles;
import com.definesys.dehoop.admin.moudle.tableasset.pojo.ResTableInfos;
import com.definesys.dehoop.admin.moudle.tableasset.pojo.ResTableLogs;
import com.definesys.dehoop.admin.moudle.tableasset.pojo.dto.AlterColumnInfo;
import com.definesys.dehoop.admin.moudle.tableasset.pojo.dto.AlterTableInfo;
import com.definesys.dehoop.api.constant.AtlasEntityAttrConstant;
import com.definesys.dehoop.api.constant.AtlasEntityTypeConstant;
import com.definesys.dehoop.api.entity.JdbcInfo;
import com.definesys.dehoop.api.enums.DataBaseType;
import com.definesys.dehoop.api.enums.ExecuteType;
import com.definesys.dehoop.api.enums.MetaDataType;
import com.definesys.dehoop.api.enums.MetadataRankEventType;
import com.definesys.dehoop.api.properties.AtlasProperties;
import com.definesys.dehoop.api.utils.JdbcUtils;
import com.definesys.mpaas.common.adapter.UserProfile;
import com.definesys.mpaas.query.session.MpaasSession;

import com.xdap.motor.entity.SnowflakeIdWorker;
import com.xdap.motor.utils.StringUtils;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import org.apache.atlas.model.instance.AtlasEntity;
import org.apache.atlas.model.instance.AtlasRelatedObjectId;
import org.apache.commons.collections.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class HandleHiveDDLService {

    private final AtlasUtilService atlasUtilService;

    private final AtlasProperties atlasProperties;

    private final TableAssetDao tableAssetDao;

    private final LogDao logDao;

    private final SnowflakeIdWorker snowflakeIdWorker;

    /**
     * 处理各种ddl解析后的结果 目前支持以下ddl操作: 创建表 重命名表 修改表描述 删除表 添加字段 更新字段 删除字段 添加分区 删除分区
     *
     * @param parseSqlResult 解析结果
     * @return 如果是建表语句需要返回表guid
     */
    public String handleDDLAfterParse(
            ParseSqlResultDto parseSqlResult,
            ConnectInfo connectInfo,
            List<JobDdlvFieldsInfoDto> fieldsInfo,
            ResTableInfos resTableInfos,
            Date createDate) {

        if (parseSqlResult.getExecuteType() == null || connectInfo == null) {
            return null;
        }

        // 获取数据库相关信息
        JdbcInfo jdbcInfo = JdbcUtils.parseJdbcUrl(connectInfo.getJdbcUrl());
        parseSqlResult.setDbName(
                jdbcInfo == null || jdbcInfo.getDbName() == null ? "default" : jdbcInfo.getDbName());
        parseSqlResult.setDbQualifiedName(
                AtlasUtils.genHiveDbQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getDbName(),
                        atlasProperties.getClusterName()));
        parseSqlResult.setTenantId(MpaasSession.getUserProfile().getTenantId());

        // 获取元数据信息
        if (!ExecuteType.DROP_TABLE.equals(parseSqlResult.getExecuteType())) {
            this.getHiveMetaData(parseSqlResult, connectInfo, fieldsInfo, resTableInfos);
        }

        switch (parseSqlResult.getExecuteType()) {
            case CREATE_TABLE:
                return this.createTableEvent(parseSqlResult, resTableInfos, createDate);
            case RENAME_TABLE:
                this.renameTableEvent(parseSqlResult, resTableInfos, createDate);
                break;
            case DROP_TABLE:
                this.dropTableEvent(parseSqlResult, resTableInfos, createDate);
                break;
            case ADD_COLS:
                this.addColumn(parseSqlResult, resTableInfos, createDate);
                break;
            case ALTER_COLS:
                this.alterColumn(parseSqlResult, resTableInfos, createDate);
                break;
            case REPLACE_COLS:
                this.replaceColumn(parseSqlResult, resTableInfos, createDate);
                break;
            case ALTER_TABLE_COMMENT:
                this.alterTableEvent(
                        parseSqlResult, resTableInfos, ExecuteType.ALTER_TABLE.name(), createDate);
                break;
            case ADD_PARTITION:
                this.alterTableEvent(
                        parseSqlResult, resTableInfos, ExecuteType.ADD_PARTITION.name(), createDate);
                break;
            case DEL_PARTITION:
                this.alterTableEvent(
                        parseSqlResult, resTableInfos, ExecuteType.DEL_PARTITION.name(), createDate);
                break;
        }

        return null;
    }

    /**
     * 创建表
     *
     * @param parseSqlResult 解析结果
     */
    public String createTableEvent(
            ParseSqlResultDto parseSqlResult, ResTableInfos resTableInfo, Date createDate) {

        // 设置表唯一标识
        parseSqlResult.setTableQualifiedName(
                AtlasUtils.genHiveTableQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getSourceId(),
                        parseSqlResult.getDbName(),
                        parseSqlResult.getTableName()));

        // 设置字段唯一标识
        if (CollectionUtils.isNotEmpty(parseSqlResult.getAtlasColumn())) {
            parseSqlResult
                    .getAtlasColumn()
                    .forEach(
                            e -> {
                                e.setQualifiedName(
                                        AtlasUtils.genHiveColumnQualifiedName(
                                                parseSqlResult.getTenantId(),
                                                parseSqlResult.getDbName(),
                                                parseSqlResult.getTableName(),
                                                e.getName(),
                                                atlasProperties.getClusterName()));
                            });
        }

        String tableGuid = atlasUtilService.createHiveTableAndColumn(parseSqlResult, resTableInfo);
        if (StringUtils.isNotBlank(tableGuid)) {
            ResTableLogs resTableLogs = new ResTableLogs();
            resTableLogs.setId(snowflakeIdWorker.nextId());
            resTableLogs.setExecutor(MpaasSession.getCurrentUser());
            resTableLogs.setContent(parseSqlResult.getChangelog());
            resTableLogs.setType(ExecuteType.CREATE_TABLE.name());
            resTableLogs.setTableGuid(tableGuid);
            resTableLogs.setTableId(resTableInfo.getId());
            resTableLogs.setCreationDate(createDate);

            AlterTableInfo alterTableInfo =
                    AlterTableInfo.builder().createTableName(parseSqlResult.getTableName()).build();

            // 记录表修改记录
            resTableLogs.setChangeInfo(JSON.toJSONString(alterTableInfo));
            logDao.addTableLog(resTableLogs);

            // 通过tableId将之前的tableLog的guid修改成新的guid
            logDao.updateGuidByTableId(resTableInfo.getId(), tableGuid);
        }

        return tableGuid;
    }

    /**
     * 重命名表
     *
     * @param parseSqlResult 解析结果
     */
    public void renameTableEvent(
            ParseSqlResultDto parseSqlResult, ResTableInfos tableInfo, Date createDate) {

        String oldQualifiedName =
                AtlasUtils.genHiveTableQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getSourceId(),
                        parseSqlResult.getDbName(),
                        parseSqlResult.getTableName());

        String newQualifiedName =
                AtlasUtils.genHiveTableQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getSourceId(),
                        parseSqlResult.getDbName(),
                        parseSqlResult.getNewTableName());

        String tableId = null;
        String tableGuid =
                atlasUtilService.updateTableName(
                        parseSqlResult.getNewTableName(),
                        oldQualifiedName,
                        newQualifiedName,
                        DataBaseType.Hive);
        if (tableInfo != null) {
            if (StringUtils.isBlank(tableGuid)) {
                tableGuid = tableInfo.getAtlasGuid();
            }
            tableId = tableInfo.getId();
        }

        // guid和tableId都不存在保存的操作日志无法找回，属于废数据
        if (StringUtils.isNotBlank(tableGuid) || StringUtils.isNotBlank(tableId)) {

            ResTableLogs resTableLogs = new ResTableLogs();
            resTableLogs.setId(snowflakeIdWorker.nextId());
            resTableLogs.setExecutor(MpaasSession.getCurrentUser());
            resTableLogs.setContent(parseSqlResult.getChangelog());
            resTableLogs.setType(ExecuteType.ALTER_TABLE.name());
            resTableLogs.setTableGuid(tableGuid);
            resTableLogs.setTableId(tableId);
            resTableLogs.setCreationDate(createDate);
            AlterTableInfo alterTableInfo =
                    AlterTableInfo.builder()
                            .oldTableName(parseSqlResult.getTableName())
                            .newTableName(parseSqlResult.getNewTableName())
                            .build();
            resTableLogs.setChangeInfo(JSON.toJSONString(alterTableInfo));
            logDao.addTableLog(resTableLogs);
        }

        updateMetadata(parseSqlResult, tableInfo, tableGuid);
    }

    /**
     * 修改表描述、添加/删除分区后更新元数据
     *
     * @param parseSqlResult 解析结果
     */
    public void alterTableEvent(
            ParseSqlResultDto parseSqlResult, ResTableInfos tableInfo, String type, Date createDate) {

        String qualifiedName =
                AtlasUtils.genHiveTableQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getSourceId(),
                        parseSqlResult.getDbName(),
                        parseSqlResult.getTableName());

        String tableId = null;
        String tableGuid =
                atlasUtilService.searchGuidByQualifiedName(
                        AtlasEntityTypeConstant.dehoopHiveTable, qualifiedName);
        if (tableInfo != null) {
            if (StringUtils.isBlank(tableGuid)) {
                tableGuid = tableInfo.getAtlasGuid();
            }
            tableId = tableInfo.getId();
        }

        // guid和tableId都不存在保存的操作日志无法找回，属于废数据
        if (StringUtils.isNotBlank(tableGuid) || StringUtils.isNotBlank(tableId)) {

            ResTableLogs resTableLogs = new ResTableLogs();
            resTableLogs.setId(snowflakeIdWorker.nextId());
            resTableLogs.setExecutor(MpaasSession.getCurrentUser());
            resTableLogs.setContent(parseSqlResult.getChangelog());
            resTableLogs.setType(type);
            resTableLogs.setTableGuid(tableGuid);
            resTableLogs.setTableId(tableId);
            resTableLogs.setCreationDate(createDate);
            logDao.addTableLog(resTableLogs);
        }

        updateMetadata(parseSqlResult, tableInfo, tableGuid);
    }

    /**
     * 删除表
     *
     * @param parseSqlResult 解析结果
     */
    public void dropTableEvent(
            ParseSqlResultDto parseSqlResult, ResTableInfos tableInfo, Date createDate) {

        String qualifiedName =
                AtlasUtils.genHiveTableQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getSourceId(),
                        parseSqlResult.getDbName(),
                        parseSqlResult.getTableName());

        String tableId = null;
        String tableGuid =
                atlasUtilService.dropTable(AtlasEntityTypeConstant.dehoopHiveTable, qualifiedName);

        // guid和tableId都不存在保存的操作日志无法找回，属于废数据
        if (StringUtils.isBlank(tableGuid) && tableInfo == null) {
            return;
        }
        if (tableInfo != null) {
            tableId = tableInfo.getId();
        }
        if (StringUtils.isBlank(tableGuid)) {
            tableGuid = tableInfo.getAtlasGuid();
        } else {
            // 删除redis中记录
            removeTable(tableGuid);
        }

        ResTableLogs resTableLogs = new ResTableLogs();
        resTableLogs.setId(snowflakeIdWorker.nextId());
        resTableLogs.setExecutor(MpaasSession.getCurrentUser());
        resTableLogs.setContent(parseSqlResult.getChangelog());
        resTableLogs.setType(ExecuteType.DROP_TABLE.name());
        resTableLogs.setTableGuid(tableGuid);
        resTableLogs.setTableId(tableId);
        resTableLogs.setCreationDate(createDate);

        AlterTableInfo alterTableInfo =
                AlterTableInfo.builder().oldTableName(parseSqlResult.getTableName()).build();

        // 记录表修改记录
        resTableLogs.setChangeInfo(JSON.toJSONString(alterTableInfo));
        logDao.addTableLog(resTableLogs);

        if (StringUtils.isBlank(tableId) && StringUtils.isNotBlank(tableGuid)) {
            ResTableInfos finalTableInfo = tableAssetDao.getTableAssetByGuid(tableGuid);
            if (finalTableInfo != null) {
                tableId = finalTableInfo.getId();
            }
        }

        // 删除表的时候将tableLog的tableId赋值,后续可使用tableId获取旧日志
        if (StringUtils.isNotBlank(tableGuid)) {
            logDao.updateTableIdByGuid(tableGuid, tableId);
            tableAssetDao.removeTableRowsAndSizeAndGuid(tableGuid);
        }
    }

    @MetaDataRank(
            value = MetaDataType.TABLE,
            eventType = MetadataRankEventType.REMOVE,
            fieldName = "tableGuid")
    public void removeTable(String tableGuid) {
        log.info("移除表" + tableGuid);
    }

    /**
     * 添加表字段
     *
     * @param parseSqlResult 解析结果
     */
    public void addColumn(
            ParseSqlResultDto parseSqlResult, ResTableInfos tableInfo, Date createDate) {

        parseSqlResult.setTableQualifiedName(
                AtlasUtils.genHiveTableQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getSourceId(),
                        parseSqlResult.getDbName(),
                        parseSqlResult.getTableName()));

        String tableGuid =
                atlasUtilService.searchGuidByQualifiedName(
                        AtlasEntityTypeConstant.dehoopHiveTable, parseSqlResult.getTableQualifiedName());

        if (StringUtils.isNotBlank(tableGuid) || tableInfo != null) {
            ResTableLogs resTableLogs = new ResTableLogs();
            resTableLogs.setId(snowflakeIdWorker.nextId());
            resTableLogs.setExecutor(MpaasSession.getCurrentUser());
            resTableLogs.setType(ExecuteType.ALTER_TABLE.name());
            List<AlterColumnInfo> alterColumnInfoList = new ArrayList<>();
            parseSqlResult
                    .getAtlasColumn()
                    .forEach(
                            col -> {
                                AlterColumnInfo alterColumnInfo = new AlterColumnInfo();

                                alterColumnInfo.setNewColumnName(col.getName());
                                alterColumnInfo.setNewColumnComment(col.getComment());
                                alterColumnInfo.setNewColumnType(col.getType());
                                alterColumnInfoList.add(alterColumnInfo);
                            });
            resTableLogs.setCreationDate(createDate);
            AlterTableInfo alterTableInfo =
                    AlterTableInfo.builder().alterColumnInfoList(alterColumnInfoList).build();
            resTableLogs.setContent(parseSqlResult.getChangelog());
            resTableLogs.setChangeInfo(JSON.toJSONString(alterTableInfo));
            resTableLogs.setTableGuid(tableGuid);
            resTableLogs.setTableId(tableInfo == null ? null : tableInfo.getId());
            logDao.addTableLog(resTableLogs);
        }

        updateMetadata(parseSqlResult, tableInfo, tableGuid);
    }

    /**
     * 修改字段
     *
     * @param parseSqlResult 解析结果
     */
    public void alterColumn(
            ParseSqlResultDto parseSqlResult, ResTableInfos tableInfo, Date createDate) {

        // 旧字段唯一标识
        parseSqlResult.setColumnQualifiedName(
                AtlasUtils.genHiveColumnQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getDbName(),
                        parseSqlResult.getTableName(),
                        parseSqlResult.getOldCol(),
                        atlasProperties.getClusterName()));

        // 新字段唯一标识
        parseSqlResult.setNewColumnQualifiedName(
                AtlasUtils.genHiveColumnQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getDbName(),
                        parseSqlResult.getTableName(),
                        parseSqlResult.getNewCol(),
                        atlasProperties.getClusterName()));

        // 表唯一标识
        parseSqlResult.setTableQualifiedName(
                AtlasUtils.genHiveTableQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getSourceId(),
                        parseSqlResult.getDbName(),
                        parseSqlResult.getTableName()));

        String tableGuid =
                atlasUtilService.searchGuidByQualifiedName(
                        AtlasEntityTypeConstant.dehoopHiveTable, parseSqlResult.getTableQualifiedName());
        String tableId = tableInfo == null ? null : tableInfo.getId();
        updateTableColumn(parseSqlResult, tableGuid, tableId, createDate);

        updateMetadata(parseSqlResult, tableInfo, tableGuid);
    }

    /**
     * replace 字段 目前只考虑drop字段的行为
     *
     * @param parseSqlResult 解析结果
     */
    public void replaceColumn(
            ParseSqlResultDto parseSqlResult, ResTableInfos tableInfo, Date createDate) {

        String tableQualifiedName =
                AtlasUtils.genHiveTableQualifiedName(
                        parseSqlResult.getTenantId(),
                        parseSqlResult.getSourceId(),
                        parseSqlResult.getDbName(),
                        parseSqlResult.getTableName());

        parseSqlResult.setTableQualifiedName(tableQualifiedName);

        String tableGuid =
                atlasUtilService.searchGuidByQualifiedName(
                        AtlasEntityTypeConstant.dehoopHiveTable, parseSqlResult.getTableQualifiedName());
        String tableId = tableInfo == null ? null : tableInfo.getId();
        replaceTableColumn(parseSqlResult, tableGuid, tableId, createDate);
        updateMetadata(parseSqlResult, tableInfo, tableGuid);
    }

    public void getHiveMetaData(
            ParseSqlResultDto parseSqlResult,
            ConnectInfo connectInfo,
            List<JobDdlvFieldsInfoDto> fieldsInfo,
            ResTableInfos resTableInfos) {
        DataBaseService dataBaseService = DataBaseFactory.getDatabase(connectInfo.getDataBaseType());
        connectInfo.setTableName(resTableInfos.getName());
        TableColumn tableColumn = dataBaseService.queryTableColumn(connectInfo);
        parseSqlResult.transColumnInfoToAtlasColumn(tableColumn, fieldsInfo);
        TableInfo tableInfo = dataBaseService.getDetailTableInfo(connectInfo);
        parseSqlResult.setLocation(tableInfo.getLocation());
        parseSqlResult.setFileType(tableInfo.getFileType().name());
        parseSqlResult.setTableType(tableInfo.getTableType());
        parseSqlResult.setComment(tableInfo.getComment());
        resTableInfos.setDataLength(tableInfo.getTotalSize());
        resTableInfos.setTotalRows(tableInfo.getNumRows());
        resTableInfos.setLastDdlTime(tableInfo.getLastTransientTime());
        resTableInfos.setLastModifyTime(tableInfo.getLastModifyTime());
        resTableInfos.setCreationDate(tableInfo.getCreateTime());
        // 更新res_table_info表中的数据
        tableAssetDao.updateTableInfos(
                resTableInfos.getId(),
                tableInfo.getTotalSize(),
                tableInfo.getNumRows(),
                tableInfo.getComment());
    }

    /**
     * 处理添加表的操作
     *
     * @param resTableInfos 表信息
     * @param connectInfo 连接信息
     */
    @Async
    public void handleCreateTableEvent(
            ResTableInfos resTableInfos,
            ConnectInfo connectInfo,
            List<JobDdlvFieldsInfoDto> fieldsInfo,
            Boolean isOverWrite,
            String createSql,
            UserProfile userProfile) {
        MpaasSession.setUserProfile(userProfile);

        if (!DataBaseType.Hive.equals(connectInfo.getDataBaseType())) {
            return;
        }

        ParseSqlResultDto sqlResultDto = new ParseSqlResultDto();
        sqlResultDto.setTableName(resTableInfos.getName());
        sqlResultDto.setSourceId(resTableInfos.getSourceId());

        try {
            // 创建并覆盖操作先drop_table
            if (isOverWrite) {
                sqlResultDto.setChangelog("删除表: " + resTableInfos.getName());
                sqlResultDto.setExecuteType(ExecuteType.DROP_TABLE);
                this.handleDDLAfterParse(sqlResultDto, connectInfo, fieldsInfo, resTableInfos, new Date());
            }
            sqlResultDto.setChangelog("创建表: " + resTableInfos.getName() + ";\n" + createSql);
            sqlResultDto.setExecuteType(ExecuteType.CREATE_TABLE);
            this.handleDDLAfterParse(sqlResultDto, connectInfo, fieldsInfo, resTableInfos, new Date());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /*
     * 更新表元数据
     */
    private void updateMetadata(
            ParseSqlResultDto parseSqlResult, ResTableInfos tableInfo, String tableGuid) {

        if (StringUtils.isNotBlank(tableGuid)
                && CollectionUtils.isNotEmpty(parseSqlResult.getAtlasColumn())) {

            // 设置字段唯一标识
            parseSqlResult
                    .getAtlasColumn()
                    .forEach(
                            e -> {
                                e.setQualifiedName(
                                        AtlasUtils.genHiveColumnQualifiedName(
                                                parseSqlResult.getTenantId(),
                                                parseSqlResult.getDbName(),
                                                parseSqlResult.getTableName(),
                                                e.getName(),
                                                atlasProperties.getClusterName()));
                            });
            try {
                // 更新表和字段的元数据
                atlasUtilService.updateHiveTableInfo(tableGuid, parseSqlResult, tableInfo);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            // 更新存储量大小
            ResTableCycles metaTableCycle = new ResTableCycles();
            metaTableCycle.setAtlasGuid(tableGuid);
            metaTableCycle.setGenDate(new Date());
            metaTableCycle.setTotalNum(tableInfo.getTotalRows());
            metaTableCycle.setTotalSize(tableInfo.getDataLength());
            metaTableCycle.setTableId(tableInfo.getId());
            metaTableCycle.setId(snowflakeIdWorker.nextId());
            tableAssetDao.addTableCycle(metaTableCycle);
        }
    }

    /**
     * 更新表column
     *
     * @param atlasDto 解析参数
     */
    public void updateTableColumn(
            ParseSqlResultDto atlasDto, String tableGuid, String tableId, Date createDate) {

        String columnGuid =
                atlasUtilService.searchGuidByQualifiedName(
                        AtlasEntityTypeConstant.hiveColumn, atlasDto.getColumnQualifiedName());
        AtlasEntity columnEntity = atlasUtilService.getAtlasEntityByGuid(columnGuid);

        String changeLog = null;

        if (columnEntity != null) {

            // 获取变更前字段属性
            String oldName = String.valueOf(columnEntity.getAttribute(AtlasEntityAttrConstant.ATTR_NAME));
            String oldType = String.valueOf(columnEntity.getAttribute(AtlasEntityAttrConstant.type));
            String oldComment =
                    String.valueOf(columnEntity.getAttribute(AtlasEntityAttrConstant.ATTR_COMMENT));

            // 设置字段唯一标识
            columnEntity.setAttribute(
                    AtlasEntityAttrConstant.qualifiedName, atlasDto.getNewColumnQualifiedName());

            // 设置新字段名
            columnEntity.setAttribute(AtlasEntityAttrConstant.ATTR_NAME, atlasDto.getNewCol());
            if (!oldName.equalsIgnoreCase(atlasDto.getNewCol())) {
                changeLog = "将字段名称" + oldName + "修改为" + atlasDto.getNewCol() + ";";
            }

            // 设置新字段类型
            if (atlasDto.getNewColType() != null) {
                columnEntity.setAttribute(AtlasEntityAttrConstant.type, atlasDto.getNewColType());
                if (!atlasDto.getNewColType().equalsIgnoreCase(oldType)) {
                    changeLog =
                            changeLog
                                    + "将字段"
                                    + atlasDto.getNewCol()
                                    + "类型"
                                    + oldType
                                    + "修改为"
                                    + atlasDto.getNewColType()
                                    + ";";
                }
            }

            // 设置新字段描述
            if (atlasDto.getNewColComment() != null) {
                columnEntity.setAttribute(
                        AtlasEntityAttrConstant.ATTR_COMMENT, atlasDto.getNewColComment());
                if (!atlasDto.getNewColComment().equalsIgnoreCase(oldComment)) {
                    changeLog =
                            changeLog
                                    + "将字段"
                                    + atlasDto.getNewCol()
                                    + "的描述"
                                    + oldComment
                                    + "修改为"
                                    + atlasDto.getNewColComment()
                                    + ";";
                }
            }
            atlasUtilService.updateEntity(columnEntity);
        }

        if (StringUtils.isNotBlank(tableGuid) || StringUtils.isNotBlank(tableId)) {
            ResTableLogs resTableLogs = new ResTableLogs();
            resTableLogs.setId(snowflakeIdWorker.nextId());
            resTableLogs.setExecutor(MpaasSession.getCurrentUser());
            resTableLogs.setContent(changeLog);
            resTableLogs.setType(ExecuteType.ALTER_TABLE.name());
            AlterColumnInfo alterColumnInfo = new AlterColumnInfo();
            alterColumnInfo.setNewColumnName(atlasDto.getNewCol());
            alterColumnInfo.setNewColumnComment(atlasDto.getNewColComment());
            alterColumnInfo.setNewColumnType(atlasDto.getNewColType());
            alterColumnInfo.setOldColumnName(atlasDto.getOldCol());
            alterColumnInfo.setOldColumnType(
                    columnEntity.getAttribute(AtlasEntityAttrConstant.type).toString());
            alterColumnInfo.setOldColumnComment(
                    columnEntity.getAttribute(AtlasEntityAttrConstant.ATTR_COMMENT).toString());
            List<AlterColumnInfo> alterColumnInfoList = new ArrayList<>();
            alterColumnInfoList.add(alterColumnInfo);
            AlterTableInfo alterTableInfo =
                    AlterTableInfo.builder().alterColumnInfoList(alterColumnInfoList).build();
            resTableLogs.setChangeInfo(JSON.toJSONString(alterTableInfo));
            resTableLogs.setTableGuid(tableGuid);
            resTableLogs.setTableId(tableId);
            resTableLogs.setCreationDate(createDate);
            logDao.addTableLog(resTableLogs);
        }
    }

    /**
     * replace表字段 只考虑drop字段的情况
     *
     * @param atlasDto 解析结果
     */
    public void replaceTableColumn(
            ParseSqlResultDto atlasDto, String tableGuid, String tableId, Date createDate) {

        AtlasEntity tableEntity = atlasUtilService.getAtlasEntityByGuid(tableGuid);

        if (tableEntity == null
                || tableEntity.getRelationshipAttribute(AtlasEntityAttrConstant.ATTR_COLUMNS) == null) {
            return;
        }

        List<String> newColumnName =
                atlasDto.getAtlasColumn().stream()
                        .map(AtlasColumnDto::getName)
                        .collect(Collectors.toList());

        String relatedColumnString =
                JSON.toJSONString(
                        tableEntity.getRelationshipAttribute(AtlasEntityAttrConstant.ATTR_COLUMNS));

        List<AtlasRelatedObjectId> oldColumn =
                JSONArray.parseArray(relatedColumnString, AtlasRelatedObjectId.class);

        List<String> oldColumnName = new ArrayList<>();

        List<AlterColumnInfo> alterColumnInfoList = new ArrayList<>();

        StringBuilder changeLog = new StringBuilder();

        oldColumn.forEach(
                e -> {
                    e.setDisplayText(e.getDisplayText().toLowerCase());
                    oldColumnName.add(e.getDisplayText());
                    if (!newColumnName.contains(e.getDisplayText())) {
                        AlterColumnInfo metaAlterColumnInfo = new AlterColumnInfo();
                        metaAlterColumnInfo.setDropColumnName(e.getDisplayText());
                        alterColumnInfoList.add(metaAlterColumnInfo);
                        changeLog.append("删除字段:").append(e.getDisplayText()).append(";");
                    }
                });

        Set<String> allColumn = new HashSet<>();
        allColumn.addAll(newColumnName);
        allColumn.addAll(oldColumnName);
        oldColumnName.forEach(allColumn::remove);
        allColumn.forEach(
                e -> {
                    AlterColumnInfo metaAlterColumnInfo = new AlterColumnInfo();
                    metaAlterColumnInfo.setAddColumnName(e);
                    alterColumnInfoList.add(metaAlterColumnInfo);
                    changeLog.append("新增字段:").append(e).append(";");
                });

        if (CollectionUtils.isEmpty(alterColumnInfoList)) {
            return;
        }

        AlterTableInfo alterTableInfo =
                AlterTableInfo.builder().alterColumnInfoList(alterColumnInfoList).build();
        ResTableLogs resTableLogs = new ResTableLogs();
        resTableLogs.setId(snowflakeIdWorker.nextId());
        resTableLogs.setExecutor(MpaasSession.getCurrentUser());
        resTableLogs.setType(ExecuteType.ALTER_TABLE.name());
        resTableLogs.setTableGuid(tableGuid);
        resTableLogs.setTableId(tableId);
        resTableLogs.setChangeInfo(JSON.toJSONString(alterTableInfo));
        resTableLogs.setContent(String.valueOf(changeLog));
        resTableLogs.setCreationDate(createDate);
        logDao.addTableLog(resTableLogs);
    }
}

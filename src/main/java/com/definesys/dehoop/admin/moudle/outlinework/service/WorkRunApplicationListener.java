package com.definesys.dehoop.admin.moudle.outlinework.service;

import com.definesys.dehoop.admin.moudle.log.dao.LogDao;
import com.definesys.dehoop.admin.moudle.log.pojo.dto.LogInfo;
import com.definesys.dehoop.admin.moudle.log.service.LogServiceNew;
import com.definesys.dehoop.admin.moudle.outlinework.dao.OutlineWorkDao;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobFlowInstances;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.dto.WorkLinkDto;
import com.definesys.dehoop.admin.moudle.outlinework.work.OutlineWorkService;
import com.definesys.dehoop.api.constant.FlowConstants;
import com.definesys.dehoop.api.constant.WorkRunStatusConstants;
import com.definesys.dehoop.api.constant.WorkTypeConstants;
import com.definesys.mpaas.query.session.MpaasSession;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.jetbrains.annotations.NotNull;

/** 工作流作业运行监听事件 */
@Slf4j
@Component
public class WorkRunApplicationListener implements ApplicationListener<WorkRunEvent> {

    private final ApplicationContext applicationContext;

    private final OutlineWorkDao outlineWorkDao;

    private final OutlineWorkService outlineWorkService;

    private final LogDao logDao;

    private final LogServiceNew logServiceNew;

    public WorkRunApplicationListener(
            ApplicationContext applicationContext,
            OutlineWorkDao outlineWorkDao,
            OutlineWorkService outlineWorkService,
            LogDao logDao,
            LogServiceNew logServiceNew) {

        this.applicationContext = applicationContext;
        this.outlineWorkDao = outlineWorkDao;
        this.outlineWorkService = outlineWorkService;
        this.logDao = logDao;
        this.logServiceNew = logServiceNew;
    }

    @Async
    @Override
    public void onApplicationEvent(@NotNull WorkRunEvent workRunEvent) {

        // 设置当前执行人
        MpaasSession.setUserProfile(workRunEvent.getUserProfile());

        // 获取整个工作流的节点
        List<WorkLinkDto> flowLinks = workRunEvent.getFlowLinks();
        // 当前作业的id
        String currentRunWorkId = workRunEvent.getWorkId();
        // 工作流实例
        String instanceId = workRunEvent.getInstanceId();

        // 判断当前作业是否可运行
        JobFlowInstances nextWorkInstance =
                outlineWorkDao.getFlowInstance(currentRunWorkId, instanceId);
        if (!WorkRunStatusConstants.PENDING.equals(nextWorkInstance.getState())) {
            return;
        }

        // 计算子作业 和 父作业有哪些
        List<String> sonWorkIds = new ArrayList<>();
        List<String> parentWorkIds = new ArrayList<>();
        List<String> allWorks = new ArrayList<>();
        flowLinks.forEach(
                e -> {
                    if (e.getFrom().equals(currentRunWorkId)) {
                        sonWorkIds.add(e.getTo());
                    }
                    if (e.getTo().equals(currentRunWorkId)) {
                        parentWorkIds.add(e.getFrom());
                    }
                    allWorks.add(e.getFrom());
                    allWorks.add(e.getTo());
                });

        // 查询是否满足运行条件
        if (!FlowConstants.START_WORK.equals(currentRunWorkId)) {
            // 查看父级作业，是否存在运行中的或者等待的作业
            List<JobFlowInstances> jobFlowInstances =
                    outlineWorkDao.queryWorkInstances(instanceId, parentWorkIds);
            List<String> workStates =
                    jobFlowInstances.stream().map(JobFlowInstances::getState).collect(Collectors.toList());
            if (workStates.contains(WorkRunStatusConstants.PENDING)
                    || workStates.contains(WorkRunStatusConstants.DOING)) {
                // 存在则不运行
                return;
            } else if (workStates.contains(WorkRunStatusConstants.ERROR)) {
                // 如果有一个失败则全失败
                workRunEvent.setPrevRunSuccess(false);
            }
        }

        // 如果上一个失败,下一个作业直接失败
        if (!workRunEvent.getPrevRunSuccess()) {
            outlineWorkDao.updateFlowInstance(instanceId, currentRunWorkId, WorkRunStatusConstants.ERROR);
            if (sonWorkIds.isEmpty()) {
                // 如果子作业为空 则修改工作流的状态
                List<JobFlowInstances> allWorkInstance =
                        outlineWorkDao.queryWorkInstances(instanceId, allWorks);
                List<String> allWorkStatus =
                        allWorkInstance.stream().map(JobFlowInstances::getState).collect(Collectors.toList());
                if (!allWorkStatus.contains(WorkRunStatusConstants.DOING)
                        && !allWorkStatus.contains(WorkRunStatusConstants.PENDING)) {
                    logDao.updateWorkLog(instanceId, "", WorkRunStatusConstants.ERROR);
                }
            } else {
                // 递归推送子任务，状态全部为失败
                sonWorkIds.forEach(
                        e -> {
                            WorkRunEvent nextWorkRunEvent =
                                    new WorkRunEvent(
                                            this, e, flowLinks, instanceId, false, workRunEvent.getUserProfile());
                            applicationContext.publishEvent(nextWorkRunEvent);
                        });
            }
            return;
        }

        // 运行作业
        String workRunStatus;
        if (FlowConstants.START_WORK.equals(currentRunWorkId)) {
            workRunStatus = WorkRunStatusConstants.SUCCESS;
        } else {
            workRunStatus = outlineWorkService.executeWork(instanceId, currentRunWorkId);
            // 获取作业的日志
            String executeId =
                    outlineWorkDao.getFlowInstance(currentRunWorkId, instanceId).getExecuteId();
            //            int maxTime = 0;
            while (WorkRunStatusConstants.DOING.equals(workRunStatus)) {
                try {
                    // 每个2秒,获取一个日志状态，知道不是DOING状态
                    //                    maxTime++;
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error(e.getMessage());
                }
                LogInfo logInfo = new LogInfo();

                try {
                    logInfo = logServiceNew.getLogInfo(executeId, WorkTypeConstants.CHUNJUN);
                    workRunStatus = logInfo.getState();
                } catch (Exception e) {
                    log.error(e.getMessage());
                    logInfo.setLogList(new ArrayList<>());
                    logInfo.setState(WorkRunStatusConstants.DOING);
                }

                //                if (maxTime > 600) {
                //                    workRunStatus = WorkRunStatusConstants.ERROR;
                //                    break;
                //                }
            }
        }

        // 更新当前节点运行状态
        outlineWorkDao.updateFlowInstance(instanceId, currentRunWorkId, workRunStatus);

        // 开始推送子作业
        if (sonWorkIds.isEmpty()) {
            // 修改当前作业状态 判断工作流是否运行完
            List<JobFlowInstances> allWorkInstance =
                    outlineWorkDao.queryWorkInstances(instanceId, allWorks);
            List<String> allWorkStatus =
                    allWorkInstance.stream().map(JobFlowInstances::getState).collect(Collectors.toList());
            if (!allWorkStatus.contains(WorkRunStatusConstants.DOING)
                    && !allWorkStatus.contains(WorkRunStatusConstants.PENDING)) {
                logDao.updateWorkLog(instanceId, "", workRunStatus);
            }
        } else {
            String finalWorkRunStatus = workRunStatus;
            sonWorkIds.forEach(
                    e -> {
                        WorkRunEvent nextWorkRunEvent =
                                new WorkRunEvent(
                                        this,
                                        e,
                                        flowLinks,
                                        instanceId,
                                        finalWorkRunStatus.equals(WorkRunStatusConstants.SUCCESS),
                                        workRunEvent.getUserProfile());
                        applicationContext.publishEvent(nextWorkRunEvent);
                    });
        }
    }
}

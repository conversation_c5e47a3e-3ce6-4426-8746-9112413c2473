package com.definesys.dehoop.admin.moudle.outlinework.etl.dao;

import com.definesys.dehoop.admin.moudle.outlinework.etl.pojo.entity.JobETLWorkNodeField;
import com.definesys.mpaas.query.MpaasQueryFactory;
import com.definesys.mpaas.query.session.MpaasSession;

import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年11月26日 18:17
 */
@Repository
public class JobETLWorkNodeFieldDao {

    private final MpaasQueryFactory sw;

    public JobETLWorkNodeFieldDao(MpaasQueryFactory sw) {
        this.sw = sw;
    }

    public void setJobETLWorkNodeField(JobETLWorkNodeField jobETLWorkNodeField) {
        sw.buildQuery().doInsert(jobETLWorkNodeField);
    }

    public void setJobETLWorkNodeField(List<JobETLWorkNodeField> jobETLWorkNodeFieldList) {
        sw.buildQuery().doBatchInsert(jobETLWorkNodeFieldList);
    }

    public void deleteJobETLWorkNodeFieldByNodeId(String nodeId) {
        sw.buildQuery()
                .eq("NODE_ID", nodeId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(JobETLWorkNodeField.class);
    }

    public List<JobETLWorkNodeField> queryWorkNodeFieldByNodeId(List<String> nodeIdList) {
        return sw.buildQuery().in("NODE_ID", nodeIdList).doQuery(JobETLWorkNodeField.class);
    }

    public List<JobETLWorkNodeField> queryWorkNodeFieldByNodeId(String nodeId) {
        return sw.buildQuery().eq("NODE_ID", nodeId).doQuery(JobETLWorkNodeField.class);
    }
}

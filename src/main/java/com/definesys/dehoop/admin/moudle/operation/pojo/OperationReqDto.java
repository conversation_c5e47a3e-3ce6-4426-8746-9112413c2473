package com.definesys.dehoop.admin.moudle.operation.pojo;

import com.definesys.dehoop.api.serializer.DateDeserializer;
import com.definesys.dehoop.api.serializer.DateSerializer;
import com.definesys.dehoop.api.serializer.RegexDeserializer;
import com.definesys.dehoop.api.serializer.StringNoneDeserializer;

import lombok.Data;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@Data
public class OperationReqDto {

    @JsonDeserialize(using = RegexDeserializer.class)
    private String searchWord;

    @JsonDeserialize(using = StringNoneDeserializer.class)
    private String director;

    private Integer page;

    private Integer pageSize;

    @JsonDeserialize(using = StringNoneDeserializer.class)
    private String state;

    private Date scheduleDate;

    @JsonDeserialize(using = StringNoneDeserializer.class)
    private String workId;

    @JsonDeserialize(using = StringNoneDeserializer.class)
    private String flowId;

    @JsonDeserialize(using = StringNoneDeserializer.class)
    private String flowInstanceId;

    @JsonSerialize(using = DateSerializer.class)
    @JsonDeserialize(using = DateDeserializer.class)
    private Date executeDate;

    @JsonSerialize(using = DateSerializer.class)
    @JsonDeserialize(using = DateDeserializer.class)
    private Date startDate;

    @JsonSerialize(using = DateSerializer.class)
    @JsonDeserialize(using = DateDeserializer.class)
    private Date endDate;

    @JsonDeserialize(using = StringNoneDeserializer.class)
    private String instanceType;

    private List<String> instanceIdList;

    @JsonDeserialize(using = StringNoneDeserializer.class)
    private String categoryId;

    /** 排序字段 */
    @JsonDeserialize(using = StringNoneDeserializer.class)
    private String orderField;

    /** 是否倒叙 */
    private Boolean isDesc = true;

    private String realWorkId;

    private String projectId;
}

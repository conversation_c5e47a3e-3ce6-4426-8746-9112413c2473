package com.definesys.dehoop.admin.moudle.outlinework.pojo;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.Column;
import com.definesys.mpaas.query.annotation.Style;
import com.definesys.mpaas.query.annotation.Table;
import com.definesys.mpaas.query.annotation.TenantEnable;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/2/14 17:09
 * @description 存储过程配置
 */
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "JOB_HPLSQL_CONFIGS")
@Style
@NoArgsConstructor
@AllArgsConstructor
@TenantEnable
public class JobHplSqlConfigs extends XdapBasePojo {

    @Column("ID")
    private String id;

    @Column("WORK_ID")
    private String workId;

    @Column("MAIN_ENTRY")
    private String mainEntry;
}

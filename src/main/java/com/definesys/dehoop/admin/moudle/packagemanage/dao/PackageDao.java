package com.definesys.dehoop.admin.moudle.packagemanage.dao;

import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobWorkParams;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobWorkScripts;
import com.definesys.dehoop.admin.moudle.packagemanage.pojo.PackageEntity;
import com.definesys.dehoop.admin.moudle.packagemanage.pojo.PackageResEntity;
import com.definesys.dehoop.admin.moudle.packagemanage.pojo.dto.WorkStateDto;
import com.definesys.dehoop.admin.moudle.schedule.pojo.SchWorkStates;
import com.definesys.dehoop.api.constant.DehoopConstant;
import com.definesys.dehoop.api.constant.WorkPublishStatusConstants;
import com.definesys.mpaas.query.MpaasQuery;
import com.definesys.mpaas.query.MpaasQueryFactory;
import com.definesys.mpaas.query.db.PageQueryResult;
import com.definesys.mpaas.query.session.MpaasSession;

import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/** 发布包管理Dao层 */
@Repository
public class PackageDao {

    private final MpaasQueryFactory sw;

    public PackageDao(MpaasQueryFactory sw) {
        this.sw = sw;
    }

    /**
     * 添加发布包
     *
     * @param packageEntity 发布包对象
     */
    public void addPackage(PackageEntity packageEntity) {

        sw.buildQuery().doInsert(packageEntity);
    }

    /**
     * 按名称查询发布包
     *
     * @param name 名称
     * @param projectId 项目ID
     * @return 查询结果
     */
    public PackageEntity getPackageByName(String projectId, String name) {

        return sw.buildQuery()
                .eq("PROJECT_ID", projectId)
                .eq("NAME", name)
                .doQueryFirst(PackageEntity.class);
    }

    /**
     * 删除发布包
     *
     * @param id 发布包ID
     */
    public void deletePackage(String id) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(PackageEntity.class);
    }

    /**
     * 更新发布包的状态
     *
     * @param id 发布包ID
     * @param state 发布包状态
     */
    public void updateState(String id, String state) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("STATE", state)
                .doUpdate(PackageEntity.class);
    }

    public void updatePackageRelDate(String packageId) {

        sw.buildQuery()
                .eq("ID", packageId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("REL_DATE", new Date())
                .doUpdate(PackageEntity.class);
    }

    /**
     * 查询单个发布包信息
     *
     * @param id 发布包ID
     * @return 发布包对象
     */
    public PackageEntity getPackageById(String id) {

        return sw.buildQuery().eq("ID", id).doQueryFirst(PackageEntity.class);
    }

    /**
     * 查询发布包
     *
     * @param proposerId 申请人ID
     * @param curApprorId 当前审批人ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param searchWord 搜索关键词
     * @param projectId 项目id
     * @return 查询结果
     */
    public List<PackageEntity> queryPackage(
            List<String> proposerId,
            List<String> curApprorId,
            Date startDate,
            Date endDate,
            String searchWord,
            String projectId) {

        return sw.buildQuery()
                .eq("PROJECT_ID", projectId)
                .like("NAME", searchWord)
                .and()
                .in("PROPOSER_ID", proposerId)
                .and()
                .in("CUR_APPROR_ID", curApprorId)
                .gteq("APP_DATE", startDate)
                .and()
                .lteq("APP_DATE", endDate)
                .orderBy("ID", "desc")
                .doQuery(PackageEntity.class);
    }

    public PageQueryResult<PackageEntity> pageQueryPackageByState(
            List<String> proposerId,
            List<String> curApprorId,
            Date startDate,
            Date endDate,
            String searchWord,
            String projectId,
            String state,
            Integer page,
            Integer pageSize) {

        MpaasQuery mpaasQuery =
                sw.buildQuery()
                        .eq("PROJECT_ID", projectId)
                        .like("NAME", searchWord)
                        .and()
                        .in("PROPOSER_ID", proposerId)
                        .and()
                        .in("CUR_APPROR_ID", curApprorId)
                        .gteq("APP_DATE", startDate)
                        .and()
                        .lteq("APP_DATE", endDate);

        if (DehoopConstant.toSubmit.equals(state)) {

            mpaasQuery.in(
                    "STATE",
                    new ArrayList<>(Arrays.asList(DehoopConstant.toSubmit, DehoopConstant.UN_APPROVE)));
        } else {
            mpaasQuery.eq("STATE", state);
        }

        return mpaasQuery.orderBy("ID", "desc").doPageQuery(page, pageSize, PackageEntity.class);
    }

    /**
     * 查询申请人的发布包
     *
     * @param proposerId 申请人ID
     * @return 查询结果
     */
    public List<PackageEntity> queryPackageByProposerId(String proposerId, List<String> state) {

        return sw.buildQuery()
                .tenant()
                .eq("PROPOSER_ID", proposerId)
                .and()
                .in("STATE", state)
                .doQuery(PackageEntity.class);
    }

    /**
     * 查询当前审批人下的发布包
     *
     * @param approrId 审批人ID
     * @return 查询结果
     */
    public List<PackageEntity> queryPackageByApprorId(String approrId, String state) {

        return sw.buildQuery()
                .eq("CUR_APPROR_ID", approrId)
                .eq("STATE", state)
                .doQuery(PackageEntity.class);
    }

    /**
     * 查询多个项目下的发布包
     *
     * @param projectId 项目id
     * @param state 发布包状态
     * @return 查询结果
     */
    public List<PackageEntity> queryPackageByProject(List<String> projectId, String state) {

        if (projectId.isEmpty()) {

            return new ArrayList<>();
        }

        return sw.buildQuery()
                .in("PROJECT_ID", projectId)
                .eq("STATE", state)
                .doQuery(PackageEntity.class);
    }

    /**
     * 添加发布包-作业资源关联信息
     *
     * @param packageResEntity 关联对象
     */
    public void addPackageRes(PackageResEntity packageResEntity) {

        sw.buildQuery().doInsert(packageResEntity);
    }

    /**
     * 查询作业资源
     *
     * @param workResourceId ID列表
     * @return 查询及结果
     */
    public List<SchWorkStates> queryWorkStateById(List<String> workResourceId) {

        return sw.buildQuery().in("ID", workResourceId).doQuery(SchWorkStates.class);
    }

    /**
     * 更新作业资源状态
     *
     * @param id 作业资源ID
     * @param state 状态
     */
    public void updateWorkState(String id, String state) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("STATE", state)
                .doUpdate(SchWorkStates.class);
    }

    /**
     * 按发布包ID查询关联表信息
     *
     * @param id 发布包ID
     * @return 查询结果
     */
    public List<PackageResEntity> getPackageResByPackageId(String id) {

        return sw.buildQuery().eq("PACKAGE_ID", id).doQuery(PackageResEntity.class);
    }

    /**
     * 不分类型分页查询全部的workState
     *
     * @param envId 环境ID
     * @param searchWord 搜索关键词
     * @param page 页号
     * @param pageSize 页大小
     * @return 分页查询结果
     */
    public PageQueryResult<WorkStateDto> queryAllWorkState(
            String type,
            String envId,
            String searchWord,
            String projectId,
            Integer page,
            Integer pageSize) {

        return sw.buildQuery()
                .sql(
                        "select *\n"
                                + "from (select\n"
                                + "  \tsws.ID as id,\n"
                                + "    sws.BUSINESS_ID as businessId,\n"
                                + "    sws.BUSINESS_TYPE as type,\n"
                                + "    sws.OPT_TYPE as optType,\n"
                                + "    sws.COMMENT as comment,\n"
                                + "    sws.STATE as state,\n"
                                + "    sws.FLOW_ID as flowId,\n"
                                + "    sws.LAST_UPDATED_BY as createdBy,\n"
                                + "    sws.LAST_UPDATE_DATE as creationDate,\n"
                                + "    IFNULL(jow.NAME,sws.BUSINESS_NAME) as name,\n"
                                + "    IFNULL(jow.TYPE,sws.BUSINESS_TYPE) as resType\n"
                                + "from\n"
                                + "    SCH_WORK_STATES sws\n"
                                + "left join ( \n"
                                + "    select id,name,type from JOB_OUTLINE_WORKS\n"
                                + "    union all \n"
                                + "    select id,name,type from JOB_REAL_WORKS \n"
                                + "    union all \n"
                                + "    select id,name,type from RES_RESOURCE_FILES\n"
                                + "    union all \n"
                                + "    select id,name,type from TAG_LABELS\n"
                                + "    union all \n"
                                + "    select id ,name,'FUNCTION' type from JOB_FUNCTIONS\n"
                                + "    union all\n"
                                + "    select id,name,'ENTITY' type from MODELING_ENTITY\n"
                                + "    union all\n"
                                + "    select id,name,'DATA_DIMENSION' type from MODELING_DATA_DIMENSION\n"
                                + "    union all\n"
                                + "    select id,API_NAME name,'API' type FROM DAS_APIS\n"
                                + "    union all\n"
                                + "    select id,name_cn name,'MODELING_ATOMIC_INDICATOR' type FROM MODELING_ATOMIC_INDICATOR\n"
                                + "    union all\n"
                                + "    select id,name_cn name,'MODELING_STATISTICAL_CONDITIONS' type FROM MODELING_STATISTICAL_CONDITIONS\n"
                                + "    \n"
                                + "    ) jow on sws.BUSINESS_ID = jow.ID \n"
                                + "\n"
                                + "where \n"
                                + "      ((sws.ENV_ID = #envId "
                                + "      or sws.project_id = #projectId )"
                                + "      and sws.TENANT_ID = #tenantId )"
                                + "      \n"
                                + "order by creationDate desc, id desc) alia")
                .setVar("envId", envId)
                .setVar("projectId", projectId)
                .setVar("tenantId", MpaasSession.getUserProfile().getTenantId())
                .like("name", searchWord)
                .eq("state", WorkPublishStatusConstants.UN_PUBLISH)
                .eq("type", type)
                .doPageQuery(page, pageSize, WorkStateDto.class);
    }

    /**
     * 更新状态
     *
     * @param idList id列表
     * @param state 状态
     */
    public void updateSchWorkState(List<String> idList, String state) {

        sw.buildQuery()
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .in("ID", idList)
                .update("STATE", state)
                .doUpdate(SchWorkStates.class);
    }

    /**
     * 获取发布状态的作业状态
     *
     * @param state 状态
     */
    public List<SchWorkStates> queryWorkStateByFlowId(String flowId, String state) {

        return sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("STATE", state)
                .orderBy("LAST_UPDATE_DATE", "desc")
                .doQuery(SchWorkStates.class);
    }

    /**
     * 更新状态通过工作
     *
     * @param idList
     * @param state
     */
    public void updateSchWorkStateByBusinessId(List<String> idList, String state) {

        sw.buildQuery()
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .in("BUSINESS_ID", idList)
                .update("STATE", state)
                .doUpdate(SchWorkStates.class);
    }

    /**
     * 查询该发布包下的作业资源
     *
     * @param packageId 资源ID
     * @return 查询结果
     */
    public List<PackageResEntity> queryPackResByPackId(String packageId) {

        return sw.buildQuery().eq("PACKAGE_ID", packageId).doQuery(PackageResEntity.class);
    }

    /**
     * 更新审批意见
     *
     * @param packId 发布包ID
     * @param apprOpinion 审批意见
     */
    public void updateApprOpinion(String packId, String apprOpinion) {

        if (apprOpinion == null) {
            return;
        }

        sw.buildQuery()
                .eq("ID", packId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("APPR_OPINION", apprOpinion)
                .doUpdate(PackageEntity.class);
    }

    /**
     * 查询列表ID中的作业资源
     *
     * @param workId 作业资源ID列表
     * @return 查询结果
     */
    public List<WorkStateDto> queryWorkResById(List<String> workId) {

        return sw.buildQuery()
                .sql(
                        "select * from ( select\n"
                                + "    sws.ID as id,\n"
                                + "    sws.BUSINESS_ID as businessId,\n"
                                + "    sws.BUSINESS_TYPE as type,\n"
                                + "    sws.OPT_TYPE as optType,\n"
                                + "    sws.COMMENT as comment,\n"
                                + "    sws.STATE as state,\n"
                                + "    sws.FLOW_ID as flowId,\n"
                                + "    sws.LAST_UPDATED_BY as createdBy,\n"
                                + "    sws.LAST_UPDATE_DATE as creationDate,\n"
                                + "    IFNULL(jow.NAME,sws.BUSINESS_NAME) as name,\n"
                                + "    IFNULL(jow.TYPE,sws.BUSINESS_TYPE) as resType\n"
                                + "  from SCH_WORK_STATES sws \n"
                                + "  left join \n"
                                + "    ( SELECT  id,name , type from JOB_OUTLINE_WORKS\n"
                                + "union all\n"
                                + "SELECT  id,name , type from RES_RESOURCE_FILES\n"
                                + "union all\n"
                                + "SELECT  id,name , type from TAG_LABELS\n"
                                + "union all\n"
                                + "SELECT  id,name , type from JOB_REAL_WORKS\n"
                                + "union all\n"
                                + "SELECT  id,name , 'DATA_DIMENSION' from MODELING_DATA_DIMENSION\n"
                                + "union all\n"
                                + "SELECT  id,name , 'ENTITY' from MODELING_ENTITY\n"
                                + "union all\n"
                                + "SELECT  id,API_NAME , 'API' from DAS_APIS\n"
                                + "union all\n"
                                + "SELECT  id,name_cn , 'MODELING_ATOMIC_INDICATOR' from MODELING_STATISTICAL_CONDITIONS\n"
                                + "union all\n"
                                + "SELECT  id,name_cn , 'MODELING_STATISTICAL_CONDITIONS' from MODELING_ATOMIC_INDICATOR\n"
                                + "\n"
                                + ") jow on sws.BUSINESS_ID = jow.ID "
                                + " where sws.TENANT_ID = #tenantId "
                                + ") alias")
                .setVar("tenantId", MpaasSession.getUserProfile().getTenantId())
                .in("ID", workId)
                .doQuery(WorkStateDto.class);
    }

    /**
     * 查询发布包下面的作业关联内容
     *
     * @param packId 发布包ID
     * @return 查询结果
     */
    public List<PackageResEntity> queryWorkByPackId(String packId) {

        return sw.buildQuery().eq("PACKAGE_ID", packId).doQuery(PackageResEntity.class);
    }

    /**
     * 转交审批权
     *
     * @param id 发布包ID
     * @param approrId 用户ID
     */
    public void updateAppror(String id, String approrId) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("CUR_APPROR_ID", approrId)
                .doUpdate(PackageEntity.class);
    }

    /**
     * 查询工作参数
     *
     * @param id 工作ID
     * @return 查询结果
     */
    public List<JobWorkParams> queryWorkParams(String id) {

        return sw.buildQuery().eq("WORK_ID", id).doQuery(JobWorkParams.class);
    }

    /**
     * 查询代码
     *
     * @param id 作业ID
     * @return 查询结果
     */
    public JobWorkScripts getWorkScripts(String id) {

        return sw.buildQuery().eq("WORK_ID", id).doQueryFirst(JobWorkScripts.class);
    }

    public void updatePackageWorkVersion(String packageId, String workResId, String versionId) {

        sw.buildQuery()
                .eq("PACKAGE_ID", packageId)
                .eq("WORK_RES_ID", workResId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("VERSION_ID", versionId)
                .doUpdate(PackageResEntity.class);
    }

    public PackageResEntity getPackageWork(String packageId, String workResId) {

        return sw.buildQuery()
                .eq("PACKAGE_ID", packageId)
                .eq("WORK_RES_ID", workResId)
                .doQueryFirst(PackageResEntity.class);
    }

    /**
     * 通过作业 ID 删除发布包中的作业
     *
     * @param workId 作业id
     */
    public void deletePackageWork(String workId) {

        sw.buildQuery()
                .eq("WORK_RES_ID", workId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(PackageResEntity.class);
    }

    /** 查询在发布中的任务包 */
    public List<PackageEntity> queryPublishingPackage() {

        return sw.buildQuery().in("STATE", "WAITING_CONFIRM", "TO_SUBMIT").doQuery(PackageEntity.class);
    }

    /** 查询在发布中的任务包 */
    public List<PackageEntity> queryPublishingPackage(String projectId) {

        return sw.buildQuery()
                .eq("PROJECT_ID", projectId)
                .in("STATE", "WAITING_CONFIRM", "TO_SUBMIT")
                .doQuery(PackageEntity.class);
    }

    public List<PackageResEntity> queryPublishingWork(List<String> packageIds) {

        return sw.buildQuery().in("PACKAGE_ID", packageIds).doQuery(PackageResEntity.class);
    }

    /**
     * 更新发布时间
     *
     * @param id 发布包ID
     */
    public void updateRelDate(String id) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("REL_DATE", new Date())
                .doUpdate(PackageEntity.class);
    }
}

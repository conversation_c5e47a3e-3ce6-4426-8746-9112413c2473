package com.definesys.dehoop.admin.moudle.businessunit.pojo.dto;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.*;

import lombok.Data;

/** 查询返回对象 */
@Data
@SQLQuery(
        value = {
            @SQL(
                    view = "view_business_unit",
                    sql =
                            "SELECT bu.*, cal.name AS calculateName, cal.CLUSTER_TYPE AS type FROM RES_BUSINESS_UNIT bu LEFT JOIN RES_CAL_ENGINES cal ON cal.id = bu.CAL_ID")
        })
@Style
@TenantEnable
public class QueryBusinessUnit extends XdapBasePojo {

    private String id;

    private String name;

    @Column("CAL_ID")
    private String calId;

    @Column("WORKSPACE_ID")
    private String workspaceId;

    @Column("DATABASE_NAME")
    private String databaseName;

    @Column("ADMIN_ID")
    private String adminId;

    private String icon;

    @Column("DESCRIPTION")
    private String descr;

    private String state;

    private String adminName;

    private String calculateName;

    private String type;

    // 头像地址
    private String avatarUrl;

    @Column("ENVIRONMENT_TYPE")
    private String environmentType;
}

package com.definesys.dehoop.admin.moudle.initdata.pojo.dto;

import com.definesys.mpaas.common.adapter.UserProfile;

import org.springframework.context.ApplicationEvent;

import lombok.Getter;
import lombok.Setter;

/** 同步平台event */
@Getter
@Setter
public class InitPlatformDataEvent extends ApplicationEvent {

    private InitPlatformDataReqDto initPlatformDataReqDto;

    private UserProfile userProfile;

    public InitPlatformDataEvent(
            Object source, InitPlatformDataReqDto initPlatformDataReqDto, UserProfile userProfile) {
        super(source);
        this.initPlatformDataReqDto = initPlatformDataReqDto;
        this.userProfile = userProfile;
    }
}

SEMICOLON=1
LEFT_PAREN=2
RIGHT_PAREN=3
COMMA=4
DOT=5
LEFT_BRACKET=6
RIGHT_BRACKET=7
LEFT_BRACE=8
RIGHT_BRACE=9
ACCOUNT_LOCK=10
ACCOUNT_UNLOCK=11
ADD=12
ADDDATE=13
ADMIN=14
AFTER=15
AGG_STATE=16
AGGREGATE=17
ALIAS=18
ALL=19
ALTER=20
ANALYZE=21
ANALYZED=22
AND=23
ANTI=24
APPEND=25
ARRAY=26
ARRAY_RANGE=27
AS=28
ASC=29
AT=30
AUTHORS=31
AUTO=32
AUTO_INCREMENT=33
ALWAYS=34
BACKEND=35
BACKENDS=36
BACKUP=37
BEGIN=38
BELONG=39
BETWEEN=40
BIGINT=41
BIN=42
BINARY=43
BINLOG=44
BITAND=45
BITMAP=46
BITMAP_UNION=47
BITOR=48
BITXOR=49
BLOB=50
BOOLEAN=51
BRIEF=52
BROKER=53
BUCKETS=54
BUILD=55
BUILTIN=56
BULK=57
BY=58
CACHE=59
CACHED=60
CALL=61
CANCEL=62
CASE=63
CAST=64
CATALOG=65
CATALOGS=66
CHAIN=67
CHAR=68
CHARSET=69
CHECK=70
CLEAN=71
CLUSTER=72
CLUSTERS=73
COLLATE=74
COLLATION=75
COLLECT=76
COLOCATE=77
COLUMN=78
COLUMNS=79
COMMENT=80
COMMIT=81
COMMITTED=82
COMPACT=83
COMPLETE=84
COMPRESS_TYPE=85
CONFIG=86
CONNECTION=87
CONNECTION_ID=88
CONSISTENT=89
CONSTRAINT=90
CONSTRAINTS=91
CONVERT=92
CONVERT_LSC=93
COPY=94
COUNT=95
CREATE=96
CREATION=97
CRON=98
CROSS=99
CUBE=100
CURRENT=101
CURRENT_CATALOG=102
CURRENT_DATE=103
CURRENT_TIME=104
CURRENT_TIMESTAMP=105
CURRENT_USER=106
DATA=107
DATABASE=108
DATABASES=109
DATE=110
DATE_ADD=111
DATE_CEIL=112
DATE_DIFF=113
DATE_FLOOR=114
DATE_SUB=115
DATEADD=116
DATEDIFF=117
DATETIME=118
DATETIMEV2=119
DATEV2=120
DATETIMEV1=121
DATEV1=122
DAY=123
DAYS_ADD=124
DAYS_SUB=125
DECIMAL=126
DECIMALV2=127
DECIMALV3=128
DECOMMISSION=129
DEFAULT=130
DEFERRED=131
DELETE=132
DEMAND=133
DESC=134
DESCRIBE=135
DIAGNOSE=136
DISK=137
DISTINCT=138
DISTINCTPC=139
DISTINCTPCSA=140
DISTRIBUTED=141
DISTRIBUTION=142
DIV=143
DO=144
DORIS_INTERNAL_TABLE_ID=145
DOUBLE=146
DROP=147
DROPP=148
DUAL=149
DUPLICATE=150
DYNAMIC=151
ELSE=152
ENABLE=153
ENCRYPTKEY=154
ENCRYPTKEYS=155
END=156
ENDS=157
ENGINE=158
ENGINES=159
ENTER=160
ERRORS=161
EVENTS=162
EVERY=163
EXCEPT=164
EXCLUDE=165
EXECUTE=166
EXISTS=167
EXPIRED=168
EXPLAIN=169
EXPORT=170
EXTENDED=171
EXTERNAL=172
EXTRACT=173
FAILED_LOGIN_ATTEMPTS=174
FALSE=175
FAST=176
FEATURE=177
FIELDS=178
FILE=179
FILTER=180
FIRST=181
FLOAT=182
FOLLOWER=183
FOLLOWING=184
FOR=185
FOREIGN=186
FORCE=187
FORMAT=188
FREE=189
FROM=190
FRONTEND=191
FRONTENDS=192
FULL=193
FUNCTION=194
FUNCTIONS=195
GENERATED=196
GENERIC=197
GLOBAL=198
GRANT=199
GRANTS=200
GRAPH=201
GROUP=202
GROUPING=203
GROUPS=204
HASH=205
HAVING=206
HDFS=207
HELP=208
HISTOGRAM=209
HLL=210
HLL_UNION=211
HOSTNAME=212
HOTSPOT=213
HOUR=214
HUB=215
IDENTIFIED=216
IF=217
IGNORE=218
IMMEDIATE=219
IN=220
INCREMENTAL=221
INDEX=222
INDEXES=223
INFILE=224
INNER=225
INSERT=226
INSTALL=227
INT=228
INTEGER=229
INTERMEDIATE=230
INTERSECT=231
INTERVAL=232
INTO=233
INVERTED=234
IPV4=235
IPV6=236
IS=237
IS_NOT_NULL_PRED=238
IS_NULL_PRED=239
ISNULL=240
ISOLATION=241
JOB=242
JOBS=243
JOIN=244
JSON=245
JSONB=246
KEY=247
KEYS=248
KILL=249
LABEL=250
LARGEINT=251
LAST=252
LATERAL=253
LDAP=254
LDAP_ADMIN_PASSWORD=255
LEFT=256
LESS=257
LEVEL=258
LIKE=259
LIMIT=260
LINES=261
LINK=262
LIST=263
LOAD=264
LOCAL=265
LOCALTIME=266
LOCALTIMESTAMP=267
LOCATION=268
LOCK=269
LOGICAL=270
LOW_PRIORITY=271
MANUAL=272
MAP=273
MATCH=274
MATCH_ALL=275
MATCH_ANY=276
MATCH_PHRASE=277
MATCH_PHRASE_EDGE=278
MATCH_PHRASE_PREFIX=279
MATCH_REGEXP=280
MATERIALIZED=281
MAX=282
MAXVALUE=283
MEMO=284
MERGE=285
MIGRATE=286
MIGRATIONS=287
MIN=288
MINUS=289
MINUTE=290
MODIFY=291
MONTH=292
MTMV=293
NAME=294
NAMES=295
NATURAL=296
NEGATIVE=297
NEVER=298
NEXT=299
NGRAM_BF=300
NO=301
NON_NULLABLE=302
NOT=303
NULL=304
NULLS=305
OBSERVER=306
OF=307
OFFSET=308
ON=309
ONLY=310
OPEN=311
OPTIMIZED=312
OR=313
ORDER=314
OUTER=315
OUTFILE=316
OVER=317
OVERWRITE=318
PARAMETER=319
PARSED=320
PARTITION=321
PARTITIONS=322
PASSWORD=323
PASSWORD_EXPIRE=324
PASSWORD_HISTORY=325
PASSWORD_LOCK_TIME=326
PASSWORD_REUSE=327
PATH=328
PAUSE=329
PERCENT=330
PERIOD=331
PERMISSIVE=332
PHYSICAL=333
PI=334
PLACEHOLDER=335
PLAN=336
PRIVILEGES=337
PROCESS=338
PLUGIN=339
PLUGINS=340
POLICY=341
PRECEDING=342
PREPARE=343
PRIMARY=344
PROC=345
PROCEDURE=346
PROCESSLIST=347
PROFILE=348
PROPERTIES=349
PROPERTY=350
QUANTILE_STATE=351
QUANTILE_UNION=352
QUERY=353
QUOTA=354
RANDOM=355
RANGE=356
READ=357
REAL=358
REBALANCE=359
RECENT=360
RECOVER=361
RECYCLE=362
REFRESH=363
REFERENCES=364
REGEXP=365
RELEASE=366
RENAME=367
REPAIR=368
REPEATABLE=369
REPLACE=370
REPLACE_IF_NOT_NULL=371
REPLICA=372
REPOSITORIES=373
REPOSITORY=374
RESOURCE=375
RESOURCES=376
RESTORE=377
RESTRICTIVE=378
RESUME=379
RETURNS=380
REVOKE=381
REWRITTEN=382
RIGHT=383
RLIKE=384
ROLE=385
ROLES=386
ROLLBACK=387
ROLLUP=388
ROUTINE=389
ROW=390
ROWS=391
S3=392
SAMPLE=393
SCHEDULE=394
SCHEDULER=395
SCHEMA=396
SCHEMAS=397
SECOND=398
SELECT=399
SEMI=400
SEQUENCE=401
SERIALIZABLE=402
SESSION=403
SET=404
SETS=405
SHAPE=406
SHOW=407
SIGNED=408
SKEW=409
SMALLINT=410
SNAPSHOT=411
SONAME=412
SPLIT=413
SQL=414
SQL_BLOCK_RULE=415
STAGES=416
START=417
STARTS=418
STATS=419
STATUS=420
STOP=421
STORAGE=422
STREAM=423
STREAMING=424
STRING=425
STRUCT=426
SUBDATE=427
SUM=428
SUPERUSER=429
SWITCH=430
SYNC=431
SYSTEM=432
TABLE=433
TABLES=434
TABLESAMPLE=435
TABLET=436
TABLETS=437
TASK=438
TASKS=439
TEMPORARY=440
TERMINATED=441
TEXT=442
THAN=443
THEN=444
TIME=445
TIMESTAMP=446
TIMESTAMPADD=447
TIMESTAMPDIFF=448
TINYINT=449
TO=450
TRANSACTION=451
TRASH=452
TREE=453
TRIGGERS=454
TRIM=455
TRUE=456
TRUNCATE=457
PE=458
TYPECAST=459
TYPES=460
UNBOUNDED=461
UNCOMMITTED=462
UNINSTALL=463
UNION=464
UNIQUE=465
UNLOCK=466
UNSIGNED=467
UP=468
UPDATE=469
USE=470
USER=471
USING=472
VALUE=473
VALUES=474
VARCHAR=475
VARIABLES=476
VARIANT=477
VAULT=478
VERBOSE=479
VERSION=480
VIEW=481
WARM=482
WARNINGS=483
WEEK=484
WHEN=485
WHERE=486
WHITELIST=487
WITH=488
WORK=489
WORKLOAD=490
WRITE=491
XOR=492
YEAR=493
EQ=494
NSEQ=495
NEQ=496
LT=497
LTE=498
GT=499
GTE=500
PLUS=501
SUBTRACT=502
ASTERISK=503
SLASH=504
MOD=505
TILDE=506
AMPERSAND=507
LOGICALAND=508
LOGICALNOT=509
PIPE=510
DOUBLEPIPES=511
HAT=512
COLON=513
ARROW=514
HINT_START=515
HINT_END=516
ATSIGN=517
DOUBLEATSIGN=518
STRING_LITERAL=519
LEADING_STRING=520
BIGINT_LITERAL=521
SMALLINT_LITERAL=522
TINYINT_LITERAL=523
INTEGER_VALUE=524
EXPONENT_VALUE=525
DECIMAL_VALUE=526
BIGDECIMAL_LITERAL=527
IDENTIFIER=528
BACKQUOTED_IDENTIFIER=529
SIMPLE_COMMENT=530
BRACKETED_COMMENT=531
FROM_DUAL=532
WS=533
UNRECOGNIZED=534
';'=1
'('=2
')'=3
','=4
'.'=5
'['=6
']'=7
'{'=8
'}'=9
'?'=335
'<=>'=495
'<'=497
'>'=499
'+'=501
'-'=502
'*'=503
'/'=504
'%'=505
'~'=506
'&'=507
'&&'=508
'!'=509
'|'=510
'||'=511
'^'=512
':'=513
'->'=514
'/*+'=515
'*/'=516
'@'=517
'@@'=518

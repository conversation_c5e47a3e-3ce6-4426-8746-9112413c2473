package com.definesys.dehoop.admin.moudle.atlas.config;

import com.definesys.dehoop.api.properties.AtlasProperties;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import org.apache.atlas.AtlasClientV2;

@Configuration
public class AtlasClientConfig {

    private final AtlasProperties atlasProperties;

    public AtlasClientConfig(AtlasProperties atlasProperties) {
        this.atlasProperties = atlasProperties;
    }

    @Bean
    public AtlasClientV2 initAtlasClient() {

        return new AtlasClientV2(
                new String[] {atlasProperties.getBaseUrl()},
                new String[] {atlasProperties.getUsername(), atlasProperties.getPasswd()});
    }
}

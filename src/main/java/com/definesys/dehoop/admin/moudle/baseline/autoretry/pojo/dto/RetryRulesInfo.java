package com.definesys.dehoop.admin.moudle.baseline.autoretry.pojo.dto;

import com.definesys.dehoop.api.serializer.DateTimeDeserializer;
import com.definesys.dehoop.api.serializer.DateTimeSerializer;

import lombok.Data;

import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@Data
public class RetryRulesInfo {

    private String id;

    private String name;

    private String retryWay;

    private String retryWayMeaning;

    private String retryPolicy;

    private String retryPolicyMeaning;

    private Integer retryInterval;

    private String retryIntervalWithUnit;

    private Integer maxRetryTimes;

    private String projectId;

    @JsonSerialize(using = DateTimeSerializer.class)
    @JsonDeserialize(using = DateTimeDeserializer.class)
    private Date creationDate;

    private String createdBy;
}

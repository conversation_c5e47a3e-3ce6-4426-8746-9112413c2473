package com.definesys.dehoop.admin.moudle.operation.controller;

import com.definesys.dehoop.admin.moudle.operation.pojo.OperationReqDto;
import com.definesys.dehoop.admin.moudle.operation.pojo.dto.*;
import com.definesys.dehoop.admin.moudle.operation.service.OperationService;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobFlowConfigs;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.dto.FlowWebConfigInfo;
import com.definesys.dehoop.admin.moudle.realwork.pojo.dto.RealWorkInfo;
import com.definesys.dehoop.admin.moudle.realwork.pojo.dto.RealWorkNodeInfo;
import com.definesys.dehoop.admin.moudle.role.sercurity.RoleAuthorized;
import com.definesys.dehoop.admin.moudle.schedule.pojo.dto.WorkVersionInfo;
import com.definesys.dehoop.api.constant.AuthorityConstant;
import com.definesys.mpaas.common.adapter.UserProfile;
import com.definesys.mpaas.query.session.MpaasSession;

import com.xdap.motor.annotation.RestWrapper;
import com.xdap.motor.vo.PageRespHelper;
import com.xdap.motor.vo.ResponseVo;

import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/sch/operation")
@RoleAuthorized(code = AuthorityConstant.OPERATIONAL_CENTER)
public class OperationController {

    private final OperationService operationService;

    public OperationController(OperationService operationService) {

        this.operationService = operationService;
    }

    //    /**
    //     * 查询发布中的工作流
    //     */
    //    @PostMapping("/query/flows")
    //    @RestWrapper(msg = "查询成功")
    //    @RoleAuthorized
    //    public List<FlowSimpleInfo> queryFlows(@RequestBody OperationReqDto operationReqDto) {
    //
    //        return operationService.queryFlows(operationReqDto, MpaasSession.getCurrentUser());
    //    }

    //    /**
    //     * 查询工作流信息接口
    //     */
    //    @GetMapping("/flowInfo")
    //    @RestWrapper(msg = "查询成功")
    //    @RoleAuthorized
    //    public FlowInfo getFlowInfo(@RequestParam String flowId) {
    //
    //        return operationService.getFlowInfo(flowId);
    //    }

    /** 获取工作流子级接口 */
    @GetMapping("/sonFlow")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public List<FlowSimpleInfo> getSonFlow(@RequestParam String flowId) {

        return operationService.getSonFlow(flowId);
    }

    /** 获取工作流父级接口 */
    @GetMapping("/parentFlow")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public List<FlowSimpleInfo> getParentFlow(@RequestParam String flowId) {

        return operationService.getParentFlow(flowId);
    }

    //    /**
    //     * 查询发布后作业列表接口
    //     */
    //    @PostMapping("/query/works")
    //    @RestWrapper(msg = "查询成功")
    //    @RoleAuthorized
    //    public List<WorkSimpleInfo> queryFlowWorks(@RequestBody OperationReqDto operationReqDto) {
    //
    //        return operationService.queryFlowWorks(operationReqDto);
    //    }

    /** 查询发布后DDL作业列表接口 */
    @PostMapping("/query/DDLWorks")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public List<WorkSimpleInfo> queryFlowDDLWorks(@RequestBody OperationReqDto operationReqDto) {
        return operationService.queryFlowDDLWork(operationReqDto);
    }

    //    /**
    //     * 查询发布后实时作业列表接口
    //     */
    //    @PostMapping("/query/realWorks")
    //    @RestWrapper(msg = "查询成功")
    //    @RoleAuthorized
    //    public List<WorkSimpleInfo> queryRealWorks(@RequestBody OperationReqDto operationReqDto) {
    //
    //        return operationService.queryRealWorks(operationReqDto);
    //    }

    /** 查询工作流DAG图 */
    @GetMapping("/flowConfig")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public JobFlowConfigs getFlowConfig(@RequestParam String flowId) {

        return operationService.getFlowConfig(flowId);
    }

    /** 查询发布后作业信息 */
    @GetMapping("/workInfo")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public WorkInfo getWorkInfo(@RequestParam String workId) {

        return operationService.getWorkInfo(workId, "WORK");
    }

    /**
     * 查询发布够DDL作业信息
     *
     * @param workId
     * @return
     */
    @GetMapping("/DDLWorkInfo")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public WorkInfo getDDLWorkInfo(String workId) {
        return operationService.getWorkInfo(workId, "DDL");
    }

    /** 查询用户作业实例 */
    @PostMapping("/queryPage/workInstances")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<WorkInstanceInfo> queryPageWorkInstances(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryPageWorkInstances(MpaasSession.getCurrentUser(), operationReqDto);
    }

    /** 查询用户作业实例 */
    @PostMapping("/queryPage/workInstancesByProject")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<WorkInstanceInfo> queryPageWorkInstancesByProject(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryPageWorkInstancesByProject(operationReqDto);
    }

    /** 查询工作流实例接口 */
    @PostMapping("/queryPage/flowInstances")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<FlowInstanceInfo> queryPageFlowInstances(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryPageFlowInstances(MpaasSession.getCurrentUser(), operationReqDto);
    }

    /** 查询项目下的工作流实例接口 */
    @PostMapping("/queryPage/flowInstancesByProject")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<FlowInstanceInfo> queryPageFlowInstancesByProject(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryPageFlowInstancesByProject(operationReqDto);
    }

    /** 查询DDL实例接口 */
    @PostMapping("/queryPage/DDLInstances")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<DDLInstanceInfo> queryPageDDLInstances(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryDDLInstances(MpaasSession.getCurrentUser(), operationReqDto);
    }

    /** 查询DDL实例接口 */
    @PostMapping("/queryPage/DDLInstancesByProject")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<DDLInstanceInfo> queryPageDDLInstancesByProject(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryDDLInstancesByProject(operationReqDto);
    }

    /** 列表查询标签实例接口 */
    @PostMapping("/queryPage/tagInstances")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<TagInstanceInfo> queryPageTagInstances(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryTagInstances(operationReqDto);
    }

    /** 列表查询标签实例接口 */
    @PostMapping("/queryPage/tagInstancesByProject")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<TagInstanceInfo> queryPageTagInstancesByProject(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryTagInstancesByProject(operationReqDto);
    }

    /** 查询实时作业实例接口 */
    @PostMapping("/queryPage/realWorkInstances")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<RealWorkInstanceInfo> queryPageRealWorkInstances(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryRealWorkInstances(MpaasSession.getCurrentUser(), operationReqDto);
    }

    /** 查询实时作业实例接口 */
    @PostMapping("/queryPage/realWorkInstancesByProject")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<RealWorkInstanceInfo> queryPageRealWorkInstancesByProject(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryRealWorkInstancesByProject(operationReqDto);
    }

    /** 重新执行工作流实例 */
    @GetMapping("/reExecuteFlow")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public ResponseVo<?> reExecuteFlow(@RequestParam String flowInstanceId) {

        final UserProfile userProfile = MpaasSession.getUserProfile();
        // 使用异步加载重跑
        Thread thread =
                new Thread(
                        () -> {
                            MpaasSession.setUserProfile(userProfile);
                            operationService.reExecuteFlow(flowInstanceId);
                        });
        thread.start();

        ResponseVo<?> result = new ResponseVo<>();
        result.setMessage("重跑成功");
        return result;
    }

    /**
     * 获取实时作业实例
     *
     * @param instanceId 实时作业
     * @return 实时作业
     */
    @GetMapping("/get/realWorkInstance")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public RealWorkInstanceDto getRealWorkInstance(String instanceId) {

        return operationService.getRealWorkInstance(instanceId);
    }

    /**
     * 实时作业实例配置信息获取
     *
     * @param instanceId 实时作业实例ID
     * @return 实时作业配置
     */
    @GetMapping("/get/realWorkInstanceConfig")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public RealWorkDagDto getRealWorkInstanceConfig(String instanceId) {
        return operationService.getRealWorkDagConfig(instanceId);
    }

    /**
     * 实时作业实例节点
     *
     * @param instanceId
     * @param nodeId
     * @return
     */
    @GetMapping("/get/instanceRealNode")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public RealWorkNodeInfo getInstanceRealNode(String instanceId, String nodeId) {
        return operationService.getInstanceRealNode(instanceId, nodeId);
    }

    /**
     * 获取工作流实例
     *
     * @param instanceId 实例ID
     * @return 工作流实例
     */
    @GetMapping("/get/flowInstance")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public FlowInstanceDagVo getFlowInstanceDAG(String instanceId) {
        return operationService.getFlowInstance(instanceId);
    }

    /**
     * 获取工作流坐标配置
     *
     * @param instanceId 实例ID
     * @return 工作流坐标配置
     */
    @GetMapping("/get/flowInstanceCfg")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public FlowWebConfigInfo getFlowInstanceCfg(String instanceId) {

        return operationService.getFlowInstanceCfg(instanceId);
    }

    /**
     * 查询ddl实例
     *
     * @param instanceId 实例ID
     * @return ddl实例
     */
    @GetMapping("/get/DDLWorkInstance")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public WorkInstanceDagDto getDDLWorkInstance(String instanceId) {

        return operationService.getWorkInstanceDAG(instanceId, "DDL");
    }

    /**
     * 获取离线作业实例分析DAG图
     *
     * @param instanceId 实例ID
     * @return 离线作业实例
     */
    @GetMapping("/get/workInstance")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public WorkInstanceDagDto getWorkInstanceDAG(String instanceId) {

        return operationService.getWorkInstanceDAG(instanceId, "WORK");
    }

    /** 暂停实例接口 */
    @PostMapping("/suspend/instances")
    @RoleAuthorized
    public ResponseVo<?> suspendInstances(@RequestBody OperationReqDto operationReqDto) {

        operationService.suspendInstances(operationReqDto);

        ResponseVo<?> result = new ResponseVo<>();
        result.setMessage("暂停成功");
        return result;
    }

    /** 终止实例接口 */
    @PostMapping("/stop/instances")
    @RoleAuthorized
    public ResponseVo<?> stopInstances(@RequestBody OperationReqDto operationReqDto) {

        operationService.stopInstances(operationReqDto);

        ResponseVo<?> result = new ResponseVo<>();
        result.setMessage("终止成功");
        return result;
    }

    /** 运行实例接口 */
    @PostMapping("/execute/instances")
    @RoleAuthorized
    public ResponseVo<?> executeInstances(@RequestBody OperationReqDto operationReqDto) {

        operationService.executeInstances(operationReqDto);

        ResponseVo<?> result = new ResponseVo<>();
        result.setMessage("运行成功");
        return result;
    }

    /**
     * 查询实时作业
     *
     * @param workId 作业ID
     * @return 实时作业
     */
    @GetMapping("/get/realWorkInfo")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public RealWorkInfo getRealWorkInfo(String workId) {
        return operationService.getRealWorkInfo(workId);
    }

    /**
     * 查询实时作业图配置信息
     *
     * @param workId 作业ID
     * @return 节点配置
     */
    @GetMapping("/get/realWorkConfig")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public RealWorkDagDto getRealWorkConfig(String workId) {
        return operationService.getRealWorkConfig(workId);
    }

    @GetMapping("/get/realWorkNode")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public RealWorkNodeInfo getRealWorkNode(String workId, String nodeId) {
        return operationService.getRealWorkNode(workId, nodeId);
    }

    /**
     * 查询上游节点
     *
     * @param instanceId
     * @return
     */
    @GetMapping("/get/parentFlow")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public List<FlowNodeDto> getInstanceParentFlow(String instanceId) {
        return operationService.queryFlowDependents(instanceId, "PARENT");
    }

    /**
     * 查询下游节点
     *
     * @param instanceId
     * @return
     */
    @GetMapping("/get/sonFlows")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public List<FlowNodeDto> getInstanceSonFlow(String instanceId) {
        return operationService.queryFlowDependents(instanceId, "SON");
    }

    /** 查询发布中的工作流 */
    @PostMapping("/query/flows")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<FlowTaskInfo> queryFlows(@RequestBody OperationReqDto operationReqDto) {

        return operationService.queryFlows(operationReqDto, MpaasSession.getCurrentUser());
    }

    /** 查询发布后实时作业列表接口 */
    @PostMapping("/query/realWorks")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public PageRespHelper<RealWorkTaskInfo> queryRealWorks(
            @RequestBody OperationReqDto operationReqDto) {

        return operationService.queryRealWorks(operationReqDto, MpaasSession.getCurrentUser());
    }

    /** 查询发布后作业列表接口 */
    @PostMapping("/query/flowWorks")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public List<WorkTaskInfo> queryFlowWorks(@RequestBody OperationReqDto operationReqDto) {

        return operationService.queryFlowWorks(operationReqDto);
    }

    /**
     * 查询已发布工作流的依赖DAG图
     *
     * @param flowId 工作流id
     * @return 查询结果
     */
    @GetMapping("/query/flowDependents")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public FlowDependentsDAG queryFlowDependents(@RequestParam String flowId) {

        return operationService.queryFlowDependents(flowId);
    }

    /** 查询工作流信息接口 */
    @GetMapping("/get/flowInfo")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public FlowDetail getFlowInfo(@RequestParam String flowId) {

        return operationService.getFlowInfo(flowId);
    }

    /**
     * 停止工作流定时器
     *
     * @param operationReqDto 请求体
     * @return response
     */
    @PostMapping("/schFlow/stop")
    @RoleAuthorized
    public ResponseVo<?> stopSchFlow(@RequestBody OperationReqDto operationReqDto) {

        operationService.stopSchFlow(operationReqDto.getFlowId());
        ResponseVo<?> result = new ResponseVo<>();
        result.setMessage("操作成功");
        return result;
    }

    /**
     * 开始工作流定时器
     *
     * @param operationReqDto 请求体
     * @return response
     */
    @PostMapping("/schFlow/start")
    @RoleAuthorized
    public ResponseVo<?> startSchFlow(@RequestBody OperationReqDto operationReqDto) {

        operationService.startSchFlow(operationReqDto.getFlowId());
        ResponseVo<?> result = new ResponseVo<>();
        result.setMessage("操作成功");
        return result;
    }

    /**
     * 查询工作流下的作业DAG图
     *
     * @param flowId 工作流id
     * @return 查询结果
     */
    @GetMapping("/get/flowWebConfig")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public FlowWebConfigInfo getFlowWebConfig(@RequestParam String flowId) {

        return operationService.getFlowWebConfig(flowId);
    }

    /**
     * 查询工作流信息接口
     *
     * @param workId 作业id
     * @return 查询结果
     */
    @GetMapping("/get/workInfo")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public WorkVersionInfo getWorkInfos(@RequestParam String workId) {

        return operationService.getWorkInfos(workId);
    }

    /**
     * 获取作业的配置信息 脚本类型作业返回作业的脚本 非脚本类型作业返回配置
     *
     * @param workId 作业id
     * @return 查询结果
     */
    @GetMapping("/get/workConfigInfo")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public WorkVersionInfo getWorkConfigInfo(@RequestParam String workId) {

        return operationService.getWorkConfigInfo(workId);
    }

    /**
     * 获取工作流实例的DAG图
     *
     * @param flowInstanceId 工作流实例id
     * @return 查询结果
     */
    @GetMapping("/get/flowInstanceDAG")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public FlowInstanceDAG getFlowInstanceDag(@RequestParam String flowInstanceId) {

        return operationService.getFlowInstanceDAG(flowInstanceId);
    }

    /**
     * 获取工作流作业实例的DAG图
     *
     * @param flowInstanceId 工作流id
     * @return 查询结果
     */
    @GetMapping("/get/workInstanceDAG")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public WorkFlowWebConfig getWorkFlowInstanceDAG(@RequestParam String flowInstanceId) {

        return operationService.getWorkFlowInstanceDAG(flowInstanceId);
    }

    /**
     * 查询实时作业下的节点
     *
     * @param realWorkId 实时作业id
     * @return 查询结果
     */
    @GetMapping("/query/realWorkNodes")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public List<RealWorkNodeInfos> queryRealWorkNode(@RequestParam String realWorkId) {

        return operationService.queryRealWorkNode(realWorkId);
    }

    /**
     * 实时作业查询单个节点的配置
     *
     * @param realWorkId 实时作业id
     * @param realNodeId 节点id
     * @return 查询结果
     */
    @GetMapping("/get/realWorkNodeInfo")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public RealWorkNodeInfo getRealNode(
            @RequestParam String realWorkId, @RequestParam String realNodeId) {

        return operationService.getRealNode(realWorkId, realNodeId);
    }

    /**
     * 查询实时作业的信息
     *
     * @param realWorkId 实时作业id
     * @return 查询结果
     */
    @GetMapping("/get/realWork")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public RealWorkInfo getRealWork(@RequestParam String realWorkId) {

        return operationService.getRealWork(realWorkId);
    }

    /**
     * 查询历史作业流信息
     *
     * @return 查询结果
     */
    @GetMapping("/query/allFlow")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public List<FlowInfoDto> queryAllFlow(@RequestParam String userId) {

        return operationService.queryAllFlow(userId);
    }

    /** aPaas平台查询作业流 */
    @GetMapping("/get/flows")
    @RestWrapper(msg = "查询成功")
    @RoleAuthorized
    public List<FlowInfoDto> queryFlows(@RequestParam String projectId) {
        return operationService.queryFlows(projectId);
    }
}

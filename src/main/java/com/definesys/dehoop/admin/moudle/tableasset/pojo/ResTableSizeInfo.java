package com.definesys.dehoop.admin.moudle.tableasset.pojo;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.*;

import lombok.*;

@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "RES_TABLE_SIZE_INFO")
@Style
@NoArgsConstructor
@AllArgsConstructor
@TenantEnable
public class ResTableSizeInfo extends XdapBasePojo {

    @Column("ID")
    private String id;

    @Column("TABLE_ID")
    private String tableId;

    @Column("ROW_SIZE")
    private Long rowSize;

    @Column("TABLE_SIZE")
    private Long tableSize;
}

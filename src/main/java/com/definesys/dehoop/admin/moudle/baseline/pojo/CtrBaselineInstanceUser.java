package com.definesys.dehoop.admin.moudle.baseline.pojo;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.Column;
import com.definesys.mpaas.query.annotation.Style;
import com.definesys.mpaas.query.annotation.Table;
import com.definesys.mpaas.query.annotation.TenantEnable;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/1/12 16:44
 * @description
 */
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "CTR_BASELINE_INSTANCE_USER")
@Style
@NoArgsConstructor
@AllArgsConstructor
@TenantEnable
public class CtrBaselineInstanceUser extends XdapBasePojo {

    @Column("ID")
    private String id;

    @Column("INSTANCE_ID")
    private String instanceId;

    @Column("USER_ID")
    private String userId;
}

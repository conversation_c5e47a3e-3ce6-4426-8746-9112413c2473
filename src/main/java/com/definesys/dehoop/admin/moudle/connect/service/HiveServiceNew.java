package com.definesys.dehoop.admin.moudle.connect.service;

import com.definesys.dehoop.admin.moudle.connect.pojo.ConnectInfo;
import com.definesys.dehoop.admin.moudle.connect.pojo.TableColumn;
import com.definesys.dehoop.admin.moudle.connect.pojo.TableData;
import com.definesys.dehoop.admin.moudle.connect.pojo.TableInfo;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.TableColumnInfo;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.dto.ChunJunColumnDto;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.dto.ChunJunReaderDto;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.dto.ChunJunWriterDto;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.dto.ChunJunWriterParameterDto;
import com.definesys.dehoop.admin.moudle.realwork.pojo.dto.ColumnInfo;
import com.definesys.dehoop.admin.moudle.realwork.pojo.dto.ColumnMapping;
import com.definesys.dehoop.admin.moudle.realwork.pojo.dto.InputTableInfo;
import com.definesys.dehoop.admin.moudle.realwork.pojo.dto.OutputTableInfo;
import com.definesys.dehoop.api.enums.HiveFileType;
import com.definesys.dehoop.api.exception.DataSourceExceptionEnum;

import com.xdap.motor.exception.CommonBizExceptionEnum;
import com.xdap.motor.exception.XDapBizException;
import com.xdap.motor.utils.StringUtils;

import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.HiveSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.source.DataSourceType;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import static java.util.regex.Pattern.compile;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HiveServiceNew implements DataBaseService {

    @Override
    public List<String> getDataType() {
        List<String> dataTypes = new ArrayList<>();
        dataTypes.add("STRING");
        dataTypes.add("BIGINT");
        dataTypes.add("DOUBLE");
        dataTypes.add("TIMESTAMP");
        dataTypes.add("DATE");
        dataTypes.add("DECIMAL");
        return dataTypes;
    }

    @Override
    public boolean hasExistedTable(ConnectInfo connectInfo, String tableName, String databaseName) {

        if (StringUtils.isBlank(databaseName)) {
            String[] jdbcSplit = connectInfo.getJdbcUrl().split("/");
            if (StringUtils.isBlank(jdbcSplit[jdbcSplit.length - 1])) {
                return false;
            } else {
                databaseName = jdbcSplit[jdbcSplit.length - 1];
            }
        }

        // 查询表名
        return getClient().isTableExistsInDatabase(getSource(connectInfo), tableName, databaseName);
    }

    @Override
    public boolean hasExistedDatabase(ConnectInfo connectInfo, String databaseName) {

        // 查询表名
        return getClient().isDatabaseExists(getSource(connectInfo), databaseName);
    }

    /** 转换hive字段类型 */
    public static String transToHiveType(String columnType, String dataBaseType) {

        String typeStr = compile("\\(.*?\\)").matcher(columnType).replaceAll("");

        String type = typeStr.toLowerCase().replace("unsigned", "");
        switch (type.trim()) {
            case "uint8":
            case "uint16":
            case "uint32":
            case "uint64":
            case "double":
            case "float64":
            case "number":
                return "DOUBLE";
            case "int":
            case "int32":
            case "int8":
            case "int16":
            case "tinyint":
            case "bigint":
            case "integer":
            case "smallint":
            case "mediumint":
                return "INT";
            case "longtext":
            case "varchar":
            case "varchar2":
            case "nvarchar2":
            case "nvarchar":
            case "nchar":
            case "text":
                return "STRING";
            case "datetime":
            case "timestamp":
                return "TIMESTAMP";
            case "date":
                return "DATE";
            case "float":
            case "float32":
                return "FLOAT";
            case "bit":
                Matcher matcher = compile("\\((.*?)\\)").matcher(columnType);
                if (matcher.find() && "1".equals(matcher.group(1))) {
                    return "BOOLEAN";
                }
                return "BINARY";
            default:
                return columnType.toUpperCase();
        }
    }

    @Override
    public ISourceDTO getSource(ConnectInfo connectInfo) {
        return HiveSourceDTO.builder()
                .url(connectInfo.getJdbcUrl())
                .username(connectInfo.getUsername())
                .password(connectInfo.getPasswd())
                .schema(connectInfo.getSchema())
                .poolConfig(PoolConfig.builder().build())
                .build();
    }

    @Override
    public IClient getClient() {

        return ClientCache.getClient(DataSourceType.HIVE.getVal());
    }

    @Override
    public List<String> queryAllDatabaseName(ConnectInfo connectInfo) {
        return getClient().getAllDatabases(getSource(connectInfo), SqlQueryDTO.builder().build());
    }

    @Override
    public List<String> queryTableNames(ConnectInfo connectInfo) {

        // 检测是否输入中文字符，输入中文字符报错
        String tableName = connectInfo.getTableName();
        String pattern = "[^\\x00-\\xff]+";
        Pattern regex = Pattern.compile(pattern);
        if (tableName != null && regex.matcher(tableName).find()) {
            throw new XDapBizException(DataSourceExceptionEnum.HIVE_NOT_SUPPORT_CHINESE_CHARACTER);
        }

        // 获取页数
        Integer page = connectInfo.getPage();
        if (page != null && page < 1) {
            page = 1;
        }

        try {

            SqlQueryDTO.SqlQueryDTOBuilder builder = SqlQueryDTO.builder();
            if (tableName != null) {
                builder.tableNamePattern(tableName);
            }
            if (page != null) {
                builder.limit(page * 100);
            }

            List<String> tableNames = getClient().getTableList(getSource(connectInfo), builder.build());
            return tableNames.stream()
                    .skip(page == null ? 0 : (page - 1) * 100L)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new XDapBizException(DataSourceExceptionEnum.GET_TABLE_INFO_ERROR);
        }
    }

    @Override
    public TableData queryTableData(ConnectInfo connectInfo) {

        TableData data = new TableData();
        try {
            // 拼接sql
            String sql = "select * from " + connectInfo.getTableName();
            if (Objects.nonNull(connectInfo.getConditionSql())) {
                sql += " where " + connectInfo.getConditionSql();
            }

            if (Objects.nonNull(connectInfo.getConditionSql())) {
                sql += connectInfo.getConditionSql();
            }

            // 执行sql
            SqlQueryDTO sqlQueryDTO =
                    SqlQueryDTO.builder().sql("select * from ( " + sql + " ) as t limit 10 ").build();
            List<Map<String, Object>> executeQuery =
                    getClient().executeQuery(getSource(connectInfo), sqlQueryDTO);

            List<String> columnList = new ArrayList<>();
            if (!executeQuery.isEmpty()) {

                Map<String, Object> meta = executeQuery.get(0);

                meta.forEach((k, v) -> columnList.add(k));
            }

            // 解析返回结果
            List<List<String>> dataResult = new ArrayList<>();
            executeQuery.forEach(
                    e -> {
                        List<String> metaData = new ArrayList<>();
                        e.forEach((k, v) -> metaData.add(v == null ? null : String.valueOf(v)));
                        dataResult.add(metaData);
                    });

            // 封装返回对象
            data.setDataList(dataResult);
            data.setColumnList(columnList);
            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new XDapBizException(DataSourceExceptionEnum.GET_TABLE_INFO_ERROR);
        }
    }

    @Override
    public void executeSql(ConnectInfo connectInfo, String sql) {

        if (connectInfo == null) {
            log.error(CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE.getMessage());
            throw new XDapBizException(CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        }

        try {
            // 执行sql
            log.debug("直接执行hive sql语句:" + sql);
            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().sql(sql).build();
            getClient().executeSqlWithoutResultSet(getSource(connectInfo), sqlQueryDTO);

        } catch (Exception e) {

            log.error(e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 分析hive数据元数据
     *
     * @param connectInfo
     */
    public void analyzeTableInfo(ConnectInfo connectInfo) {

        try {

            String sql;

            TableColumn tableColumn = queryTableColumn(connectInfo);
            List<TableColumnInfo> partitionColumnInfos = tableColumn.getPartitionColumnInfos();

            if (partitionColumnInfos.isEmpty()) {
                sql = " analyze table  " + connectInfo.getTableName() + " compute statistics ";
            } else {
                List<String> partitions =
                        partitionColumnInfos.stream()
                                .map(TableColumnInfo::getField)
                                .collect(Collectors.toList());
                String partitionSql = StringUtils.join(partitions, ",");
                sql =
                        String.format(
                                " analyze table  "
                                        + connectInfo.getTableName()
                                        + " partition(%s) compute statistics ",
                                partitionSql);
            }

            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().sql(sql).build();
            getClient().executeSqlWithoutResultSet(getSource(connectInfo), sqlQueryDTO);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Override
    public JdbcTemplate getJdbcTemplate(ConnectInfo connectInfo) {
        return null;
    }

    @Override
    public List<Map<String, Object>> queryTableDataAsObject(ConnectInfo connectInfo) {
        return null;
    }

    @Override
    public TableInfo getDetailTableInfo(ConnectInfo connectInfo) {
        TableInfo tableInfo = new TableInfo();
        TableColumn tableColumn = queryTableColumn(connectInfo);
        try {
            // 日志
            log.debug(
                    "getDetailTableInfo==>"
                            + "jdbcUrl==>"
                            + connectInfo.getJdbcUrl()
                            + "username==>"
                            + connectInfo.getUsername()
                            + "password==>"
                            + connectInfo.getPasswd());

            // 执行sql
            SqlQueryDTO sqlQueryDTO =
                    SqlQueryDTO.builder().sql("desc formatted " + connectInfo.getTableName()).build();
            List<Map<String, Object>> list =
                    getClient().executeQuery(getSource(connectInfo), sqlQueryDTO);

            // 解析数据
            Map<String, List<String>> data = new HashMap<>();
            list.forEach(
                    e -> {
                        String firstValue = null;
                        String secondValue = null;
                        List<String> values = new ArrayList<>();

                        if (Objects.nonNull(e.get("col_name"))) {

                            firstValue = String.valueOf(e.get("col_name")).trim();
                            if (firstValue.isEmpty()) {

                                firstValue = null;
                            } else {
                                values.add(firstValue);
                                data.put(firstValue, null);
                            }
                        }

                        if (Objects.nonNull(e.get("data_type"))) {

                            secondValue = String.valueOf(e.get("data_type")).trim();
                            values.add(secondValue);
                            if (Objects.isNull(firstValue)) {
                                data.put(secondValue, null);
                            }
                        }

                        if (Objects.nonNull(e.get("comment"))) {

                            String thirdValue = String.valueOf(e.get("comment")).trim();
                            values.add(thirdValue);
                        }

                        data.put(Objects.isNull(firstValue) ? secondValue : firstValue, values);
                    });

            // 储存hdfs地址
            if (data.get("Location:") != null) {
                String locationStr = String.valueOf(data.get("Location:").get(1));
                tableInfo.setLocation(locationStr);

                String[] split = locationStr.split("/", 4);
                tableInfo.setHiveHdfsUrl(
                        locationStr.substring(0, locationStr.length() - split[3].length()));

                // hive储存地址
                tableInfo.setHiveTableLocation("/" + split[3]);
            }

            // 数据行数
            if (data.get("numRows") != null && data.get("numRows").get(1) != null) {
                tableInfo.setNumRows(Long.parseLong(data.get("numRows").get(1)));
            }
            // 存储大小
            if (data.get("totalSize") != null && data.get("totalSize").get(1) != null) {
                tableInfo.setTotalSize(Long.parseLong(data.get("totalSize").get(1)));
            }
            // 是否是分区表
            if (Objects.nonNull(data.get("# Partition Information"))) {
                tableInfo.setHasPartition(true);
                tableInfo.setPartitionColumnInfos(tableColumn.getPartitionColumnInfos());
            }
            // 最新更新时间
            if (data.get("last_modified_time") != null && data.get("last_modified_time").get(1) != null) {
                tableInfo.setLastModifyTime(Long.parseLong(data.get("last_modified_time").get(1)) * 1000);
            }
            // 最新操作时间
            if (data.get("transient_lastDdlTime") != null
                    && data.get("transient_lastDdlTime").get(1) != null) {
                tableInfo.setLastTransientTime(
                        Long.parseLong(data.get("transient_lastDdlTime").get(1)) * 1000);
            }
            // 最新操作时间
            if (data.get("CreateTime:") != null && data.get("CreateTime:").get(1) != null) {
                tableInfo.setCreateDateStr(data.get("CreateTime:").get(1));
                if (StringUtils.isNotBlank(tableInfo.getCreateDateStr())) {
                    tableInfo.setCreateTime(new Date(tableInfo.getCreateDateStr()));
                }
            }
            // 行分割符
            if (data.get("mapkey.delim") != null && data.get("mapkey.delim").get(1) != null) {
                String lineSplit = data.get("mapkey.delim").get(1);
                tableInfo.setLineSplitBy(lineSplit == null ? "\\t" : lineSplit.isEmpty() ? " " : lineSplit);
            } else {
                tableInfo.setLineSplitBy("\\t");
            }
            // 字段分割符
            if (data.get("field.delim") != null && data.get("field.delim").get(1) != null) {
                String fieldSplit = data.get("field.delim").get(1);
                tableInfo.setFieldSplitBy(
                        fieldSplit == null ? "\u0001" : fieldSplit.isEmpty() ? " " : fieldSplit);
            } else if (data.get("separatorChar") != null) {
                String fieldSplit = data.get("separatorChar").get(1);
                tableInfo.setFieldSplitBy(
                        fieldSplit == null ? "\t" : fieldSplit.isEmpty() ? " " : fieldSplit);
            } else {
                tableInfo.setFieldSplitBy("\\u0001");
            }
            // 表类型
            if (data.get("Table Type:") != null) {
                tableInfo.setTableType(data.get("Table Type:").get(1));
            }
            // 判断表是否为avro表
            if (data.get("SerDe Library:") != null) {

                String serDeLibrary = data.get("SerDe Library:").get(1);
                tableInfo.setIsAvroTable(serDeLibrary.contains("avro"));

                // 判断文件类型
                if (data.get("InputFormat:") != null) {

                    String inputFormat = data.get("InputFormat:").get(1);

                    if (inputFormat.contains("avro")) {
                        tableInfo.setFileType(HiveFileType.AVRO);

                    } else if (inputFormat.contains("orc")) {
                        tableInfo.setFileType(HiveFileType.ORC);

                    } else if (inputFormat.contains("RCFile")) {
                        tableInfo.setFileType(HiveFileType.RC);

                    } else if (inputFormat.contains("Parquet")) {
                        tableInfo.setFileType(HiveFileType.PARQUET);

                    } else if (inputFormat.contains("Text") && serDeLibrary.contains("Json")) {
                        tableInfo.setFileType(HiveFileType.JSON);

                    } else if (inputFormat.contains("Sequence")) {
                        tableInfo.setFileType(HiveFileType.SEQUENCEFILE);

                    } else {
                        tableInfo.setFileType(HiveFileType.TEXTFILE);
                    }
                }
            }

            if (data.get("serialization.null.format") != null
                    && data.get("serialization.null.format").size() >= 2) {
                tableInfo.setSerializationNullFormat(data.get("serialization.null.format").get(1));
            }

            // owner
            if (data.get("Owner:") != null) {
                tableInfo.setOwner(data.get("Owner:").get(1));
            }

            // 描述
            if (data.get("comment") != null) {
                tableInfo.setComment(data.get("comment").get(1));
            }

            tableInfo.setTableColumnInfos(tableColumn.getTableColumnInfos());
            return tableInfo;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new XDapBizException(DataSourceExceptionEnum.GET_TABLE_INFO_ERROR);
        }
    }

    @Override
    public TableColumn queryTableColumn(ConnectInfo connectInfo) {
        log.debug(
                "getDetailTableInfo==>"
                        + "jdbcUrl==>"
                        + connectInfo.getJdbcUrl()
                        + "username==>"
                        + connectInfo.getUsername()
                        + "password==>"
                        + connectInfo.getPasswd()
                        + " tablename==>"
                        + connectInfo.getTableName());

        TableColumn columns = new TableColumn();
        List<TableColumnInfo> tableColumn = new ArrayList<>();
        List<TableColumnInfo> partitionColumn = new ArrayList<>();
        List<TableColumnInfo> primaryColumn = new ArrayList<>();

        try {
            // 执行sql
            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().tableName(connectInfo.getTableName()).build();
            List<ColumnMetaDTO> columnMetaData =
                    getClient().getColumnMetaData(getSource(connectInfo), sqlQueryDTO);

            // 解析返回值
            columnMetaData.forEach(
                    e -> {
                        TableColumnInfo columnInfo = new TableColumnInfo();
                        columnInfo.setField(e.getKey());
                        columnInfo.setType(e.getType());
                        columnInfo.setComment(e.getComment());
                        if (e.getPart()) {
                            partitionColumn.add(columnInfo);
                        } else {
                            tableColumn.add(columnInfo);
                            if (e.getPrimary()) {
                                primaryColumn.add(columnInfo);
                            }
                        }
                    });

            columns.setTableColumnInfos(tableColumn);
            columns.setPartitionColumnInfos(partitionColumn);
            columns.setPrimaryColumnInfos(primaryColumn);

            return columns;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 这里抛出原本的异常，外部捕获获取信息
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public boolean checkDatabaseConnect(ConnectInfo connectInfo) {

        if (connectInfo == null) {
            return false;
        }

        HiveSourceDTO source = (HiveSourceDTO) getSource(connectInfo);
        return getClient().testCon(source);
    }

    @Override
    public List<Map<String, Object>> executeSqlWithResult(ConnectInfo connectInfo, String sql) {
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().sql(sql).build();

        return getClient().executeQuery(getSource(connectInfo), sqlQueryDTO);
    }

    @Override
    public ChunJunReaderDto getReader(
            ConnectInfo connectInfo, InputTableInfo inputTableInfo, List<ColumnMapping> columnMapping) {
        return DataBaseService.super.getReader(connectInfo, inputTableInfo, columnMapping);
    }

    @Override
    public ChunJunWriterDto getWriter(ConnectInfo connectInfo, OutputTableInfo outputTableInfo) {

        ChunJunWriterDto hdfsWriter = new ChunJunWriterDto();
        ChunJunWriterParameterDto hdfsParameter = new ChunJunWriterParameterDto();
        List<ChunJunColumnDto> hiveColumn = new ArrayList<>();
        List<String> fullColumn = new ArrayList<>();
        List<String> fullType = new ArrayList<>();
        TableInfo tableDetail = getDetailTableInfo(connectInfo);

        tableDetail
                .getTableColumnInfos()
                .forEach(
                        column -> {
                            fullColumn.add(column.getField());
                            fullType.add(column.getType());
                        });

        outputTableInfo
                .getColumnMapping()
                .forEach(
                        mapping -> {
                            ChunJunColumnDto outputColumn = new ChunJunColumnDto();
                            outputColumn.setIndex(fullColumn.indexOf(mapping.getToColumn()));
                            outputColumn.setName(mapping.getToColumn());
                            outputColumn.setType(mapping.getToColumnType());
                            if (outputColumn.getType().contains("(")) {
                                outputColumn.setType(
                                        outputColumn.getType().substring(0, outputColumn.getType().indexOf("(")));
                            }
                            hiveColumn.add(outputColumn);
                        });

        hdfsParameter.setFullColumnName(fullColumn);
        hdfsParameter.setFullColumnType(fullType);
        hdfsParameter.setColumn(hiveColumn);
        hdfsParameter.setColumn(hiveColumn);
        hdfsParameter.setPath(tableDetail.getLocation());
        hdfsParameter.setDefaultFS(tableDetail.getHiveHdfsUrl());
        hdfsParameter.setFileType(tableDetail.getFileType().name());
        hdfsParameter.setFieldDelimiter(tableDetail.getFieldSplitBy());
        hdfsParameter.setMaxFileSize(0);
        hdfsParameter.setNextCheckRows(
                outputTableInfo.getCheckRows() == null ? 5 : outputTableInfo.getCheckRows());
        hdfsParameter.setEncoding("utf-8");
        hdfsParameter.setWriteMode(outputTableInfo.getWriteMode());
        Map<String, String> hadoopConfig = new HashMap<>();
        hadoopConfig.put("fs.defaultFS", tableDetail.getHiveHdfsUrl());
        hadoopConfig.put(
                "dfs.client.failover.proxy.provider.ns",
                "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider");
        hadoopConfig.put("fs.hdfs.impl.disable.cache", "true");
        hadoopConfig.put("fs.hdfs.impl", "org.apache.hadoop.hdfs.DistributedFileSystem");
        hadoopConfig.put("dfs.client.use.datanode.hostname", "true");
        hadoopConfig.put("dfs.datanode.use.datanode.hostname", "true");
        hdfsParameter.setHadoopConfig(hadoopConfig);
        hdfsWriter.setParameter(hdfsParameter);
        hdfsWriter.setName("hdfswriter");
        Map<String, String> hdfsOutputTransformer = new HashMap<>();
        hdfsOutputTransformer.put("tableName", "sinkTable");
        hdfsWriter.setTable(hdfsOutputTransformer);

        return hdfsWriter;
    }

    @Override
    public StringBuilder getSource(
            ConnectInfo connectInfo, InputTableInfo inputTableInfo, List<ColumnMapping> columnMapping) {
        return DataBaseService.super.getSource(connectInfo, inputTableInfo, columnMapping);
    }

    @Override
    public StringBuilder getSink(
            ConnectInfo connectInfo, OutputTableInfo outputTableInfo, List<ColumnInfo> inputColumns) {
        return DataBaseService.super.getSink(connectInfo, outputTableInfo, inputColumns);
    }

    /** 查询表的所有 */
    public List<String> queryPartitions(ConnectInfo connectInfo) {

        List<String> partitionList = new ArrayList<>();

        try {
            String sql = "show partitions " + connectInfo.getTableName();
            List<Map<String, Object>> query =
                    getClient().executeQuery(getSource(connectInfo), SqlQueryDTO.builder().sql(sql).build());
            query.forEach(
                    partitions -> partitions.forEach((k, v) -> partitionList.add(String.valueOf(v))));

        } catch (Exception e) {
            // do nothing
        }

        return partitionList;
    }

    /**
     * 根据指定分区获取分区信息
     *
     * @partition 此参数需要是field=value/field=value格式，可以是queryPartitions()方法的返回值
     */
    public Map<String, String> queryInfoByPartition(ConnectInfo connectInfo, String partition) {

        Map<String, String> partitionInfo = new HashMap<>();

        try {

            List<String> partitionList = new ArrayList<>();
            StringTokenizer tokenizer = new StringTokenizer(partition, "/=");

            while (tokenizer.hasMoreTokens()) {
                String p = String.format("%s='%s'", tokenizer.nextToken(), tokenizer.nextToken());
                partitionList.add(p);
            }

            String partitionValue = StringUtils.join(partitionList, ",");
            String sql =
                    String.format(
                            "desc formatted %s partition(%s)", connectInfo.getTableName(), partitionValue);

            List<Map<String, Object>> query =
                    getClient().executeQuery(getSource(connectInfo), SqlQueryDTO.builder().sql(sql).build());
            query.forEach(
                    info -> {
                        Object dataType = info.get("data_type");
                        String type = String.valueOf(dataType).trim();
                        if (Objects.nonNull(dataType) && "numRows".equals(type)) {
                            Object rowNum = info.get("comment");
                            partitionInfo.put(type, String.valueOf(rowNum).trim());
                        } else if (Objects.nonNull(dataType) && "totalSize".equals(type)) {
                            Object size = info.get("comment");
                            partitionInfo.put(type, String.valueOf(size).trim());
                        }
                    });
        } catch (Exception e) {
            // do nothing
        }

        return partitionInfo;
    }

    /** 只会刷新表的总存储量 */
    public void analyzeTableInfoWithoutScan(ConnectInfo connectInfo) {

        try {

            String sql;

            TableColumn tableColumn = queryTableColumn(connectInfo);
            List<TableColumnInfo> partitionColumnInfos = tableColumn.getPartitionColumnInfos();

            if (partitionColumnInfos.isEmpty()) {
                sql = " analyze table  " + connectInfo.getTableName() + " compute statistics noscan ";
            } else {
                List<String> partitions =
                        partitionColumnInfos.stream()
                                .map(TableColumnInfo::getField)
                                .collect(Collectors.toList());
                String partitionSql = StringUtils.join(partitions, ",");
                sql =
                        String.format(
                                " analyze table  "
                                        + connectInfo.getTableName()
                                        + " partition(%s) compute statistics noscan ",
                                partitionSql);
            }

            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().sql(sql).build();
            getClient().executeSqlWithoutResultSet(getSource(connectInfo), sqlQueryDTO);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}

package com.definesys.dehoop.admin.moudle.product.authorization.service;

import com.definesys.dehoop.admin.moudle.product.authorization.dao.ProductAuthorizationDao;
import com.definesys.dehoop.admin.moudle.product.authorization.pojo.FndTenantProductAuthorizations;
import com.definesys.dehoop.admin.moudle.product.authorization.pojo.dto.ProductAuthorizationInfo;
import com.definesys.dehoop.api.exception.TenantExceptionEnum;

import com.xdap.motor.entity.SnowflakeIdWorker;
import com.xdap.motor.exception.CommonBizExceptionEnum;
import com.xdap.motor.exception.XdapWarningException;
import com.xdap.motor.utils.Assert;
import com.xdap.motor.utils.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import org.apache.commons.collections.CollectionUtils;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
@Slf4j
public class ProductAuthorizationService {

    private final SnowflakeIdWorker snowflakeIdWorker;

    private final ProductAuthorizationDao productAuthorizationDao;

    /** 生成新的ProductKey */
    public String generateProductKey() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /** 类型转换 */
    public ProductAuthorizationInfo getProductAuthorizationInfo(
            FndTenantProductAuthorizations fndTenantProductAuthorizations) {

        if (fndTenantProductAuthorizations == null) {
            return null;
        }

        ProductAuthorizationInfo productAuthorizationInfo = new ProductAuthorizationInfo();
        BeanUtils.copyProperties(fndTenantProductAuthorizations, productAuthorizationInfo);
        if (StringUtils.isNotBlank(fndTenantProductAuthorizations.getWhiteList())) {
            productAuthorizationInfo.setWhiteList(
                    fndTenantProductAuthorizations.getWhiteList().split(","));
        }
        return productAuthorizationInfo;
    }

    /** 新增产品软件授权 */
    public void addProductAuthorization(ProductAuthorizationInfo productAuthorizationInfo) {

        // 字段校验
        Assert.isNotEmpty(
                productAuthorizationInfo.getName(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        // 检测名称重复
        if (productAuthorizationDao.getProductAuthorizationByName(productAuthorizationInfo.getName())
                != null) {
            throw new XdapWarningException(TenantExceptionEnum.PRODUCT_AUTHORIZATIONS_NAME_IS_DUPLICATE);
        }

        FndTenantProductAuthorizations fndTenantProductAuthorizations =
                new FndTenantProductAuthorizations();
        fndTenantProductAuthorizations.setId(snowflakeIdWorker.nextId());
        fndTenantProductAuthorizations.setName(productAuthorizationInfo.getName());
        fndTenantProductAuthorizations.setProductKey(UUID.randomUUID().toString().replace("-", ""));
        fndTenantProductAuthorizations.setWhiteList(
                StringUtils.join(productAuthorizationInfo.getWhiteList(), ","));
        productAuthorizationDao.insertProductAuthorizations(fndTenantProductAuthorizations);
    }

    /** 更新产品软件授权 */
    public ProductAuthorizationInfo updateProductAuthorization(
            ProductAuthorizationInfo productAuthorizationInfo) {

        // 字段校验
        Assert.isNotEmpty(
                productAuthorizationInfo.getId(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        Assert.isNotEmpty(
                productAuthorizationInfo.getName(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        Assert.isNotEmpty(
                productAuthorizationInfo.getProductKey(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        // 检测名称重复
        FndTenantProductAuthorizations productAuthorizationByName =
                productAuthorizationDao.getProductAuthorizationByName(productAuthorizationInfo.getName());
        if (productAuthorizationByName != null
                && !productAuthorizationInfo.getId().equals(productAuthorizationByName.getId())) {
            throw new XdapWarningException(TenantExceptionEnum.PRODUCT_AUTHORIZATIONS_NAME_IS_DUPLICATE);
        }

        FndTenantProductAuthorizations fndTenantProductAuthorizations =
                new FndTenantProductAuthorizations();
        BeanUtils.copyProperties(productAuthorizationInfo, fndTenantProductAuthorizations);
        fndTenantProductAuthorizations.setWhiteList(
                StringUtils.join(productAuthorizationInfo.getWhiteList(), ","));
        productAuthorizationDao.updateProductAuthorizations(fndTenantProductAuthorizations);

        return getProductAuthorizationInfo(
                productAuthorizationDao.getProductAuthorizationById(productAuthorizationInfo.getId()));
    }

    /** 查询当前租户所有产品软件授权 */
    public List<ProductAuthorizationInfo> queryProductAuthorizations() {

        List<ProductAuthorizationInfo> productAuthorizationInfoList = new ArrayList<>();

        List<FndTenantProductAuthorizations> productAuthorizations =
                productAuthorizationDao.queryProductAuthorizations();
        if (CollectionUtils.isNotEmpty(productAuthorizations)) {
            for (FndTenantProductAuthorizations productAuthorization : productAuthorizations) {
                productAuthorizationInfoList.add(getProductAuthorizationInfo(productAuthorization));
            }
        }

        return productAuthorizationInfoList;
    }

    /** 根据Id删除当前租户的产品软件授权 */
    public void deleteProductAuthorization(ProductAuthorizationInfo productAuthorizationInfo) {

        // 字段校验
        Assert.isNotEmpty(
                productAuthorizationInfo.getId(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        productAuthorizationDao.deleteProductAuthorizations(productAuthorizationInfo.getId());
    }
}

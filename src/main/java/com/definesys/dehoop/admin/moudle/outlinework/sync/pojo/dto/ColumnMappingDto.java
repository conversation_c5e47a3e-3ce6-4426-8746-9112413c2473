package com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.dto;

import lombok.Data;

import java.util.List;

@Data
public class ColumnMappingDto {

    private String fromTable;

    private String from;

    private String fromApiFieldId;

    private String fromTransform;

    private String fromFieldPath;

    private List<String> fromFieldPathList;

    private String fromFieldType;

    private String toTable;

    private String to;

    private String toFieldType;

    private String toFieldPath;

    private List<String> toFieldPathList;
}

package com.definesys.dehoop.admin.moudle.outlinework.work;

import com.definesys.dehoop.admin.moudle.executor.job.Job;
import com.definesys.dehoop.admin.moudle.executor.job.JobProps;
import com.definesys.dehoop.admin.moudle.executor.job.jobExecutor.DorisJob;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobOutlineWorks;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.JobWorkResources;
import com.definesys.dehoop.admin.moudle.outlinework.service.WorkNodeService;
import com.definesys.dehoop.admin.moudle.project.dao.ProjectDao;
import com.definesys.dehoop.admin.moudle.project.pojo.ProEnvironmentEntity;
import com.definesys.dehoop.admin.moudle.schedule.pojo.SchWorkVersions;
import com.definesys.dehoop.admin.moudle.schedule.pojo.dto.WorkVersionInfo;
import com.definesys.dehoop.api.enums.OutlineWorksType;

import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;

import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class DorisWorkNodeService extends WorkNodeService {

    private final ApplicationContext applicationContext;
    private final ProjectDao projectDao;

    @Override
    public void generateNodeProps(String id, JobProps jobProps) {

        super.generateNodeProps(id, jobProps);

        // 环境信息
        JobOutlineWorks jobOutlineWork = outlineWorkDao.getWorkById(id);
        jobProps.addCustomConfig(jobOutlineWork);

        ProEnvironmentEntity environmentEntity = projectDao.queryProEnvById(jobOutlineWork.getEnvId());
        jobProps.addCustomConfig(environmentEntity);
    }

    @Override
    public void generateNodeVersionProps(WorkVersionInfo versionInfo, JobProps jobProps) {
        super.generateNodeVersionProps(versionInfo, jobProps);
        jobProps.setResDependents(versionInfo.getResDependents());
    }

    @Override
    public void getWorkConfigVersion(SchWorkVersions workVersion, WorkVersionInfo workVersionInfo) {
        super.getWorkConfigVersion(workVersion, workVersionInfo);
        List<JobWorkResources> jobWorkResources =
                JSONArray.parseArray(workVersion.getResDependents(), JobWorkResources.class);
        if (jobWorkResources == null) {
            jobWorkResources = new ArrayList<>();
        }
        workVersionInfo.setResDependents(jobWorkResources);
    }

    public Job getJob() {
        return new DorisJob(applicationContext);
    }

    @Override
    public OutlineWorksType getNodeType() {
        return OutlineWorksType.DorisSQL;
    }

    @Override
    public boolean hasScript() {
        return true;
    }
}

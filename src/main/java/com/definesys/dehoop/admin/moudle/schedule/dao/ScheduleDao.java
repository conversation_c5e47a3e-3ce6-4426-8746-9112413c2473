package com.definesys.dehoop.admin.moudle.schedule.dao;

import com.definesys.dehoop.admin.moudle.schedule.pojo.*;
import com.definesys.dehoop.admin.moudle.schedule.pojo.dto.WorkInstanceInfo;
import com.definesys.dehoop.api.constant.*;
import com.definesys.mpaas.query.MpaasQuery;
import com.definesys.mpaas.query.MpaasQueryFactory;
import com.definesys.mpaas.query.db.PageQueryResult;
import com.definesys.mpaas.query.session.MpaasSession;

import com.xdap.motor.utils.StringUtils;

import org.springframework.stereotype.Repository;

import org.apache.commons.collections.CollectionUtils;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class ScheduleDao {

    private final MpaasQueryFactory sw;

    public ScheduleDao(MpaasQueryFactory sw) {

        this.sw = sw;
    }

    public void addSchWorkState(SchWorkStates schWorkState) {

        sw.buildQuery().doInsert(schWorkState);
    }

    public void addSchWorkState(List<SchWorkStates> schWorkState) {

        sw.buildQuery().doBatchInsert(schWorkState);
    }

    public void updateSchWorkState(SchWorkStates schWorkState) {

        sw.buildQuery()
                .eq("ID", schWorkState.getId())
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("STATE", schWorkState.getState())
                .update("OPT_TYPE", schWorkState.getOptType())
                .doUpdate(schWorkState);
    }

    /**
     * 更新状态
     *
     * @param id id
     * @param state 状态
     */
    public void updateWorkState(String id, String state) {

        MpaasQuery mpaasQuery =
                sw.buildQuery()
                        .eq("ID", id)
                        .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                        .update("STATE", state);

        // 如果是提交修改更新人
        if (WorkPublishStatusConstants.SUBMITTED.equals(state)) {
            mpaasQuery.update("LAST_UPDATED_BY", MpaasSession.getCurrentUser());
        } else if (WorkPublishStatusConstants.UN_PUBLISH.equals(state)) {
            mpaasQuery
                    .update("LAST_UPDATED_BY", MpaasSession.getCurrentUser())
                    .update("LAST_UPDATE_DATE", new Date());
        }

        mpaasQuery.doUpdate(SchWorkStates.class);
    }

    /**
     * 更新作业状态 同时更新更新人字段
     *
     * @param id id
     * @param state 状态
     */
    public void updateWorkStateWithUpdater(String id, String state) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("STATE", state)
                .update("LAST_UPDATED_BY", MpaasSession.getCurrentUser())
                .doUpdate(SchWorkStates.class);
    }

    /**
     * 更新状态
     *
     * @param businessId id
     * @param state 状态
     */
    public void updateWorkStateByBusinessId(String businessId, String state) {

        MpaasQuery mpaasQuery =
                sw.buildQuery()
                        .eq("BUSINESS_ID", businessId)
                        .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                        .update("STATE", state);

        // 如果是提交修改更新人
        if (WorkPublishStatusConstants.SUBMITTED.equals(state)) {
            mpaasQuery.update("LAST_UPDATED_BY", MpaasSession.getCurrentUser());
        } else if (WorkPublishStatusConstants.UN_PUBLISH.equals(state)) {
            mpaasQuery
                    .update("LAST_UPDATED_BY", MpaasSession.getCurrentUser())
                    .update("LAST_UPDATE_DATE", new Date());
        }

        mpaasQuery.doUpdate(SchWorkStates.class);
    }

    /** 更新状态 */
    public void updateWorkStateByBusinessIds(List<String> businessIds, String state) {

        sw.buildQuery()
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("STATE", state)
                .in("BUSINESS_ID", businessIds)
                .doUpdate(SchWorkStates.class);
    }

    /**
     * 更新opt类型
     *
     * @param id id
     * @param optType 类型
     */
    public void updateWorkOptType(String id, String optType) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("OPT_TYPE", optType)
                .doUpdate(SchWorkStates.class);
    }

    public void submitWorkState(List<String> idList, String comment) {

        if (idList.isEmpty()) {
            return;
        }

        sw.buildQuery()
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .in("ID", idList)
                .update("STATE", WorkPublishStatusConstants.UN_PUBLISH)
                .update("COMMENT", comment)
                .update("LAST_UPDATE_DATE", new Date())
                .doUpdate(SchWorkStates.class);
    }

    public void submitWorkState(String workStateId) {

        sw.buildQuery()
                .eq("ID", workStateId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("STATE", "SUBMITTED")
                .doUpdate(SchWorkStates.class);
    }

    public SchWorkStates getSchWorkState(String businessType, String businessId) {

        return sw.buildQuery()
                .eq("BUSINESS_TYPE", businessType)
                .eq("BUSINESS_ID", businessId)
                .doQueryFirst(SchWorkStates.class);
    }

    public List<SchWorkStates> getSchWorkStates(String businessType, List<String> businessIdList) {

        return sw.buildQuery()
                .eq("BUSINESS_TYPE", businessType)
                .in("BUSINESS_ID", businessIdList)
                .doQuery(SchWorkStates.class);
    }

    public SchWorkStates getSchWorkStateByBusinessId(String businessId) {

        return sw.buildQuery().eq("BUSINESS_ID", businessId).doQueryFirst(SchWorkStates.class);
    }

    public SchWorkStates getSchWorkState(String id) {

        return sw.buildQuery().eq("ID", id).doQueryFirst(SchWorkStates.class);
    }

    public void deleteSchWorkState(String businessType, String businessId) {

        sw.buildQuery()
                .eq("BUSINESS_TYPE", businessType)
                .eq("BUSINESS_ID", businessId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(SchWorkStates.class);
    }

    public void deleteSchWorkStateBatch(List<String> businessIdList) {

        sw.buildQuery()
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .in("BUSINESS_ID", businessIdList)
                .doDelete(SchWorkStates.class);
    }

    public PageQueryResult<SchWorkStates> queryPageSchWorkStates(SchReqDto schReqDto) {

        return sw.buildQuery()
                .eq("FLOW_ID", schReqDto.getFlowId())
                .eq("ENV_ID", schReqDto.getEnvId())
                .eq("STATE", WorkPublishStatusConstants.SUBMITTED)
                .doPageQuery(schReqDto.getPage(), schReqDto.getPageSize(), SchWorkStates.class);
    }

    public PageQueryResult<SchWorkStates> pageQuerySchWorkStatesWithEnableSubmitCheck(
            SchReqDto schReqDto, List<String> ids) {

        String sql =
                "select *\n"
                        + "from (select sws.ID                    id,\n"
                        + "             sws.BUSINESS_TYPE         businessType,\n"
                        + "             sws.BUSINESS_ID           businessId,\n"
                        + "             sws.BUSINESS_NAME         businessName,\n"
                        + "             sws.OPT_TYPE              optType,\n"
                        + "             sws.COMMENT               comment,\n"
                        + "             sws.STATE                 state,\n"
                        + "             sws.FLOW_ID               flowId,\n"
                        + "             sws.CREATED_BY            createdBy,\n"
                        + "             sws.CREATION_DATE         creationDate,\n"
                        + "             sws.LAST_UPDATED_BY       lastUpdatedBy,\n"
                        + "             sws.LAST_UPDATE_DATE      lastUpdatedDate,\n"
                        + "             sws.OBJECT_VERSION_NUMBER objectVersionNumber,\n"
                        + "             sws.TENANT_ID             tenantId,\n"
                        + "             sws.ENV_ID                envId,\n"
                        + "             sws.PROJECT_ID            projectId\n"
                        + "      from SCH_WORK_STATES sws\n"
                        + "               left join JOB_OUTLINE_WORKS jow\n"
                        + "                         on sws.BUSINESS_ID = jow.ID\n"
                        + "      where sws.FLOW_ID = #flowId\n"
                        + "        and sws.ENV_ID =  #envId\n"
                        + "        and sws.STATE != 'PUBLISHED'\n"
                        + "        and sws.STATE != 'UN_PUBLISH'\n"
                        + "        and sws.STATE != 'UN_CONFIRM'\n"
                        + "        and (sws.STATE = 'SUBMITTED'";
        if (CollectionUtils.isNotEmpty(ids)) {
            sql =
                    sql
                            + " or (jow.ID in (#ids) and (jow.RUNNING_STATUS = 'SUCCESS' or jow.TYPE = 'BRANCH_NODE' or jow.TYPE = 'MERGE_NODE'))";
        }
        sql =
                sql
                        + " or (jow.TYPE in ('DDL', 'DDLV', 'DorisDDL', 'DorisDDLV') and jow.RUNNING_STATUS = 'SUCCESS')) order by sws.LAST_UPDATE_DATE desc) alias";

        MpaasQuery mq =
                sw.buildQuery()
                        .sql(sql)
                        .setVar("flowId", schReqDto.getFlowId())
                        .setVar("envId", schReqDto.getEnvId());
        if (CollectionUtils.isNotEmpty(ids)) {
            mq.setVar("ids", ids);
        }
        return mq.noTenant()
                .doPageQuery(schReqDto.getPage(), schReqDto.getPageSize(), SchWorkStates.class);
    }

    public PageQueryResult<SchWorkStates> pageQuerySchWorkStatesWithDisableSubmitCheck(
            SchReqDto schReqDto, List<String> ids) {

        String sql =
                "select *\n"
                        + "from (select sws.ID                    id,\n"
                        + "             sws.BUSINESS_TYPE         businessType,\n"
                        + "             sws.BUSINESS_ID           businessId,\n"
                        + "             sws.BUSINESS_NAME         businessName,\n"
                        + "             sws.OPT_TYPE              optType,\n"
                        + "             sws.COMMENT               comment,\n"
                        + "             sws.STATE                 state,\n"
                        + "             sws.FLOW_ID               flowId,\n"
                        + "             sws.CREATED_BY            createdBy,\n"
                        + "             sws.CREATION_DATE         creationDate,\n"
                        + "             sws.LAST_UPDATED_BY       lastUpdatedBy,\n"
                        + "             sws.LAST_UPDATE_DATE      lastUpdatedDate,\n"
                        + "             sws.OBJECT_VERSION_NUMBER objectVersionNumber,\n"
                        + "             sws.TENANT_ID             tenantId,\n"
                        + "             sws.ENV_ID                envId,\n"
                        + "             sws.PROJECT_ID            projectId\n"
                        + "      from SCH_WORK_STATES sws\n"
                        + "               left join JOB_OUTLINE_WORKS jow\n"
                        + "                         on sws.BUSINESS_ID = jow.ID\n"
                        + "      where sws.FLOW_ID = #flowId\n"
                        + "        and sws.ENV_ID =  #envId\n"
                        + "        and sws.STATE != 'PUBLISHED'\n"
                        + "        and sws.STATE != 'UN_PUBLISH'\n"
                        + "        and sws.STATE != 'UN_CONFIRM'\n"
                        + "        and (sws.STATE = 'SUBMITTED'";
        if (CollectionUtils.isNotEmpty(ids)) {
            sql = sql + " or jow.ID in (#ids)";
        }
        sql =
                sql
                        + " or jow.TYPE in ('DDL', 'DDLV', 'DorisDDL', 'DorisDDLV')) order by sws.LAST_UPDATE_DATE desc) alias";

        MpaasQuery mq =
                sw.buildQuery()
                        .sql(sql)
                        .setVar("flowId", schReqDto.getFlowId())
                        .setVar("envId", schReqDto.getEnvId());
        if (CollectionUtils.isNotEmpty(ids)) {
            mq.setVar("ids", ids);
        }
        return mq.noTenant()
                .doPageQuery(schReqDto.getPage(), schReqDto.getPageSize(), SchWorkStates.class);
    }

    public SchWorkVersions getLatestWorkVersion(String workId, String type) {

        return sw.buildQuery()
                .eq("WORK_ID", workId)
                .eq("TYPE", type)
                .orderBy("ID", "desc")
                .doQueryFirst(SchWorkVersions.class);
    }

    public void addSchWorkVersion(SchWorkVersions schWorkVersions) {

        sw.buildQuery().doInsert(schWorkVersions);
    }

    public PageQueryResult<SchWorkVersions> queryPageSchWorkVersions(
            String workId, String type, Integer page, Integer pageSize) {

        return sw.buildQuery()
                .eq("WORK_ID", workId)
                .orderBy("ID", "desc")
                .doPageQuery(page, pageSize, SchWorkVersions.class);
    }

    public SchWorkVersions getWorkVersion(String versionId) {

        return sw.buildQuery().eq("ID", versionId).doQueryFirst(SchWorkVersions.class);
    }

    /**
     * 按ID列表查询版本
     *
     * @param versionId 版本ID列表
     * @return 查询结果
     */
    public List<SchWorkVersions> queryWorkVersion(List<String> versionId) {

        if (versionId.isEmpty()) {

            return new ArrayList<>();
        }

        return sw.buildQuery().in("ID", versionId).doQuery(SchWorkVersions.class);
    }

    public SchFlowWorks getSchFlowWorks(String flowId) {

        return sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("TYPE", "FLOW")
                .doQueryFirst(SchFlowWorks.class);
    }

    public SchFlowWorks getSchWork(String workId) {

        return sw.buildQuery().eq("WORK_ID", workId).doQueryFirst(SchFlowWorks.class);
    }

    public void deleteSchFlowWork(String workId) {

        sw.buildQuery()
                .eq("WORK_ID", workId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(SchFlowWorks.class);
    }

    public SchFlowWorks getSchFlowWork(String businessId, String businessType) {

        if ("FLOW".equals(businessType)) {
            return sw.buildQuery()
                    .eq("FLOW_ID", businessId)
                    .eq("TYPE", "FLOW")
                    .doQueryFirst(SchFlowWorks.class);
        } else if (WorkTypeConstants.WORK.equals(businessType)) {

            return sw.buildQuery()
                    .eq("WORK_ID", businessId)
                    .in(
                            "TYPE",
                            businessType,
                            WorkTypeConstants.DDL,
                            WorkTypeConstants.DDLV,
                            WorkTypeConstants.DORIS_DDL,
                            WorkTypeConstants.DORIS_DDLV)
                    .doQueryFirst(SchFlowWorks.class);
        } else {
            return sw.buildQuery()
                    .eq("WORK_ID", businessId)
                    .in("TYPE", businessType)
                    .doQueryFirst(SchFlowWorks.class);
        }
    }

    public void addSchWorkJob(SchWorkJobs schWorkJobs) {

        try {
            sw.buildQuery().doInsert(schWorkJobs);
        } catch (Exception e) {
            // do nothing
        }
    }

    public SchWorkJobs getSchWorkJob(String workId, String type) {

        return sw.buildQuery().eq("WORK_ID", workId).eq("TYPE", type).doQueryFirst(SchWorkJobs.class);
    }

    /**
     * 查询调度中的工作流
     *
     * @param flowId 工作流id
     * @return 查询结果
     */
    public SchWorkJobs getSchFlowJob(String flowId) {

        return sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("TYPE", WorkTypeConstants.FLOW)
                .doQueryFirst(SchWorkJobs.class);
    }

    public SchWorkJobs getRealWorkJob(String workId) {

        return sw.buildQuery()
                .eq("WORK_ID", workId)
                .eq("TYPE", WorkTypeConstants.REAL_WORK)
                .doQueryFirst(SchWorkJobs.class);
    }

    public List<SchWorkJobs> querySchFlowJob(String flowId) {

        return sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("TYPE", WorkTypeConstants.FLOW)
                .doQuery(SchWorkJobs.class);
    }

    public SchWorkJobs getSchWorkJobByInstanceId(String flowInstanceId, String workId) {

        return sw.buildQuery().eq("FLOW_INSTANCE_ID", flowInstanceId).doQueryFirst(SchWorkJobs.class);
    }

    /** 查询所有已发布作业流 */
    public List<SchFlowWorks> queryAllPublishFlows() {

        return sw.buildQuery()
                .eq("TYPE", "FLOW")
                .eq("IS_PUBLISHING", "ENABLE")
                .doQuery(SchFlowWorks.class);
    }

    public List<SchFlowWorks> queryFlowWorks(String flowId) {

        return sw.buildQuery().eq("FLOW_ID", flowId).eq("TYPE", "WORK").doQuery(SchFlowWorks.class);
    }

    /**
     * 查询所有的工作流下的发布后的作业(包括ddl)
     *
     * @param flowId 工作流id
     * @return 查询结果
     */
    public List<SchFlowWorks> queryAllFlowWorks(String flowId) {

        return sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("IS_PUBLISHING", DehoopConstant.ENABLE)
                .conjuctionAnd()
                .or()
                .eq("TYPE", WorkTypeConstants.WORK)
                .eq("TYPE", WorkTypeConstants.DDL)
                .eq("TYPE", WorkTypeConstants.DDLV)
                .doQuery(SchFlowWorks.class);
    }

    public void addSchWorkInstance(SchWorkInstances schWorkInstances) {

        try {
            sw.buildQuery().doInsert(schWorkInstances);
        } catch (Exception ignored) {
            ignored.getStackTrace();
        }
    }

    public List<SchWorkJobs> querySchWorkJobs(String flowId) {

        return sw.buildQuery().eq("FLOW_ID", flowId).doQuery(SchWorkJobs.class);
    }

    /**
     * 按作业id的集合以及作业的类型查询调度作业
     *
     * @param businessIds 作业id集合
     * @param businessType 作业类型
     * @return 查询结果
     */
    public List<SchWorkJobs> querySchWorkJobs(List<String> businessIds, String businessType) {

        if (businessIds.isEmpty()) {

            return new ArrayList<>();
        } else {

            return sw.buildQuery()
                    .in("WORK_ID", businessIds)
                    .eq("TYPE", businessType)
                    .doQuery(SchWorkJobs.class);
        }
    }

    public List<SchWorkJobs> querySchWorkJobs(String flowInstanceId, List<String> workIds) {

        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .in("WORK_ID", workIds)
                .doQuery(SchWorkJobs.class);
    }

    public SchWorkInstances getSchWorkInstanceByFlowInstanceId(String flowInstanceId) {
        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .doQueryFirst(SchWorkInstances.class);
    }

    /**
     * 查询powerJob 任务
     *
     * @param evnId
     * @return
     */
    public List<SchWorkJobs> querySchWorkJobsByEvnId(String evnId) {

        return sw.buildQuery()
                .noTenant()
                .sql(
                        "select t1.* from JOB_OUTLINE_WORKS t "
                                + " left join SCH_WORK_JOBS t1 on t.FLOW_ID = t1.FLOW_ID "
                                + " where t.ENV_ID = #ENV_ID and t.type = 'flow' "
                                + " and t1.TENANT_ID = #tenantId ")
                .setVar("ENV_ID", evnId)
                .setVar("tenantId", MpaasSession.getUserProfile().getTenantId())
                .doQuery(SchWorkJobs.class);
    }

    public void deleteWorkJob(String id) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(SchWorkJobs.class);
    }

    public SchWorkInstances getSchWorkInstance(String flowInstanceId, String workId) {

        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("WORK_ID", workId)
                .doQueryFirst(SchWorkInstances.class);
    }

    public void updateWorkInstanceState(String workInstanceId, String state) {

        log.info("修改WORK_INSTANCE_ID:" + workInstanceId + "状态 state:" + state);

        sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("STATE", state)
                .doUpdate(SchWorkInstances.class);
    }

    public void updateWorkInstanceExecuteId(String workInstanceId, String executeId) {

        log.info("修改WORK_INSTANCE_ID:" + workInstanceId + "状态 executeId:" + executeId);

        sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("EXECUTE_ID", executeId)
                .doUpdate(SchWorkInstances.class);
    }

    public void updateWorkInstanceStates(String flowInstanceId, String state) {

        sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .ne("TYPE", WorkTypeConstants.FLOW)
                .update("STATE", state)
                .doUpdate(SchWorkInstances.class);
    }

    public void initAllInstanceStates(String flowInstanceId) {

        sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("START_DATE", new Date())
                .update("END_DATE", null)
                .doUpdate(SchWorkInstances.class);
    }

    public void initInstanceDateAndState(String flowInstanceId, String state) {

        sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("STATE", state)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("START_DATE", new Date())
                .update("END_DATE", null)
                .doUpdate(SchWorkInstances.class);
    }

    public List<SchWorkInstances> queryWorkInstanceByFlow(String flowInstanceId, String state) {

        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("STATE", state)
                .ne("TYPE", WorkTypeConstants.FLOW)
                .doQuery(SchWorkInstances.class);
    }

    public void updateWorkInstanceStateByIds(List<String> workInstanceIds, String state) {

        if (workInstanceIds.isEmpty()) {

            return;
        }
        sw.buildQuery()
                .in("WORK_INSTANCE_ID", workInstanceIds)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("STATE", state)
                .doUpdate(SchWorkInstances.class);
    }

    public void updateFlowInstanceStartDate(String flowInstanceId, Date startDate) {

        sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("TYPE", WorkTypeConstants.FLOW)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("START_DATE", startDate)
                .doUpdate(SchWorkInstances.class);
    }

    public void updateFlowInstanceEndDate(String flowInstanceId, Date endDate) {

        sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("TYPE", WorkTypeConstants.FLOW)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("END_DATE", endDate)
                .doUpdate(SchWorkInstances.class);
    }

    public void initFlowInstanceDate(String flowInstanceId) {

        if (flowInstanceId == null || flowInstanceId.isEmpty()) {
            return;
        }

        sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("TYPE", WorkTypeConstants.FLOW)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("START_DATE", new Date())
                .update("END_DATE", null)
                .doUpdate(SchWorkInstances.class);
    }

    /**
     * 更新 workInstance state
     *
     * @param flowInstanceIds
     * @param state 类型
     */
    public void updateWorkInstanceByFlow(List<String> flowInstanceIds, String state) {

        sw.buildQuery()
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .in("flowInstanceId", flowInstanceIds)
                .update("state", state)
                .doUpdate(SchWorkInstances.class);
    }

    /**
     * 查询所有 指定工作流下的在运行实例 包括离线作业、工作流
     *
     * @param flowId
     * @param state
     * @return
     */
    public List<SchWorkInstances> queryWorkInstance(String flowId, String state) {
        return sw.buildQuery().eq("flowId", flowId).eq("state", state).doQuery(SchWorkInstances.class);
    }

    public List<SchWorkInstances> queryWorkInstances(String flowInstanceId, List<String> workIds) {

        if (workIds.isEmpty()) {
            return new ArrayList<>();
        }

        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .in("WORK_ID", workIds)
                .doQuery(SchWorkInstances.class);
    }

    public List<SchFlowWorks> queryAllFlows(String projectId) {

        return sw.buildQuery()
                .eq("PROJECT_ID", projectId)
                .eq("TYPE", "FLOW")
                .doQuery(SchFlowWorks.class);
    }

    public List<SchWorkInstances> queryRunningWorks(String flowInstanceId) {

        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .in("STATE", "PENDING", "DOING")
                .eq("TYPE", "WORK")
                .doQuery(SchWorkInstances.class);
    }

    public SchWorkInstances hasErrorWorkInstance(String flowInstanceId) {

        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("STATE", "ERROR")
                .doQueryFirst(SchWorkInstances.class);
    }

    public void updateFlowInstanceState(String flowInstanceId, String state) {
        if (flowInstanceId == null || flowInstanceId.isEmpty()) {
            return;
        }

        if (WorkRunStatusConstants.SUCCESS.equals(state)
                || WorkRunStatusConstants.ERROR.equals(state)) {

            sw.buildQuery()
                    .eq("FLOW_INSTANCE_ID", flowInstanceId)
                    .eq("TYPE", WorkTypeConstants.FLOW)
                    .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                    .update("END_DATE", new Date())
                    .update("STATE", state)
                    .doUpdate(SchWorkInstances.class);
        } else {
            sw.buildQuery()
                    .eq("FLOW_INSTANCE_ID", flowInstanceId)
                    .eq("TYPE", WorkTypeConstants.FLOW)
                    .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                    .update("STATE", state)
                    .doUpdate(SchWorkInstances.class);
        }
    }

    public void updateFlowAllInstanceState(String flowInstanceId, String state) {

        if (flowInstanceId == null || flowInstanceId.isEmpty()) {
            return;
        }

        MpaasQuery mpaasQuery =
                sw.buildQuery()
                        .eq("FLOW_INSTANCE_ID", flowInstanceId)
                        .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                        .update("STATE", state);

        if (WorkRunStatusConstants.SUCCESS.equals(state)
                || WorkRunStatusConstants.ERROR.equals(state)) {
            mpaasQuery.update("END_DATE", new Date());
        }

        mpaasQuery.doUpdate(SchWorkInstances.class);
    }

    public void updateWorkInstancePlanDate(String workInstanceId, Date planDate) {

        sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("PLAN_DATE", planDate)
                .doUpdate(SchWorkInstances.class);
    }

    public void updateWorkInstanceStartDate(String workInstanceId, Date startDate) {

        sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("START_DATE", startDate)
                .update("END_DATE", null)
                .doUpdate(SchWorkInstances.class);
    }

    public void updateWorkInstanceEndDate(String workInstanceId, Date endDate) {

        sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("END_DATE", endDate)
                .doUpdate(SchWorkInstances.class);
    }

    public void updateWorkInstanceStateAndEndDate(String workInstanceId, String state, Date endDate) {

        sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .update("END_DATE", endDate)
                .update("STATE", state)
                .doUpdate(SchWorkInstances.class);
    }

    public void updateWorkInstanceStateAndStartDate(
            String workInstanceId, String state, Date startDate) {

        sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .update("START_DATE", startDate)
                .update("END_DATE", null)
                .update("STATE", state)
                .doUpdate(SchWorkInstances.class);
    }

    public void initWorkInstanceDate(String workInstanceId) {

        sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("START_DATE", new Date())
                .update("END_DATE", null)
                .doUpdate(SchWorkInstances.class);
    }

    public void initWorkInstance(String workInstanceId, String executeId, String state) {

        sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("START_DATE", new Date())
                .update("END_DATE", null)
                .update("EXECUTE_ID", executeId)
                .update("STATE", state)
                .doUpdate(SchWorkInstances.class);
    }

    public void initFlowInstanceDate(String flowInstanceId, String state) {

        if (flowInstanceId == null || flowInstanceId.isEmpty()) {
            return;
        }

        sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("TYPE", WorkTypeConstants.FLOW)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("STATE", state)
                .update("START_DATE", new Date())
                .update("END_DATE", null)
                .doUpdate(SchWorkInstances.class);
    }

    public List<SchFlowWorks> queryProjectFlows(List<String> projectIds) {

        return sw.buildQuery()
                .in("PROJECT_ID", projectIds)
                .eq("TYPE", "FLOW")
                .doQuery(SchFlowWorks.class);
    }

    public List<SchWorkStates> queryFlowWorkStates(String flowId) {

        return sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .in("STATE", WorkPublishStatusConstants.UN_PUBLISH)
                .doQuery(SchWorkStates.class);
    }

    public List<SchWorkStates> queryFlowPublishedWorkStates(String flowId) {

        return sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("STATE", "PUBLISHED")
                .doQuery(SchWorkStates.class);
    }

    public List<SchWorkStates> queryWorkStates(List<String> workStateIds) {

        return sw.buildQuery().in("ID", workStateIds).doQuery(SchWorkStates.class);
    }

    public void updateSchFlowWorkVersion(
            String businessId,
            String businessType,
            String versionId,
            String director,
            String isPublishing) {

        if ("FLOW".equals(businessType)) {
            sw.buildQuery()
                    .eq("FLOW_ID", businessId)
                    .eq("TYPE", "FLOW")
                    .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                    .update("VERSION_ID", versionId)
                    .update("DIRECTOR", director)
                    .update("IS_PUBLISHING", isPublishing)
                    .doUpdate(SchFlowWorks.class);
        } else {
            sw.buildQuery()
                    .eq("WORK_ID", businessId)
                    .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                    .in("TYPE", businessType)
                    .update("VERSION_ID", versionId)
                    .update("DIRECTOR", director)
                    .update("IS_PUBLISHING", isPublishing)
                    .doUpdate(SchFlowWorks.class);
        }
    }

    public void updateSchFlowVersion(String flowId, String versionId, String director) {

        sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("TYPE", "FLOW")
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("VERSION_ID", versionId)
                .update("DIRECTOR", director)
                .doUpdate(SchFlowWorks.class);
    }

    public SchWorkInstances getSchFlowInstance(String flowInstanceId) {

        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("TYPE", "FLOW")
                .doQueryFirst(SchWorkInstances.class);
    }

    public SchWorkInstances getSchWorkInstance(String workInstanceId) {

        return sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .doQueryFirst(SchWorkInstances.class);
    }

    public SchWorkInstances getLatestFlowInstance(String flowId) {

        if (StringUtils.isBlank(flowId)) {
            return null;
        }

        return sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("TYPE", "FLOW")
                .orderBy("ID", "desc")
                .doQueryFirst(SchWorkInstances.class);
    }

    /**
     * 新增运行中作业流
     *
     * @param schFlowWorks
     */
    public void addSchFlowWork(SchFlowWorks schFlowWorks) {

        sw.buildQuery().doInsert(schFlowWorks);
    }

    public void deleteSchFlowWorkByFlowId(String flowId) {
        sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(SchFlowWorks.class);
    }

    public Integer countWorkNum(String director, List<String> workType, List<String> projectIds) {

        Map<String, Object> result;
        if (director == null) {
            if ("REAL_WORK".equals(workType.get(0))) {
                result =
                        sw.buildQuery()
                                .sql(
                                        "select count(1) sum\n"
                                                + "from (select sfw.TYPE type, jrw.PROJECT_ID projectId\n"
                                                + "      from SCH_FLOW_WORKS sfw\n"
                                                + "               left join JOB_REAL_WORKS jrw on sfw.WORK_ID = jrw.ID) alia\n"
                                                + "where type in (#workType)\n"
                                                + "  and projectId in (#projectIds)")
                                .setVar("projectIds", projectIds)
                                .setVar("workType", workType)
                                .noTenant()
                                .doQueryFirst();
            } else {
                result =
                        sw.buildQuery()
                                .sql(
                                        ""
                                                + "select count(1) sum\n"
                                                + "from (select sfw.TYPE type, jow.PROJECT_ID projectId\n"
                                                + "      from SCH_FLOW_WORKS sfw\n"
                                                + "               left join JOB_OUTLINE_WORKS jow on sfw.FLOW_ID = jow.ID and jow.TYPE = 'FLOW') alia\n"
                                                + "where type in (#workType) \n"
                                                + "  and projectId in (#projectIds) ")
                                .setVar("projectIds", projectIds)
                                .setVar("workType", workType)
                                .noTenant()
                                .doQueryFirst();
            }
        } else {
            if ("REAL_WORK".equals(workType.get(0))) {
                result =
                        sw.buildQuery()
                                .sql(
                                        "select count(*) sum from SCH_FLOW_WORKS sfw left join JOB_REAL_WORKS jrw on jrw.ID = sfw.WORK_ID where sfw.TYPE in (#workType) and sfw.DIRECTOR = #director and jrw.PROJECT_ID is not null")
                                .setVar("director", director)
                                .setVar("workType", workType)
                                .noTenant()
                                .doQueryFirst();
            } else {
                result =
                        sw.buildQuery()
                                .sql(
                                        "select count(*) sum from SCH_FLOW_WORKS sfw where sfw.TYPE in (#workType) and sfw.DIRECTOR = #director and sfw.PROJECT_ID is not null")
                                .setVar("director", director)
                                .setVar("workType", workType)
                                .noTenant()
                                .doQueryFirst();
            }
        }

        try {
            return Integer.parseInt(String.valueOf(result.get("sum")));
        } catch (Exception e) {
            return 0;
        }
    }

    public Integer countTagNum(String director, List<String> projectIds) {

        Map<String, Object> result;
        if (director == null) {
            result =
                    sw.buildQuery()
                            .sql(
                                    ""
                                            + "select count(1) sum \n"
                                            + "                         from SCH_FLOW_WORKS sfw\n"
                                            + "                                  join TAG_LABELS jow on sfw.WORK_ID = jow.ID and sfw.TYPE = 'TAG'"
                                            + "  where jow.PROJECT_ID in (#projectIds) "
                                            + " and sfw.TENANT_ID = #tenantId ")
                            .setVar("projectIds", projectIds)
                            .setVar("tenantId", MpaasSession.getUserProfile().getTenantId())
                            .noTenant()
                            .doQueryFirst();
        } else {
            result =
                    sw.buildQuery()
                            .sql(
                                    "select count(*) sum from SCH_FLOW_WORKS sfw where sfw.TYPE = 'TAG' and sfw.DIRECTOR = #director and sfw.TENANT_ID = #tenantId")
                            .setVar("director", director)
                            .setVar("tenantId", MpaasSession.getUserProfile().getTenantId())
                            .noTenant()
                            .doQueryFirst();
        }

        try {
            return Integer.parseInt(String.valueOf(result.get("sum")));
        } catch (Exception e) {
            return 0;
        }
    }

    public List<WorkInstanceInfo> queryWorkInstances(SchReqDto schReqDto) {

        if (schReqDto.getFlowId() != null && schReqDto.getFlowId().isEmpty()) {
            schReqDto.setFlowId(null);
        }

        MpaasQuery query;
        if ("REAL_WORK".equals(schReqDto.getWorkType())) {
            query =
                    sw.buildQuery()
                            .sql(
                                    "select *\n"
                                            + "from (select swi.STATE                                        state,\n"
                                            + "             swi.WORK_ID                                      workId,\n"
                                            + "             swi.TYPE                                         type,\n"
                                            + "             date_format(swi.START_DATE, '%Y-%m-%d')          startDate,\n"
                                            + "             DATE_FORMAT(swi.START_DATE, '%k')                startHour,\n"
                                            + "             IF(\n"
                                            + "                             DATE_FORMAT(DATE_ADD(swi.START_DATE, INTERVAL 1 DAY), '%Y-%m-%d') <=\n"
                                            + "                             DATE_FORMAT(swi.END_DATE, '%Y-%m-%d') or END_DATE is null,\n"
                                            + "                             24,\n"
                                            + "                             DATE_FORMAT(swi.END_DATE, '%k')) endHour,\n"
                                            + "             swi.DIRECTOR                                     director,\n"
                                            + "             jrw.PROJECT_ID                                   projectId\n"
                                            + "      from SCH_WORK_INSTANCES swi\n"
                                            + "               left join JOB_REAL_WORKS jrw\n"
                                            + "                         on swi.WORK_ID = jrw.ID\n"
                                            + "      where swi.ID in (select max(ID)\n"
                                            + "                       from SCH_WORK_INSTANCES\n"
                                            + "                       where TYPE = 'REAL_WORK'\n"
                                            + "                       group by WORK_ID)\n"
                                            + "        and jrw.PROJECT_ID is not null) alia")
                            .eq("director", schReqDto.getDirector())
                            .eq("type", schReqDto.getWorkType())
                            .eq("startDate", schReqDto.getSearchDate());
        } else {
            query =
                    sw.buildQuery()
                            .sql(
                                    "select  *\n"
                                            + "from (select swi.STATE                                        state,\n"
                                            + "             swi.WORK_ID                                      workId,\n"
                                            + "             swi.FLOW_ID                                      flowId,\n"
                                            + "             swi.TYPE                                         type,\n"
                                            + "             date_format(swi.START_DATE, '%Y-%m-%d')          startDate,\n"
                                            + "             DATE_FORMAT(swi.START_DATE, '%k')                startHour,\n"
                                            + "             IF(\n"
                                            + "                             DATE_FORMAT(DATE_ADD(swi.START_DATE, INTERVAL 1 DAY), '%Y-%m-%d') <=\n"
                                            + "                             DATE_FORMAT(swi.END_DATE, '%Y-%m-%d') or END_DATE is null,\n"
                                            + "                             24,\n"
                                            + "                             DATE_FORMAT(swi.END_DATE, '%k')) endHour,\n"
                                            + "             swi.DIRECTOR                                     director,\n"
                                            + "             jow.PROJECT_ID                                   projectId\n"
                                            + "      from SCH_WORK_INSTANCES swi\n"
                                            + "               left join SCH_WORK_VERSIONS swv on swi.VERSION_ID = swv.ID\n"
                                            + "               left join JOB_OUTLINE_WORKS jow on jow.FLOW_ID = swi.FLOW_ID and jow.TYPE = 'FLOW') alia")
                            .eq("director", schReqDto.getDirector())
                            .eq("flowId", schReqDto.getFlowId())
                            .eq("startDate", schReqDto.getSearchDate());

            if (CollectionUtils.isNotEmpty(schReqDto.getWorkTypes())) {
                query.in("type", schReqDto.getWorkTypes());
            }
        }

        if (schReqDto.getProjectIds() != null) {
            return query.in("projectId", schReqDto.getProjectIds()).doQuery(WorkInstanceInfo.class);
        } else {
            return query.doQuery(WorkInstanceInfo.class);
        }
    }

    public List<SchWorkQueues> queryWorkQueues(String flowInstanceId) {

        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .orderBy("WORK_INDEX", "asc")
                .doQuery(SchWorkQueues.class);
    }

    public void addWorkQueues(SchWorkQueues schWorkQueues) {

        sw.buildQuery().doInsert(schWorkQueues);
    }

    public void deleteWorkQueue(String id) {

        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(SchWorkQueues.class);
    }

    /**
     * 查询最新的作业实例
     *
     * @param workId 作业ID
     * @param versionId 版本ID
     * @return 查询结果
     */
    public SchWorkInstances getNewlyWorkInstance(String workId, String versionId, String workType) {

        if (WorkTypeConstants.FLOW.equals(workType)) {

            return sw.buildQuery()
                    .eq("FLOW_ID", workId)
                    .eq("TYPE", WorkTypeConstants.FLOW)
                    .eq("VERSION_ID", versionId)
                    .orderBy("CREATION_DATE", "desc")
                    .doQueryFirst(SchWorkInstances.class);
        } else {

            return sw.buildQuery()
                    .eq("WORK_ID", workId)
                    .eq("VERSION_ID", versionId)
                    .orderBy("CREATION_DATE", "desc")
                    .doQueryFirst(SchWorkInstances.class);
        }
    }

    /**
     * 查询标签实例
     *
     * @param schReqDto
     * @return
     */
    public List<WorkInstanceInfo> queryTagInstances(SchReqDto schReqDto) {

        return sw.buildQuery()
                .sql(
                        "select * from (SELECT "
                                + "  swi.STATE state, "
                                + "  swi.WORK_ID workId, "
                                + "  swi.FLOW_ID flowId, "
                                + "  swi.TYPE type, "
                                + "  date_format( swi.START_DATE, '%Y-%m-%d' ) startDate, "
                                + "  DATE_FORMAT( swi.START_DATE, '%k' ) startHour, "
                                + " IF "
                                + "  ( "
                                + "   DATE_FORMAT( DATE_ADD( swi.START_DATE, INTERVAL 1 DAY ), '%Y-%m-%d' ) <= DATE_FORMAT( swi.END_DATE, '%Y-%m-%d' ), "
                                + "   24, "
                                + "   DATE_FORMAT( swi.END_DATE, '%k' )  "
                                + "  ) endHour, "
                                + "  swi.DIRECTOR director , "
                                + " tcl.CATEGORY_ID   "
                                + " FROM "
                                + "  SCH_WORK_INSTANCES swi "
                                + " left join TAG_CATEGORY_LABEL tcl on tcl.LABEL_ID = swi.WORK_ID where swi.TYPE = 'TAG' and swi.TENANT_ID = #tenantId ) alia")
                .setVar("tenantId", MpaasSession.getUserProfile().getTenantId())
                .eq("CATEGORY_ID", schReqDto.getCategoryId())
                .eq("startDate", schReqDto.getSearchDate())
                .doQuery(WorkInstanceInfo.class);
    }

    public List<SchWorkInstances> queryPendingWorkInstances(String flowInstanceId) {

        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("STATE", "PENDING")
                .doQuery(SchWorkInstances.class);
    }

    public void updateSchWorkJobInstanceId(Long jobId, Long instanceId) {

        sw.buildQuery()
                .eq("JOB_ID", jobId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("INSTANCE_ID", instanceId)
                .doUpdate(SchWorkJobs.class);
    }

    /**
     * 添加实例的依赖关系
     *
     * @param schWorkInstanceQueue 作业依赖关系对象
     */
    public void addSchWorkInstanceQueue(SchWorkInstanceQueue schWorkInstanceQueue) {

        sw.buildQuery().doInsert(schWorkInstanceQueue);
    }

    /**
     * 使用上游作业实例的id查询依赖关系
     *
     * @param fromWorkInstanceId 上游节点的实例id
     * @return 查询结果
     */
    public List<SchWorkInstanceQueue> querySchWorkInstanceQueueByUpNode(String fromWorkInstanceId) {

        return sw.buildQuery()
                .eq("FROM_WORK_INSTANCE_ID", fromWorkInstanceId)
                .doQuery(SchWorkInstanceQueue.class);
    }

    /**
     * 使用下游作业实例id查询依赖关系
     *
     * @param toWorkInstanceId 下游作业的实例id
     * @return 查询结果
     */
    public List<SchWorkInstanceQueue> querySchWorkInstanceQueueByDownNode(String toWorkInstanceId) {

        return sw.buildQuery()
                .eq("TO_WORK_INSTANCE_ID", toWorkInstanceId)
                .doQuery(SchWorkInstanceQueue.class);
    }

    /**
     * 删除作业实例依赖对象
     *
     * @param fromWorkInstanceId 上游依赖实例id
     * @param toWorkInstanceId 下游依赖实例id
     */
    public void deleteSchWorkInstanceQueue(String fromWorkInstanceId, String toWorkInstanceId) {

        sw.buildQuery()
                .eq("FROM_WORK_INSTANCE_ID", fromWorkInstanceId)
                .eq("TO_WORK_INSTANCE_ID", toWorkInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .doDelete(SchWorkInstanceQueue.class);
    }

    /**
     * 添加工作流的依赖关系
     *
     * @param schFlowInstanceQueues 工作流的依赖关系
     */
    public void addSchFlowInstanceQueue(SchFlowInstanceQueues schFlowInstanceQueues) {

        if (!schFlowInstanceQueues
                .getToFlowInstanceId()
                .equals(schFlowInstanceQueues.getFromFlowInstanceId())) {

            sw.buildQuery().doInsert(schFlowInstanceQueues);
        } else {

            log.debug("工作流实例依赖存在上游依赖与下游依赖相同的情况");
        }
    }

    /**
     * 按照上游依赖的实例id查询依赖队列
     *
     * @param fromFlowInstanceId 上游依赖id
     * @return 查询结果
     */
    public List<SchFlowInstanceQueues> queryFlowInstanceQueueByUp(
            String fromFlowInstanceId, String hasPushed) {

        return sw.buildQuery()
                .eq("FROM_FLOW_INSTANCE_ID", fromFlowInstanceId)
                .eq("HAS_PUSHED", hasPushed)
                .doQuery(SchFlowInstanceQueues.class);
    }

    public List<SchFlowInstanceQueues> queryFlowQueueByUpFlowId(
            String fromFlowFlowId, String hasPushed) {

        return sw.buildQuery()
                .eq("FROM_FLOW_ID", fromFlowFlowId)
                .eq("HAS_PUSHED", hasPushed)
                .eq("TYPE", DehoopConstant.UN_PUBLISH_FLOW)
                .doQuery(SchFlowInstanceQueues.class);
    }

    /**
     * 按照下游依赖的实例id查询依赖队列
     *
     * @param toFlowInstanceId 下游依赖id
     * @return 查询结果
     */
    public List<SchFlowInstanceQueues> queryFlowInstanceQueueByDown(
            String toFlowInstanceId, String hasPushed) {

        return sw.buildQuery()
                .eq("TO_FLOW_INSTANCE_ID", toFlowInstanceId)
                .eq("HAS_PUSHED", hasPushed)
                .doQuery(SchFlowInstanceQueues.class);
    }

    /**
     * 按照下游依赖的实例id查询依赖队列
     *
     * @param toFlowInstanceId 下游依赖id
     * @return 查询结果
     */
    public List<SchFlowInstanceQueues> queryFlowInstanceQueueByDown(String toFlowInstanceId) {

        return sw.buildQuery()
                .eq("TO_FLOW_INSTANCE_ID", toFlowInstanceId)
                .ne("TYPE", DehoopConstant.UN_PUBLISH_FLOW)
                .doQuery(SchFlowInstanceQueues.class);
    }

    /**
     * 按照上游依赖的实例id查询依赖队列
     *
     * @param fromFlowInstanceId 上游依赖id
     * @return 查询结果
     */
    public List<SchFlowInstanceQueues> queryFlowInstanceQueueByUp(String fromFlowInstanceId) {

        return sw.buildQuery()
                .eq("FROM_FLOW_INSTANCE_ID", fromFlowInstanceId)
                .ne("TYPE", DehoopConstant.UN_PUBLISH_FLOW)
                .doQuery(SchFlowInstanceQueues.class);
    }

    /**
     * 查询依赖关系
     *
     * @param fromFlowInstanceId 上游实例id
     * @param toFlowInstanceId 下游实例id
     * @return 查询结果
     */
    public SchFlowInstanceQueues getFlowInstanceQueue(
            String fromFlowInstanceId, String toFlowInstanceId) {

        return sw.buildQuery()
                .eq("FROM_FLOW_INSTANCE_ID", fromFlowInstanceId)
                .eq("TO_FLOW_INSTANCE_ID", toFlowInstanceId)
                .doQueryFirst(SchFlowInstanceQueues.class);
    }

    /**
     * 更新工作流实例依赖的状态
     *
     * @param fromFlowInstanceId 上游实例id
     * @param toFlowInstanceId 下游实例id
     * @param hasPushed 是否发布过这个事件
     */
    public void updateFlowInstanceQueueState(
            String fromFlowInstanceId, String toFlowInstanceId, String hasPushed) {
        sw.buildQuery()
                .eq("FROM_FLOW_INSTANCE_ID", fromFlowInstanceId)
                .eq("TO_FLOW_INSTANCE_ID", toFlowInstanceId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("HAS_PUSHED", hasPushed)
                .doUpdate(SchFlowInstanceQueues.class);
    }

    public void updateFlowQueueState(String id, String type, String fromFlowInstanceId) {
        sw.buildQuery()
                .eq("ID", id)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .update("FROM_FLOW_INSTANCE_ID", fromFlowInstanceId)
                .update("TYPE", type)
                .doUpdate(SchFlowInstanceQueues.class);
    }

    /**
     * 获取最新的工作流实例
     *
     * @param flowId 工作流id
     * @param versionId 版本id
     * @param flowInstanceId 需要排除实例id
     * @return 查询结果
     */
    public SchWorkInstances getLatestFlowInstance(
            String flowId, String versionId, String flowInstanceId) {

        return sw.buildQuery()
                .ne("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("FLOW_ID", flowId)
                .eq("VERSION_ID", versionId)
                .eq("TYPE", "FLOW")
                .orderBy("ID", "desc")
                .doQueryFirst(SchWorkInstances.class);
    }

    /**
     * 获取最新的工作流实例
     *
     * @param flowInstanceId 工作流实例
     * @return 查询结果
     */
    public SchWorkInstances getFlowInstance(String flowInstanceId) {

        return sw.buildQuery()
                .eq("FLOW_INSTANCE_ID", flowInstanceId)
                .eq("TYPE", "FLOW")
                .doQueryFirst(SchWorkInstances.class);
    }

    public SchWorkInstances getWorkInstance(String workInstanceId) {

        return sw.buildQuery()
                .eq("WORK_INSTANCE_ID", workInstanceId)
                .eq("TYPE", "WORK")
                .doQueryFirst(SchWorkInstances.class);
    }

    /**
     * 获取最新的工作流实例
     *
     * @param flowInstanceId 工作流实例
     * @return 查询结果
     */
    public List<SchWorkInstances> queryFlowInstanceByIdList(List<String> flowInstanceId) {

        if (flowInstanceId.isEmpty()) {

            return new ArrayList<>();
        } else {

            return sw.buildQuery()
                    .in("FLOW_INSTANCE_ID", flowInstanceId)
                    .eq("TYPE", "FLOW")
                    .doQuery(SchWorkInstances.class);
        }
    }

    /**
     * 查询调度作业
     *
     * @param flowIds 工作流id
     * @return 查询结果
     */
    public List<SchWorkJobs> querySchJobByFlowIds(List<String> flowIds) {

        return sw.buildQuery().eq("TYPE", "FLOW").in("FLOW_ID", flowIds).doQuery(SchWorkJobs.class);
    }

    /**
     * 获取最新的实例
     *
     * @param businessType 作业类型
     * @param businessId 作业id
     * @return 查询结果
     */
    public SchWorkInstances getNewlyInstance(String businessType, String businessId) {

        return sw.buildQuery()
                .eq("WORK_ID", businessId)
                .eq("TYPE", businessType)
                .orderBy("CREATION_DATE", "desc")
                .doQueryFirst(SchWorkInstances.class);
    }

    public void updateSchFlowWorksPublish(String workId, String type, String isPublishing) {

        if (WorkTypeConstants.FLOW.equals(type)) {
            sw.buildQuery()
                    .eq("FLOW_ID", workId)
                    .eq("TYPE", type)
                    .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                    .update("IS_PUBLISHING", isPublishing)
                    .doUpdate(SchFlowWorks.class);
        } else {
            sw.buildQuery()
                    .eq("WORK_ID", workId)
                    .eq("TYPE", type)
                    .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                    .update("IS_PUBLISHING", isPublishing)
                    .doUpdate(SchFlowWorks.class);
        }
    }

    /**
     * 更新publish状态
     *
     * @param flowId 工作流id
     * @param isPublishing 发布状态
     */
    public void updateFlowWorkPubByFlowId(String flowId, String isPublishing) {
        sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("TENANT_ID", MpaasSession.getUserProfile().getTenantId())
                .conjuctionAnd()
                .or()
                .eq("TYPE", WorkTypeConstants.WORK)
                .eq("TYPE", WorkTypeConstants.DDL)
                .eq("TYPE", WorkTypeConstants.DDLV)
                .update("IS_PUBLISHING", isPublishing)
                .doUpdate(SchFlowWorks.class);
    }

    /**
     * 根据workId获取运行中实时作业实例
     *
     * @param workId 作业id
     */
    public List<SchWorkInstances> queryRunningRealWorkInstanceByWorkId(String workId) {

        return sw.buildQuery()
                .eq("TYPE", WorkTypeConstants.REAL_WORK)
                .eq("WORK_ID", workId)
                .eq("STATE", WorkRunStatusConstants.RUNNING)
                .doQuery(SchWorkInstances.class);
    }

    public List<SchWorkStates> queryFlowIsUnConfirm(String flowId) {
        return sw.buildQuery()
                .eq("FLOW_ID", flowId)
                .eq("STATE", "UN_CONFIRM")
                .doQuery(SchWorkStates.class);
    }

    public SchWorkInstances getSchWorkInstancesByExecuteId(String executeId) {
        return sw.buildQuery().eq("EXECUTE_ID", executeId).doQueryFirst(SchWorkInstances.class);
    }

    public void updateExecuteId(String id, String executeId) {

        sw.buildQuery().eq("ID", id).update("EXECUTE_ID", executeId).doUpdate(SchWorkInstances.class);
    }

    public void endRealWorkInstanceByExecuteId(String executeId, String state) {

        sw.buildQuery()
                .eq("EXECUTE_ID", executeId)
                .eq("TYPE", WorkTypeConstants.REAL_WORK)
                .update("STATE", state)
                .update("END_DATE", new Date())
                .doUpdate(SchWorkInstances.class);
    }

    public List<SchWorkInstances> queryFlowByInstanceId(String flowInstanceId) {

        return sw.buildQuery().eq("FLOW_INSTANCE_ID", flowInstanceId).doQuery(SchWorkInstances.class);
    }
}

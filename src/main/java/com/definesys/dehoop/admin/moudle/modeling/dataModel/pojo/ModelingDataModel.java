package com.definesys.dehoop.admin.moudle.modeling.dataModel.pojo;

import com.definesys.dehoop.api.entity.XdapBasePojo;
import com.definesys.mpaas.query.annotation.Column;
import com.definesys.mpaas.query.annotation.Style;
import com.definesys.mpaas.query.annotation.Table;
import com.definesys.mpaas.query.annotation.TenantEnable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年10月22日 17:53
 */
@EqualsAndHashCode(callSuper = true)
@Table(value = "MODELING_DATA_MODEL")
@Style
@Data
@TenantEnable
public class ModelingDataModel extends XdapBasePojo {

    private String id;

    @Column("WEB_CONFIG")
    private String webConfig;

    @Column("RELATION_CONFIG")
    private String relationConfig;

    @Column("BUSINESS_PROCESS_ID")
    private String businessProcessId;

    @Column("PROJECT_ID")
    private String projectId;
}

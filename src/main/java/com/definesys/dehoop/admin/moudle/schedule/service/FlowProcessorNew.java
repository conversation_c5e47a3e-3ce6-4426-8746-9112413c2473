package com.definesys.dehoop.admin.moudle.schedule.service;

import com.definesys.dehoop.admin.moudle.outlinework.service.RedisLockService;

import com.github.kfcfans.powerjob.worker.core.processor.ProcessResult;
import com.github.kfcfans.powerjob.worker.core.processor.TaskContext;
import com.github.kfcfans.powerjob.worker.core.processor.sdk.BasicProcessor;

import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/9/23 16:52
 * @description
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FlowProcessorNew implements BasicProcessor {

    /** 工作流执行 */
    private final FlowProcessor flowProcessor;

    private final RedisLockService redisLockService;
    private static final String LOCK_FLOW_PROCESSOR = "LOCK_FLOW_PROCESSOR";

    /** 工作流执行 */
    @Override
    public ProcessResult process(TaskContext context) throws Exception {

        boolean isLocked = redisLockService.acquireLock(LOCK_FLOW_PROCESSOR + ":" + context.getJobId());
        log.info("JOBID: " + context.getJobId() + ", " + LOCK_FLOW_PROCESSOR + "外部调度锁: " + isLocked);
        if (!isLocked) {
            log.error(context.getJobId() + "：" + "重复调度");
            return new ProcessResult(true, "短时间内重复调度");
        }

        if (!isLocked) {
            log.error("重复调度后依然会生成作业流实例检测");
        }

        return flowProcessor.process(context);
    }
}

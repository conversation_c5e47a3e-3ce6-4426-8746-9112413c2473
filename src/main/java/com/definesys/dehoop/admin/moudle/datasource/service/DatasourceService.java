package com.definesys.dehoop.admin.moudle.datasource.service;

import com.definesys.dehoop.admin.moudle.connect.pojo.ConnectInfo;
import com.definesys.dehoop.admin.moudle.connect.service.DataBaseFactory;
import com.definesys.dehoop.admin.moudle.connect.service.DorisServiceNew;
import com.definesys.dehoop.admin.moudle.datasource.dao.DatasourceDao;
import com.definesys.dehoop.admin.moudle.datasource.pojo.DatasourceEntity;
import com.definesys.dehoop.admin.moudle.datasource.pojo.DatasourceReqDto;
import com.definesys.dehoop.admin.moudle.datasource.pojo.QueryType;
import com.definesys.dehoop.admin.moudle.datasource.pojo.ResDatasourceAuthority;
import com.definesys.dehoop.admin.moudle.datasource.pojo.dto.*;
import com.definesys.dehoop.admin.moudle.outlinework.sync.dao.SyncWorkDao;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.JobSyncWorkApiConfigs;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.JobSyncWorkConfig;
import com.definesys.dehoop.admin.moudle.project.dao.ProjectDao;
import com.definesys.dehoop.admin.moudle.project.pojo.ProEnvDatasource;
import com.definesys.dehoop.admin.moudle.user.dao.UserDao;
import com.definesys.dehoop.admin.moudle.user.pojo.FndUsers;
import com.definesys.dehoop.api.constant.DehoopConstant;
import com.definesys.dehoop.api.constant.JsonFieldTypeConstant;
import com.definesys.dehoop.api.constant.SQLConstant;
import com.definesys.dehoop.api.enums.DataBaseType;
import com.definesys.dehoop.api.enums.DataSourceType;
import com.definesys.dehoop.api.exception.DataSourceExceptionEnum;
import com.definesys.dehoop.api.exception.FixExceptionEnum;
import com.definesys.dehoop.api.properties.AttachmentProperties;
import com.definesys.mpaas.query.db.PageQueryResult;
import com.definesys.mpaas.query.session.MpaasSession;

import com.xdap.motor.entity.SnowflakeIdWorker;
import com.xdap.motor.exception.CommonBizExceptionEnum;
import com.xdap.motor.exception.XDapBizException;
import com.xdap.motor.i18n.I18nService;
import com.xdap.motor.utils.Assert;
import com.xdap.motor.vo.PageRespHelper;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DatasourceService {

    private final DatasourceDao datasourceDao;

    private final SnowflakeIdWorker snowflakeIdWorker;

    private final UserDao userDao;

    private final AttachmentProperties attachmentProperties;

    private final ProjectDao projectDao;

    private final SyncWorkDao syncWorkDao;

    private final I18nService i18nService;

    private final DorisServiceNew dorisServiceNew;

    public DatasourceService(
            DatasourceDao datasourceDao,
            SnowflakeIdWorker snowflakeIdWorker,
            UserDao userDao,
            AttachmentProperties attachmentProperties,
            ProjectDao projectDao,
            SyncWorkDao syncWorkDao,
            I18nService i18nService,
            DorisServiceNew dorisServiceNew) {

        this.datasourceDao = datasourceDao;

        this.snowflakeIdWorker = snowflakeIdWorker;

        this.userDao = userDao;

        this.attachmentProperties = attachmentProperties;

        this.projectDao = projectDao;

        this.syncWorkDao = syncWorkDao;
        this.i18nService = i18nService;
        this.dorisServiceNew = dorisServiceNew;
    }

    /**
     * 保存数据源 有ID则为更新操作，无ID则为新建操作
     *
     * @param datasourceReqDto 数据源请求体
     */
    public void saveDatasource(DatasourceReqDto datasourceReqDto) {

        // 初始化数据源实体
        DatasourceEntity datasourceEntity = new DatasourceEntity();

        // 属性复制
        BeanUtils.copyProperties(datasourceReqDto, datasourceEntity);

        // 对象转换为String
        datasourceEntity.setConnectInfo(JSON.toJSONString(datasourceReqDto.getConnectInfo()));

        // 判断必要属性是否为空
        Assert.isNotEmpty(datasourceEntity.getName(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        Assert.isNotEmpty(datasourceEntity.getType(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        Assert.isNotEmpty(
                datasourceEntity.getConnectInfo(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        DatasourceEntity ds = datasourceDao.getDatasourceByName(datasourceEntity.getName());

        // 判断是否存在 ID 有则更新无则添加
        if (datasourceEntity.getId() == null || datasourceEntity.getId().isEmpty()) {

            // 判断名称是否重复
            if (ds != null) {
                throw new XDapBizException(DataSourceExceptionEnum.DATASOURCE_NAME_IS_DUPLICATE);
            }
            // 雪花算法生成ID
            datasourceEntity.setId(snowflakeIdWorker.nextId());

            // 获取用户
            datasourceEntity.setCreator(MpaasSession.getCurrentUser());

            // 添加数据源
            datasourceDao.addDatasource(datasourceEntity);

            if (Objects.nonNull(datasourceReqDto.getProjectId())) {

                // 自动给项目授权
                ResDatasourceAuthority datasourceAuthority = new ResDatasourceAuthority();
                datasourceAuthority.setId(snowflakeIdWorker.nextId());
                datasourceAuthority.setDatasourceId(datasourceEntity.getId());
                datasourceAuthority.setProjectId(datasourceReqDto.getProjectId());
                List<ResDatasourceAuthority> datasourceAuthorities = new ArrayList<>();
                datasourceAuthorities.add(datasourceAuthority);
                datasourceDao.addDatasourceAuth(datasourceAuthorities);

                // 自动添加到项目中
                ProEnvDatasource proEnvDatasource = new ProEnvDatasource();
                proEnvDatasource.setId(snowflakeIdWorker.nextId());
                proEnvDatasource.setEnvId(datasourceReqDto.getEnvId());
                proEnvDatasource.setDatasourceId(datasourceEntity.getId());
                projectDao.addDatasource(proEnvDatasource);
            }
        } else {
            // 判断名称是否重复
            if (ds != null && !ds.getId().equals(datasourceEntity.getId())) {
                throw new XDapBizException(DataSourceExceptionEnum.DATASOURCE_NAME_IS_DUPLICATE);
            }

            // 当新增数据源为FTP时，datasourceEntity的id可能因为上传文件而被设置了
            switch (datasourceEntity.getType()) {
                case "FTP":
                    if (datasourceDao.getDatasourceById(datasourceEntity.getId()) != null) {
                        datasourceDao.updateDatasource(datasourceEntity);
                    } else {
                        datasourceDao.addDatasource(datasourceEntity);
                    }
                    break;
                default:
                    // 更新
                    datasourceDao.updateDatasource(datasourceEntity);
                    break;
            }
        }
    }

    /**
     * 删除数据源
     *
     * @param datasourceReqDto 数据源请求体
     */
    public void deleteDatasource(DatasourceReqDto datasourceReqDto) {

        // 判断ID是否为空
        Assert.isNotEmpty(datasourceReqDto.getId(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        DatasourceEntity result = datasourceDao.getDatasourceById(datasourceReqDto.getId());

        // 判断删除的对象是否存在以及名称是否相等,数据源是否处于启用状态,是否被同步作业引用
        if (result == null) {

            throw new XDapBizException(DataSourceExceptionEnum.DATA_SOURCE_ID_NOT_EXIST);

        } else if (result.getState().equals(DehoopConstant.ENABLE)) {

            throw new XDapBizException(DataSourceExceptionEnum.DATA_SOURCE_IS_ENABLED);
        } else if (projectDao.getEnvDataSourceByDataSourceId(result.getId()) != null) {

            throw new XDapBizException(DataSourceExceptionEnum.DATA_SOURCE_IS_REFERENCED);
        } else if (result.getName().equals(datasourceReqDto.getName())) {
            // 删除操作
            datasourceDao.deleteDatasource(datasourceReqDto.getId());

            // 删除绑定在环境配置的数据源
            projectDao.deleteDatasourceByDatasourceId(datasourceReqDto.getId());

        } else {

            throw new XDapBizException(DataSourceExceptionEnum.DATA_SOURCE_NAME_NOT_MATCH);
        }
    }

    /**
     * 分页查询数据源
     *
     * @param datasourceReqDto 数据源请求体
     * @return PageRespHelper
     */
    public PageRespHelper<QueryDatasourceDto> pageQueryDatasource(DatasourceReqDto datasourceReqDto) {

        // 获取结果
        PageQueryResult<DatasourceEntity> result =
                datasourceDao.pageQueryDatasource(
                        datasourceReqDto.getPage(),
                        datasourceReqDto.getPageSize(),
                        datasourceReqDto.getSearchWord(),
                        datasourceReqDto.getState(),
                        datasourceReqDto.getType(),
                        datasourceReqDto.getCreator());

        // 初始化对象
        List<QueryDatasourceDto> queryDatasourceDtoList = new ArrayList<>();

        // 初始化数据源与环境绑定的对象
        List<String> hasReferenceIdList = new ArrayList<>();

        if (!result.getResult().isEmpty()) {
            List<String> idList =
                    result.getResult().stream().map(DatasourceEntity::getId).collect(Collectors.toList());

            List<ProEnvDatasource> envDatasourceList =
                    projectDao.queryEnvDataSourceByDataSourceIdList(idList);

            hasReferenceIdList =
                    envDatasourceList.stream()
                            .map(ProEnvDatasource::getDatasourceId)
                            .collect(Collectors.toList());
        }

        List<String> finalHasReferenceIdList = hasReferenceIdList;
        result
                .getResult()
                .forEach(
                        e -> {

                            // 初始化返回对象
                            QueryDatasourceDto queryDatasourceDtoMeta = new QueryDatasourceDto();

                            // 初始化用户对象
                            FndUsers users = userDao.getUserInfo(e.getCreatedBy());

                            // 判断用户是否存在
                            if (users != null) {

                                // 设置用户名
                                e.setCreator(users.getUsername());
                                // 判断头像是否存在
                                if (users.getAvatar() != null && !users.getAvatar().isEmpty()) {
                                    // 设置头像地址
                                    queryDatasourceDtoMeta.setAvatarUrl(
                                            attachmentProperties.getDisplayPrefixUrl() + users.getAvatar());
                                }
                            }

                            // 设置连接信息
                            queryDatasourceDtoMeta.setConnectionInfo(JSONArray.parseObject(e.getConnectInfo()));

                            // 将原对象连接信息置空（减少网络传输流量）
                            e.setConnectInfo("");

                            queryDatasourceDtoMeta.setDatasourceEntity(e);

                            // 设置该数据源是否绑定了其他的环境
                            queryDatasourceDtoMeta.setHasReference(finalHasReferenceIdList.contains(e.getId()));

                            queryDatasourceDtoMeta.setCreationWay(
                                    i18nService.getMessage("CREATION_WAY_" + e.getCreationWay()));

                            queryDatasourceDtoList.add(queryDatasourceDtoMeta);
                        });

        PageRespHelper<QueryDatasourceDto> queryResult = new PageRespHelper<>();
        queryResult.setTable(queryDatasourceDtoList);
        queryResult.setTotal(Integer.parseInt(String.valueOf(result.getCount())));

        return queryResult;
    }

    /**
     * 分页查询数据源
     *
     * @param datasourceReqDto 数据源请求体
     * @return PageRespHelper
     */
    public PageRespHelper<QueryDatasourceDto> pageQueryInnerDatasource(
            DatasourceReqDto datasourceReqDto) {
        PageQueryResult<DatasourceEntity> result = new PageQueryResult<>();

        // mysql类型过滤apaas数据源
        if (DataBaseType.MySQL.name().equals(datasourceReqDto.getType())) {
            // 获取结果
            result =
                    datasourceDao.pageQueryInnerDatasource(
                            datasourceReqDto.getPage(),
                            datasourceReqDto.getPageSize(),
                            datasourceReqDto.getSearchWord(),
                            datasourceReqDto.getState(),
                            datasourceReqDto.getType());
        } else {
            result =
                    datasourceDao.pageQueryDatasource(
                            datasourceReqDto.getPage(),
                            datasourceReqDto.getPageSize(),
                            datasourceReqDto.getSearchWord(),
                            datasourceReqDto.getState(),
                            datasourceReqDto.getType(),
                            datasourceReqDto.getCreator());
        }

        // 初始化对象
        List<QueryDatasourceDto> queryDatasourceDtoList = new ArrayList<>();

        // 初始化数据源与环境绑定的对象
        List<String> hasReferenceIdList = new ArrayList<>();

        if (!result.getResult().isEmpty()) {
            List<String> idList =
                    result.getResult().stream().map(DatasourceEntity::getId).collect(Collectors.toList());

            List<ProEnvDatasource> envDatasourceList =
                    projectDao.queryEnvDataSourceByDataSourceIdList(idList);

            hasReferenceIdList =
                    envDatasourceList.stream()
                            .map(ProEnvDatasource::getDatasourceId)
                            .collect(Collectors.toList());
        }

        List<String> finalHasReferenceIdList = hasReferenceIdList;
        result
                .getResult()
                .forEach(
                        e -> {

                            // 初始化返回对象
                            QueryDatasourceDto queryDatasourceDtoMeta = new QueryDatasourceDto();

                            // 初始化用户对象
                            FndUsers users = userDao.getUserInfo(e.getCreatedBy());

                            // 判断用户是否存在
                            if (users != null) {

                                // 设置用户名
                                e.setCreator(users.getUsername());
                                // 判断头像是否存在
                                if (users.getAvatar() != null && !users.getAvatar().isEmpty()) {
                                    // 设置头像地址
                                    queryDatasourceDtoMeta.setAvatarUrl(
                                            attachmentProperties.getDisplayPrefixUrl() + users.getAvatar());
                                }
                            }

                            // 设置连接信息
                            queryDatasourceDtoMeta.setConnectionInfo(JSONArray.parseObject(e.getConnectInfo()));

                            // 将原对象连接信息置空（减少网络传输流量）
                            e.setConnectInfo("");

                            queryDatasourceDtoMeta.setDatasourceEntity(e);

                            // 设置该数据源是否绑定了其他的环境
                            queryDatasourceDtoMeta.setHasReference(finalHasReferenceIdList.contains(e.getId()));

                            queryDatasourceDtoList.add(queryDatasourceDtoMeta);
                        });

        PageRespHelper<QueryDatasourceDto> queryResult = new PageRespHelper<>();
        queryResult.setTable(queryDatasourceDtoList);
        queryResult.setTotal(Integer.parseInt(String.valueOf(result.getCount())));

        return queryResult;
    }

    /**
     * 分页查询数据源
     *
     * @param datasourceReqDto 数据源请求体
     * @return PageRespHelper
     */
    public PageRespHelper<QueryDatasourceDto> pageQueryDefaultDatasource(
            DatasourceReqDto datasourceReqDto) {

        // mysql类型过滤apaas数据源
        PageQueryResult<DatasourceEntity> result =
                datasourceDao.pageQueryDefaultDatasource(
                        datasourceReqDto.getPage(),
                        datasourceReqDto.getPageSize(),
                        datasourceReqDto.getSearchWord(),
                        datasourceReqDto.getState(),
                        datasourceReqDto.getType());

        // 初始化对象
        List<QueryDatasourceDto> queryDatasourceDtoList = new ArrayList<>();

        // 初始化数据源与环境绑定的对象
        List<String> hasReferenceIdList = new ArrayList<>();

        if (!result.getResult().isEmpty()) {
            List<String> idList =
                    result.getResult().stream().map(DatasourceEntity::getId).collect(Collectors.toList());

            List<ProEnvDatasource> envDatasourceList =
                    projectDao.queryEnvDataSourceByDataSourceIdList(idList);

            hasReferenceIdList =
                    envDatasourceList.stream()
                            .map(ProEnvDatasource::getDatasourceId)
                            .collect(Collectors.toList());
        }

        List<String> finalHasReferenceIdList = hasReferenceIdList;
        result
                .getResult()
                .forEach(
                        e -> {

                            // 初始化返回对象
                            QueryDatasourceDto queryDatasourceDtoMeta = new QueryDatasourceDto();

                            // 初始化用户对象
                            FndUsers users = userDao.getUserInfo(e.getCreatedBy());

                            // 判断用户是否存在
                            if (users != null) {

                                // 设置用户名
                                e.setCreator(users.getUsername());
                                // 判断头像是否存在
                                if (users.getAvatar() != null && !users.getAvatar().isEmpty()) {
                                    // 设置头像地址
                                    queryDatasourceDtoMeta.setAvatarUrl(
                                            attachmentProperties.getDisplayPrefixUrl() + users.getAvatar());
                                }
                            }

                            // 设置连接信息
                            queryDatasourceDtoMeta.setConnectionInfo(JSONArray.parseObject(e.getConnectInfo()));

                            // 将原对象连接信息置空（减少网络传输流量）
                            e.setConnectInfo("");

                            queryDatasourceDtoMeta.setDatasourceEntity(e);

                            // 设置该数据源是否绑定了其他的环境
                            queryDatasourceDtoMeta.setHasReference(finalHasReferenceIdList.contains(e.getId()));

                            queryDatasourceDtoList.add(queryDatasourceDtoMeta);
                        });

        PageRespHelper<QueryDatasourceDto> queryResult = new PageRespHelper<>();
        queryResult.setTable(queryDatasourceDtoList);
        queryResult.setTotal(Integer.parseInt(String.valueOf(result.getCount())));

        return queryResult;
    }

    /**
     * 数据源分页查询外部hive数据源
     *
     * @param datasourceReqDto 数据源请求体
     * @return PageRespHelper
     */
    public PageRespHelper<QueryDatasourceDto> pageQueryOuterDatasource(
            DatasourceReqDto datasourceReqDto) {

        // mysql类型过滤apaas数据源
        PageQueryResult<DatasourceEntity> result =
                datasourceDao.pageQueryOuterDatasource(
                        datasourceReqDto.getPage(),
                        datasourceReqDto.getPageSize(),
                        datasourceReqDto.getSearchWord(),
                        datasourceReqDto.getState(),
                        datasourceReqDto.getType());

        // 初始化对象
        List<QueryDatasourceDto> queryDatasourceDtoList = new ArrayList<>();

        result
                .getResult()
                .forEach(
                        e -> {
                            QueryDatasourceDto queryDatasourceDtoMeta = new QueryDatasourceDto();
                            queryDatasourceDtoMeta.setDatasourceEntity(e);
                            queryDatasourceDtoList.add(queryDatasourceDtoMeta);
                        });

        PageRespHelper<QueryDatasourceDto> queryResult = new PageRespHelper<>();
        queryResult.setTable(queryDatasourceDtoList);
        queryResult.setTotal(Integer.parseInt(String.valueOf(result.getCount())));

        return queryResult;
    }

    /**
     * 更新状态接口
     *
     * @param datasourceReqDto 数据源请求体
     */
    public void updateDatasourceState(DatasourceReqDto datasourceReqDto) {

        Assert.isNotEmpty(datasourceReqDto.getId(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        Assert.isNotEmpty(datasourceReqDto.getState(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        DatasourceEntity datasource = datasourceDao.getDatasourceById(datasourceReqDto.getId());

        if (datasource == null) {

            throw new XDapBizException(DataSourceExceptionEnum.DATA_SOURCE_IS_NOT_EXIST);
        }

        datasourceDao.updateDatasourceState(datasourceReqDto.getId(), datasourceReqDto.getState());
    }

    /**
     * 单个查询数据源
     *
     * @param id 数据源 ID
     * @return QueryDatasourceDto
     */
    public QueryDatasourceDto queryDatasource(String id) {

        DatasourceEntity datasourceEntity = datasourceDao.getDatasourceById(id);

        if (datasourceEntity == null) {
            throw new XDapBizException(DataSourceExceptionEnum.DATA_SOURCE_ID_NOT_EXIST);
        }

        return this.datasourceToDto(datasourceEntity);
    }

    /**
     * 数据源对转换
     *
     * @param datasourceEntity 数据源实体
     * @return 转换后对象
     */
    public QueryDatasourceDto datasourceToDto(DatasourceEntity datasourceEntity) {

        QueryDatasourceDto queryDatasourceDto = new QueryDatasourceDto();

        queryDatasourceDto.setConnectionInfo(JSONArray.parseObject(datasourceEntity.getConnectInfo()));
        queryDatasourceDto.setDatasourceEntity(datasourceEntity);

        return queryDatasourceDto;
    }

    /**
     * 查询数据源类型 以及 个数
     *
     * @return 数据源类型
     */
    public List<QueryType> queryDatasourceType() {

        return datasourceDao.queryDatasourceType();
    }

    /**
     * 查询sqoop数据源类型 以及 个数
     *
     * @return 数据源类型
     */
    public List<QueryType> querySqoopDatasourceType() {

        return datasourceDao.querySqoopDatasourceType();
    }

    /**
     * 获取数据源的连接信息
     *
     * @param dataSourceId 数据源ID
     * @return 查询结果
     */
    public ConnectInfo getConnectionInfo(String dataSourceId) {

        DatasourceEntity datasource = datasourceDao.getDatasourceById(dataSourceId);

        return JSON.parseObject(datasource.getConnectInfo(), ConnectInfo.class);
    }

    /**
     * 添加数据源授权
     *
     * @param datasourceReqDto 请求体
     */
    public void addDatasourceAuthority(DatasourceReqDto datasourceReqDto) {

        Assert.isNotEmpty(
                datasourceReqDto.getDatasourceId(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);
        Assert.isNotNull(datasourceReqDto.getProjectIds(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        // 删除原来添加的数据源
        datasourceDao.deleteSourceAuthBySourceId(datasourceReqDto.getDatasourceId());

        List<ResDatasourceAuthority> datasourceAuthorities = new ArrayList<>();
        datasourceReqDto
                .getProjectIds()
                .forEach(
                        e -> {
                            ResDatasourceAuthority meta = new ResDatasourceAuthority();

                            meta.setId(snowflakeIdWorker.nextId());
                            meta.setDatasourceId(datasourceReqDto.getDatasourceId());
                            meta.setProjectId(e);
                            datasourceAuthorities.add(meta);
                        });

        // 添加授权对象
        datasourceDao.addDatasourceAuth(datasourceAuthorities);
    }

    /**
     * 分页查询数据源授权
     *
     * @param datasourceReqDto 请求体
     * @return 查询结果
     */
    public PageRespHelper<DatasourceAuthInfo> pageQuerySourceAuth(DatasourceReqDto datasourceReqDto) {

        Assert.isNotEmpty(
                datasourceReqDto.getDatasourceId(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        PageRespHelper<DatasourceAuthInfo> result = new PageRespHelper<>();

        List<DatasourceAuthInfo> resultList = new ArrayList<>();

        PageQueryResult<DatasourceAuthDto> queryResult =
                datasourceDao.pageQuerySourceAuth(
                        datasourceReqDto.getDatasourceId(),
                        datasourceReqDto.getPage(),
                        datasourceReqDto.getPageSize(),
                        datasourceReqDto.getSearchWord());

        List<String> userIdList =
                queryResult.getResult().stream()
                        .map(DatasourceAuthDto::getCreatedBy)
                        .collect(Collectors.toList());

        List<FndUsers> userList = userDao.queryUserByIdList(userIdList);

        queryResult
                .getResult()
                .forEach(
                        e -> {
                            DatasourceAuthInfo meta = new DatasourceAuthInfo();

                            BeanUtils.copyProperties(e, meta);

                            // 用户名
                            for (FndUsers user : userList) {

                                if (e.getCreatedBy().equals(user.getId())) {

                                    meta.setCreator(user.getUsername());
                                    break;
                                }
                            }

                            resultList.add(meta);
                        });

        result.setTable(resultList);
        result.setTotal(Integer.parseInt(String.valueOf(queryResult.getCount())));
        return result;
    }

    /**
     * 分页查询正在使用的数据源授权
     *
     * @param datasourceReqDto 请求体
     * @return 查询结果
     */
    public PageRespHelper<DatasourceAuthInfo> pageQueryUsedSourceAuth(
            DatasourceReqDto datasourceReqDto) {

        Assert.isNotEmpty(
                datasourceReqDto.getDatasourceId(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        PageRespHelper<DatasourceAuthInfo> result = new PageRespHelper<>();

        List<DatasourceAuthInfo> resultList = new ArrayList<>();

        PageQueryResult<DatasourceAuthDto> queryResult =
                datasourceDao.pageQueryUsedSourceAuth(
                        datasourceReqDto.getDatasourceId(),
                        datasourceReqDto.getPage(),
                        datasourceReqDto.getPageSize(),
                        datasourceReqDto.getSearchWord());

        List<String> userIdList =
                queryResult.getResult().stream()
                        .map(DatasourceAuthDto::getCreatedBy)
                        .collect(Collectors.toList());

        List<FndUsers> userList = userDao.queryUserByIdList(userIdList);

        queryResult
                .getResult()
                .forEach(
                        e -> {
                            DatasourceAuthInfo meta = new DatasourceAuthInfo();

                            BeanUtils.copyProperties(e, meta);

                            // 用户名
                            for (FndUsers user : userList) {

                                if (e.getCreatedBy().equals(user.getId())) {

                                    meta.setCreator(user.getUsername());
                                    break;
                                }
                            }

                            resultList.add(meta);
                        });

        result.setTable(resultList);
        result.setTotal(Integer.parseInt(String.valueOf(queryResult.getCount())));
        return result;
    }

    /**
     * 分页查询项目下授权的数据源
     *
     * @param datasourceReqDto 数据源请求体
     * @return PageRespHelper
     */
    public PageRespHelper<QueryDatasourceDto> pageQueryAuthDatasource(
            DatasourceReqDto datasourceReqDto) {

        Assert.isNotEmpty(datasourceReqDto.getProjectId(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        // 获取结果
        PageQueryResult<DatasourceEntity> result =
                datasourceDao.pageQueryAuthDatasource(
                        datasourceReqDto.getPage(),
                        datasourceReqDto.getPageSize(),
                        datasourceReqDto.getSearchWord(),
                        datasourceReqDto.getState(),
                        datasourceReqDto.getType(),
                        datasourceReqDto.getProjectId(),
                        datasourceReqDto.getCreationWay());

        // 初始化对象
        List<QueryDatasourceDto> queryDatasourceDtoList = new ArrayList<>();

        // 初始化数据源与环境绑定的对象
        List<String> hasReferenceIdList = new ArrayList<>();

        if (!result.getResult().isEmpty()) {
            List<String> idList =
                    result.getResult().stream().map(DatasourceEntity::getId).collect(Collectors.toList());

            List<ProEnvDatasource> envDatasourceList =
                    projectDao.queryEnvDataSourceByDataSourceIdList(idList);

            hasReferenceIdList =
                    envDatasourceList.stream()
                            .map(ProEnvDatasource::getDatasourceId)
                            .collect(Collectors.toList());
        }

        List<String> finalHasReferenceIdList = hasReferenceIdList;
        result
                .getResult()
                .forEach(
                        e -> {

                            // 初始化返回对象
                            QueryDatasourceDto queryDatasourceDtoMeta = new QueryDatasourceDto();
                            // 初始化用户对象
                            FndUsers users = userDao.getUserInfo(e.getCreatedBy());

                            // 判断用户是否存在
                            if (users != null) {

                                // 设置用户名
                                e.setCreator(users.getUsername());
                                // 判断头像是否存在
                                if (users.getAvatar() != null && !users.getAvatar().isEmpty()) {
                                    // 设置头像地址
                                    queryDatasourceDtoMeta.setAvatarUrl(
                                            attachmentProperties.getDisplayPrefixUrl() + users.getAvatar());
                                }
                            }

                            // 设置连接信息
                            queryDatasourceDtoMeta.setConnectionInfo(JSONArray.parseObject(e.getConnectInfo()));

                            // 将原对象连接信息置空（减少网络传输流量）
                            e.setConnectInfo("");

                            queryDatasourceDtoMeta.setDatasourceEntity(e);

                            // 设置该数据源是否绑定了其他的环境
                            queryDatasourceDtoMeta.setHasReference(finalHasReferenceIdList.contains(e.getId()));

                            queryDatasourceDtoList.add(queryDatasourceDtoMeta);
                        });

        PageRespHelper<QueryDatasourceDto> queryResult = new PageRespHelper<>();
        queryResult.setTable(queryDatasourceDtoList);
        queryResult.setTotal(Integer.parseInt(String.valueOf(result.getCount())));

        return queryResult;
    }

    /**
     * 查询授权后的数据源类型 以及个数
     *
     * @return 数据源类型
     */
    public List<QueryType> queryAuthDatasourceType(String projectId) {

        Assert.isNotEmpty(projectId, CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        return datasourceDao.queryAuthDatasourceType(projectId);
    }

    /**
     * 解析json对象
     *
     * @param datasourceReqDto 请求体
     * @return 解析结果
     */
    public List<RequestBodyDto> parseJsonObject(DatasourceReqDto datasourceReqDto) {

        Assert.isNotNull(datasourceReqDto.getJsonObject(), CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        return parseJson(
                JSON.parse(
                        JSON.toJSONString(
                                datasourceReqDto.getJsonObject(), SerializerFeature.WriteMapNullValue),
                        Feature.OrderedField));
    }

    /**
     * 解析字段list
     *
     * @param jsonObject 需要解析的对象
     * @return 解析后的结果
     */
    public List<RequestBodyDto> parseJson(Object jsonObject) {

        JSONObject json = (JSONObject) JSON.toJSON(jsonObject);
        List<RequestBodyDto> result = new ArrayList<>();

        // 获取第一层的元素
        for (String s : json.keySet()) {

            Object temp = json.get(s);

            RequestBodyDto meta = new RequestBodyDto();
            meta.getPathList().add(s);
            meta.setPath(String.join(".", meta.getPathList()));
            recurJsonField(temp, s, meta);

            result.add(meta);
        }

        return result;
    }

    /**
     * 递归解析json结构
     *
     * @param temp json对象
     * @param fieldName 字段的名称
     */
    public void recurJsonField(Object temp, String fieldName, RequestBodyDto result) {

        List<RequestBodyDto> child = new ArrayList<>();

        // 如果值为null则报错
        if (temp == null) {
            throw new XDapBizException(DataSourceExceptionEnum.JSON_VALUE_CAN_NOT_BE_NULL);
        }

        if (temp instanceof JSONObject) {

            JSONObject json = (JSONObject) temp;

            for (String s : json.keySet()) {

                Object instance = json.get(s);

                // 递归获取对象中的对象结构
                RequestBodyDto objectFiled = new RequestBodyDto();
                objectFiled.getPathList().addAll(result.getPathList());
                objectFiled.getPathList().add(s);
                objectFiled.setPath(String.join(".", objectFiled.getPathList()));
                recurJsonField(instance, s, objectFiled);
                child.add(objectFiled);
            }

            result.setChildren(child);
            result.setName(fieldName);
            result.setType(JsonFieldTypeConstant.Object);
        } else if (temp instanceof JSONArray) {

            JSONArray objArray = (JSONArray) temp;

            // 获取数组的第一个元素解析获取内部结构
            if (!objArray.isEmpty()) {

                if (objArray.get(0) instanceof JSONObject) {

                    JSONObject listObject = (JSONObject) objArray.get(0);

                    List<RequestBodyDto> listChild = new ArrayList<>();

                    for (String j : listObject.keySet()) {

                        // 递归获取对象中的对象结构
                        RequestBodyDto listFiled = new RequestBodyDto();
                        listFiled.getPathList().addAll(result.getPathList());
                        listFiled.getPathList().add(j);
                        listFiled.setPath(String.join(".", listFiled.getPathList()));
                        recurJsonField(listObject.get(j), j, listFiled);
                        listChild.add(listFiled);
                    }

                    result.setChildren(listChild);
                } else {

                    result.setValue(temp);
                }
            }

            result.setName(fieldName);
            result.setType(JsonFieldTypeConstant.Array);

        } else {

            result.setName(fieldName);
            setJsonFiledType(result, temp);
            result.setChildren(new ArrayList<>());
            result.setValue(temp);
        }
    }

    /**
     * 匹配json值的类型
     *
     * @param meta 返回结果
     * @param instance 对象值
     */
    public void setJsonFiledType(RequestBodyDto meta, Object instance) {

        if (instance instanceof String) {
            meta.setType(JsonFieldTypeConstant.String);
        } else if (instance instanceof Integer
                || instance instanceof Float
                || instance instanceof Long
                || instance instanceof BigDecimal) {
            meta.setType(JsonFieldTypeConstant.Number);
        } else if (instance instanceof Boolean) {
            meta.setType(JsonFieldTypeConstant.Boolean);
        }
    }

    /**
     * 通过json配置转换为json
     *
     * @param jsonInfoList jsoninfo list
     * @return 转换结果
     */
    public Map<String, Object> jsonInfoToJson(List<RequestBodyDto> jsonInfoList) {

        Assert.isNotNull(jsonInfoList, CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        Map<String, Object> result = new LinkedHashMap<>();

        jsonInfoList.forEach(e -> recursGenJsonNode(e, result));

        return result;
    }

    /**
     * 递归解析json配置为json
     *
     * @param jsonInfo json配置
     * @param result 解析结果
     */
    public void recursGenJsonNode(RequestBodyDto jsonInfo, Map<String, Object> result) {

        if (jsonInfo.getType() == null || jsonInfo.getType().isEmpty()) {
            result.put(jsonInfo.getName(), jsonInfo.getValue());
        }

        switch (jsonInfo.getType()) {
            case JsonFieldTypeConstant.String:
            case JsonFieldTypeConstant.Number:
            case JsonFieldTypeConstant.Boolean:
                result.put(jsonInfo.getName(), jsonInfo.getValue());

                break;
            case JsonFieldTypeConstant.Object:
                Map<String, Object> objectResult = new HashMap<>();

                if (jsonInfo.getChildren() != null && !jsonInfo.getChildren().isEmpty()) {
                    jsonInfo.getChildren().forEach(e -> recursGenJsonNode(e, objectResult));
                }
                result.put(jsonInfo.getName(), objectResult);

                break;
            case JsonFieldTypeConstant.Array:
                Map<String, Object> listResult = new HashMap<>();

                if (jsonInfo.getChildren() != null && !jsonInfo.getChildren().isEmpty()) {
                    jsonInfo.getChildren().forEach(e -> recursGenJsonNode(e, listResult));
                    List<Object> objectList = new ArrayList<>();
                    objectList.add(listResult);

                    result.put(jsonInfo.getName(), objectList);
                } else {
                    result.put(jsonInfo.getName(), jsonInfo.getValue());
                }

                break;
            default:
                result.put(jsonInfo.getName(), jsonInfo.getValue());
                break;
        }
    }

    /** 修复api数据源的数据 */
    public void fixApiReqBody() {

        List<DatasourceEntity> apiDatasourceList =
                datasourceDao.queryDataSourceByType(DataSourceType.API.name());

        apiDatasourceList.forEach(
                e -> {
                    try {
                        ApiConnectInfoDto apiConnectInfoDto =
                                JSON.parseObject(e.getConnectInfo(), ApiConnectInfoDto.class);

                        apiConnectInfoDto
                                .getRequestBody()
                                .forEach(
                                        requestBody -> {
                                            if (requestBody.getType() == null || requestBody.getType().isEmpty()) {

                                                requestBody.setType(JsonFieldTypeConstant.String);
                                            }

                                            if (requestBody.getPath() == null || requestBody.getPath().isEmpty()) {
                                                requestBody.setPath(requestBody.getName());
                                                requestBody.setPathList(
                                                        new ArrayList<>(Collections.singleton(requestBody.getName())));
                                            }
                                        });

                        e.setConnectInfo(JSON.toJSONString(apiConnectInfoDto));

                        datasourceDao.updateDatasourceConnectInfo(e.getId(), e.getConnectInfo());

                        JobSyncWorkConfig syncWorkConfig = syncWorkDao.getWorkSyncConfigByDbId(e.getId());

                        if (syncWorkConfig != null) {

                            JobSyncWorkApiConfigs syncWorkApiConfigs =
                                    syncWorkDao.getApiConfigByWorkIdAndConfigId(
                                            syncWorkConfig.getWorkId(), syncWorkConfig.getApiConfigId());

                            if (syncWorkApiConfigs != null) {

                                syncWorkApiConfigs.setPageField(apiConnectInfoDto.getPageField());
                                syncWorkApiConfigs.setPageSizeField(apiConnectInfoDto.getPageSizeField());
                                syncWorkApiConfigs.setPageFieldLocation(
                                        apiConnectInfoDto.getPageQueryFieldLocation());
                                syncWorkDao.updateApiConfigPageConfig(syncWorkApiConfigs);
                            }
                        }

                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                });
    }

    public void fixDorisFePort() {

        List<DatasourceEntity> datasourceEntities = datasourceDao.queryAllDorisDataSources();

        datasourceEntities.forEach(
                datasource -> {
                    try {
                        ConnectInfo info =
                                JSONObject.parseObject(datasource.getConnectInfo(), ConnectInfo.class);
                        ConnectInfo connectInfo =
                                DataBaseFactory.toConnectInfo(
                                        datasource.getConnectInfo(), DataBaseType.Doris.name());
                        List<Map<String, Object>> result =
                                dorisServiceNew.executeSqlWithResult(connectInfo, SQLConstant.SHOW_FRONTENDS);
                        if (Objects.nonNull(result) && !result.isEmpty()) {
                            Map<String, Object> config = result.get(0);
                            info.setDorisHttpPort(String.valueOf(config.get(SQLConstant.HTTP_PORT)));
                            datasourceDao.updateDatasourceConnectInfo(
                                    datasource.getId(), JSON.toJSONString(info));
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        throw new XDapBizException(FixExceptionEnum.FIX_EXCEPTION);
                    }
                });
    }

    public List<String> queryAllSchema(String dbSourceId) {

        Assert.isNotEmpty(dbSourceId, CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        DatasourceEntity datasourceInfo = datasourceDao.getDatasourceById(dbSourceId);

        ConnectInfo connectInfo =
                DataBaseFactory.toConnectInfo(datasourceInfo.getConnectInfo(), datasourceInfo.getType());

        return DataBaseFactory.getDatabase(connectInfo.getDataBaseType()).queryAllSchema(connectInfo);
    }
}

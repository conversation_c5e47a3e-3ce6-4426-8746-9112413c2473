package com.definesys.dehoop.admin.moudle.outlinework.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlowBuildInParamInfo {

    private String id;

    private String name;

    private String method;

    private String descr;

    private String type;

    private String usages;
}

package com.definesys.dehoop.admin.moudle.schedule.service;

import com.definesys.dehoop.admin.moudle.baseline.dao.BaselineDao;
import com.definesys.dehoop.admin.moudle.baseline.pojo.CtrBaselineWorks;
import com.definesys.dehoop.admin.moudle.baseline.pojo.CtrBaselines;
import com.definesys.dehoop.admin.moudle.datasource.dao.DatasourceDao;
import com.definesys.dehoop.admin.moudle.datasource.pojo.DatasourceEntity;
import com.definesys.dehoop.admin.moudle.datasource.pojo.dto.RequestHeadersDto;
import com.definesys.dehoop.admin.moudle.datasource.pojo.dto.RequestParamsDto;
import com.definesys.dehoop.admin.moudle.hdfsfile.dao.HdfsFileDao;
import com.definesys.dehoop.admin.moudle.hdfsfile.pojo.ResResourceFiles;
import com.definesys.dehoop.admin.moudle.operation.pojo.dto.OpSyncConfigInfo;
import com.definesys.dehoop.admin.moudle.operation.pojo.dto.WorkDetail;
import com.definesys.dehoop.admin.moudle.outlinework.dao.OutlineWorkDao;
import com.definesys.dehoop.admin.moudle.outlinework.ddlvisual.pojo.dto.JobDdlvFieldsInfoDto;
import com.definesys.dehoop.admin.moudle.outlinework.ddlvisual.pojo.dto.JobDdlvInfoDto;
import com.definesys.dehoop.admin.moudle.outlinework.ddlvisual.service.DdlVisualService;
import com.definesys.dehoop.admin.moudle.outlinework.etl.pojo.dto.JobETLWorkInfoDto;
import com.definesys.dehoop.admin.moudle.outlinework.pojo.*;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.JobSyncWorkApiConfigs;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.JobSyncWorkConfig;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.PartitionValue;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.dto.ColumnMappingDto;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.dto.LinkTable;
import com.definesys.dehoop.admin.moudle.outlinework.sync.pojo.dto.SyncApiConfigInfoDto;
import com.definesys.dehoop.admin.moudle.project.dao.ProjectDao;
import com.definesys.dehoop.admin.moudle.project.pojo.ProjectEntity;
import com.definesys.dehoop.admin.moudle.realwork.pojo.JobRealWorkNodeConfigs;
import com.definesys.dehoop.admin.moudle.realwork.pojo.JobRealWorks;
import com.definesys.dehoop.admin.moudle.realwork.pojo.dto.RealWorkInfo;
import com.definesys.dehoop.admin.moudle.schedule.pojo.SchWorkVersions;
import com.definesys.dehoop.admin.moudle.schedule.pojo.dto.WorkVersionInfo;
import com.definesys.dehoop.admin.moudle.user.pojo.dto.UserSimpleInfo;
import com.definesys.dehoop.admin.moudle.user.service.UserService;
import com.definesys.dehoop.admin.moudle.workspace.dao.WorkSpaceDao;
import com.definesys.dehoop.admin.moudle.workspace.pojo.WorkSpaceEntity;
import com.definesys.dehoop.api.constant.WorkTypeConstants;
import com.definesys.dehoop.api.enums.OutlineWorksType;

import com.xdap.motor.i18n.I18nService;
import com.xdap.motor.utils.StringUtil;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fasterxml.jackson.databind.ObjectMapper;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorkVersionService {

    private final UserService userService;

    private final OutlineWorkDao outlineWorkDao;

    private final WorkSpaceDao workSpaceDao;

    private final DdlVisualService ddlVisualService;

    private final I18nService i18nService;

    private final BaselineDao baselineDao;

    private final ProjectDao projectDao;

    private final DatasourceDao datasourceDao;

    private final HdfsFileDao hdfsFileDao;

    public void handleRealWorkVersionInfo(
            WorkVersionInfo workVersionInfo, SchWorkVersions workVersion) {

        RealWorkInfo realWorkInfo = new RealWorkInfo();
        realWorkInfo.setNodeList(
                JSON.parseArray(workVersion.getRealNodesConfig(), JobRealWorkNodeConfigs.class));
        JobRealWorks jobRealWorks =
                JSON.parseObject(workVersion.getRealWorkConfig(), JobRealWorks.class);
        realWorkInfo.setWebConfig(jobRealWorks.getWebConfig());
        if (StringUtil.isNotEmpty(workVersion.getWorkScripts())) {
            JobWorkScripts jobWorkScripts =
                    JSON.parseObject(workVersion.getWorkScripts(), JobWorkScripts.class);
            realWorkInfo.setWorkScript(jobWorkScripts.getContent());
        }
        workVersionInfo.setRealWorkInfo(realWorkInfo);

        // 补充额外数据 负责人 资源组 描述
        UserSimpleInfo userInfo = userService.getUserSimpleInfo(jobRealWorks.getDirector());
        if (userInfo != null) {
            workVersionInfo.setDirectorName(userInfo.getUsername());
        }
        workVersionInfo.setDescr(jobRealWorks.getDescr());
        WorkSpaceEntity workSpace = workSpaceDao.getWorkSpaceById(jobRealWorks.getWorkspaceId());
        if (workSpace != null) {
            workVersionInfo.setWorkspaceName(workSpace.getName());
        }
        workVersionInfo.setWorkDetail(this.getRealWorkDetail(jobRealWorks));
    }

    public void handleWorkVersionInfo(WorkVersionInfo workVersionInfo, SchWorkVersions workVersion) {

        workVersionInfo.setWorkParams(
                JSON.parseArray(workVersion.getWorkParams(), JobWorkParams.class));
        if (StringUtils.isNotBlank(workVersion.getWorkScripts())) {
            workVersionInfo.setWorkScripts(
                    JSON.parseObject(workVersion.getWorkScripts(), JobWorkScripts.class).getContent());
        }

        JobOutlineWorks jobOutlineWorks =
                JSON.parseObject(workVersion.getWorkInfo(), JobOutlineWorks.class);
        workVersionInfo.setWorkType(jobOutlineWorks.getType());

        // 如果是ddl可视化则计算出脚本再返回
        if (OutlineWorksType.DDLV.name().equals(jobOutlineWorks.getType())) {

            JobDdlvInfoDto ddlvInfo =
                    JSON.parseObject(workVersion.getDdlvConfigInfo(), JobDdlvInfoDto.class);
            List<JobDdlvFieldsInfoDto> fieldsList =
                    JSON.parseArray(workVersion.getDdlvFieldInfo(), JobDdlvFieldsInfoDto.class);

            workVersionInfo.setWorkScripts(ddlVisualService.hiveSqlGenerate(ddlvInfo, fieldsList));
        }

        // 补齐运行参数
        List<JobWorkParams> jobWorkParams =
                JSON.parseArray(workVersion.getWorkParams(), JobWorkParams.class);
        workVersionInfo.setWorkParams(jobWorkParams);

        // 资源列表
        List<JobWorkResources> workResources =
                outlineWorkDao.queryWorkResources(workVersion.getWorkId());
        workResources.forEach(e -> e.setResourceName(getWorkResourceName(e.getResourceId())));
        workVersionInfo.setResourceList(workResources);

        // 补齐调度配置
        JobWorkControls jobWorkControls =
                JSON.parseObject(workVersion.getControlConfig(), JobWorkControls.class);
        if (jobWorkControls != null) {
            jobWorkControls.setDaysList(JSON.parseArray(jobWorkControls.getDays(), Integer.class));
            jobWorkControls.setPeriodTypeMeaning(i18nService.getMessage(jobWorkControls.getPeriodType()));
            jobWorkControls.setLevelMeaning(i18nService.getMessage(jobWorkControls.getLevel()));
            jobWorkControls.setTypeMeaning(i18nService.getMessage(jobWorkControls.getType()));
        }
        workVersionInfo.setWorkControl(jobWorkControls);

        // 上游工作依赖
        JobFlowConfigs flowConfig = outlineWorkDao.getFlowConfig(workVersion.getFlowId());
        if (flowConfig != null) {
            List<String[]> flowRows = (List<String[]>) JSON.parse(flowConfig.getFlowConfig());
            List<String> frontWorks = new ArrayList<>();
            Object[] objects = flowRows.toArray();
            for (Object o : objects) {
                JSONArray metaObject = JSON.parseArray(o.toString());
                if (metaObject.get(1).equals(workVersion.getWorkId())) {
                    frontWorks.add((String) metaObject.get(0));
                }
            }
            List<JobOutlineWorks> works = outlineWorkDao.queryOutlineWorks(frontWorks);
            workVersionInfo.setDependentNames(
                    works.stream().map(JobOutlineWorks::getName).collect(Collectors.toList()));
        }

        // 数据同步配置
        if (workVersion.getSyncWorkConfig() != null) {

            // 数据同步配置
            workVersionInfo.setSyncWorkConfig(this.parseSyncWorkConfig(workVersion));
        }

        if (workVersion.getEtlConfig() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                workVersionInfo.setWorkNodeConfig(
                        objectMapper
                                .readValue(workVersion.getEtlConfig(), JobETLWorkInfoDto.class)
                                .getJobETLWorkNodeConfigDto());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                workVersionInfo.setWorkNodeConfig(
                        JSON.parseObject(workVersion.getEtlConfig(), JobETLWorkInfoDto.class)
                                .getJobETLWorkNodeConfigDto());
            }
        }

        workVersionInfo.setWorkDetail(this.getWorkDetail(workVersion));
    }

    /**
     * 解析版本中的数据同步作业信息
     *
     * @param workVersion 作业版本
     * @return 查询结果
     */
    public OpSyncConfigInfo parseSyncWorkConfig(SchWorkVersions workVersion) {

        JobSyncWorkConfig workSyncConfig =
                JSON.parseObject(workVersion.getSyncWorkConfig(), JobSyncWorkConfig.class);
        JobOutlineWorks workInfo = JSON.parseObject(workVersion.getWorkInfo(), JobOutlineWorks.class);
        JobSyncWorkApiConfigs syncWorkApiConfigs = new JobSyncWorkApiConfigs();
        if (WorkTypeConstants.SYNC_API.equals(workInfo.getType())
                || workVersion.getSyncApiConfig() != null) {
            syncWorkApiConfigs =
                    JSON.parseObject(workVersion.getSyncApiConfig(), JobSyncWorkApiConfigs.class);
        }

        return this.parseSyncWorkConfig(workSyncConfig, workInfo, syncWorkApiConfigs);
    }

    public OpSyncConfigInfo parseSyncWorkConfig(
            JobSyncWorkConfig workSyncConfig,
            JobOutlineWorks workInfo,
            JobSyncWorkApiConfigs syncWorkApiConfigs) {

        OpSyncConfigInfo result = new OpSyncConfigInfo();

        if (!WorkTypeConstants.SYNC.equals(workInfo.getType())
                && !WorkTypeConstants.SYNC_API.equals(workInfo.getType())) {

            return result;
        }

        BeanUtils.copyProperties(workSyncConfig, result);
        result.setMappingList(
                JSON.parseArray(workSyncConfig.getMappingConfig(), ColumnMappingDto.class));
        result.setFlowId(workInfo.getFlowId());

        // 查询数据源信息
        DatasourceEntity fromDbInfo = datasourceDao.getDatasourceById(workSyncConfig.getFromDbId());

        if (fromDbInfo != null) {
            result.setFromDbName(fromDbInfo.getName());
            result.setFromDbType(fromDbInfo.getType());
        }
        DatasourceEntity toDbInfo = datasourceDao.getDatasourceById(workSyncConfig.getToDbId());

        if (toDbInfo != null) {
            result.setToDbName(toDbInfo.getName());
            result.setToDbType(toDbInfo.getType());
        }
        result.setAppendMode(workSyncConfig.getAppendMode());
        result.setSplitByValue(workSyncConfig.getSplitByValue());
        if (workSyncConfig.getPartitionValue() != null) {
            result.setPartitionList(
                    JSON.parseArray(workSyncConfig.getPartitionValue(), PartitionValue.class));
        }

        if (workSyncConfig.getTimestampField() != null) {
            result.setTimestampField(workSyncConfig.getTimestampField());
        }

        // 多表关联关系查询接口
        if (workSyncConfig.getLinkTables() != null) {
            result.setLinkTables(JSON.parseArray(workSyncConfig.getLinkTables(), LinkTable.class));
        }

        // hbase列族
        if (workSyncConfig.getColumnFamily() != null) {
            result.setColumnGroup(workSyncConfig.getColumnFamily());
        }

        if (WorkTypeConstants.SYNC_API.equals(workInfo.getType()) || syncWorkApiConfigs != null) {

            SyncApiConfigInfoDto configResult = new SyncApiConfigInfoDto();
            // 查询配置信息

            // 基本的配置信息
            configResult.setRequestHeader(
                    JSON.parseArray(syncWorkApiConfigs.getRequestHeader(), RequestHeadersDto.class));
            configResult.setRequestParam(
                    JSON.parseArray(syncWorkApiConfigs.getRequestParam(), RequestParamsDto.class));
            configResult.setRequestBody(JSON.parseObject(syncWorkApiConfigs.getRequestBody(), Map.class));
            configResult.setPageLoop(syncWorkApiConfigs.getPageLoop());
            configResult.setStartPage(syncWorkApiConfigs.getStartPage());
            configResult.setEndPage(syncWorkApiConfigs.getEndPage());
            configResult.setPageSize(syncWorkApiConfigs.getPageSize());

            result.setApiConfigInfo(configResult);
        }

        // 返回doris管道配置
        if (workSyncConfig.getBrokerName() != null) {
            result.setBrokerName(workSyncConfig.getBrokerName());
            result.setBrokerUsername(workSyncConfig.getBrokerUsername());
            result.setBrokerPassword(workSyncConfig.getBrokerPassword());
        }

        result.setFilterSql(workSyncConfig.getFilterSql());

        return result;
    }

    /**
     * 获取作业版本的详细信息
     *
     * @param workVersions 作业版本
     * @return 结果
     */
    public WorkDetail getWorkDetail(SchWorkVersions workVersions) {

        WorkDetail result = new WorkDetail();

        // 工作流基础配置信息
        JobOutlineWorks jobOutlineWorks =
                JSON.parseObject(workVersions.getWorkInfo(), JobOutlineWorks.class);

        // 查询负责人
        result.setDirectorName(userService.getUserSimpleInfo(workVersions.getDirector()).getUsername());
        result.setDescr(jobOutlineWorks.getDescr());

        ProjectEntity project = projectDao.getProjectById(jobOutlineWorks.getProjectId());
        result.setProjectId(jobOutlineWorks.getProjectId());

        if (project != null) {
            result.setProjectName(project.getName());
        }

        // 基线信息
        if (workVersions.getBaselineId() != null && !workVersions.getBaselineId().isEmpty()) {
            CtrBaselines baseline = baselineDao.getBaselineById(workVersions.getBaselineId());
            result.setBaselineId(workVersions.getBaselineId());
            if (baseline != null) {
                result.setBaselineName(baseline.getName());
            }
        }

        // 资源组
        if (jobOutlineWorks.getWorkspaceId() != null && !jobOutlineWorks.getWorkspaceId().isEmpty()) {

            WorkSpaceEntity workSpace = workSpaceDao.getWorkSpaceById(jobOutlineWorks.getWorkspaceId());

            if (workSpace != null) {

                result.setWorkSpaceId(jobOutlineWorks.getWorkspaceId());
                result.setWorkSpaceName(workSpace.getName());
            }
        }

        return result;
    }

    public String getWorkResourceName(String resourceId) {

        ResResourceFiles resourceFile = hdfsFileDao.getFileById(resourceId);
        if (resourceFile == null) {
            return "";
        } else {
            return resourceFile.getName();
        }
    }

    /**
     * 获取作业当前的workDetail
     *
     * @param jobOutlineWorks 离线作业信息
     * @return 查询结果
     */
    public WorkDetail getWorkDetail(JobOutlineWorks jobOutlineWorks) {

        WorkDetail result = new WorkDetail();

        UserSimpleInfo director = userService.getUserSimpleInfo(jobOutlineWorks.getDirector());

        if (director != null) {

            // 查询负责人
            result.setDirectorName(director.getUsername());
        }
        result.setDescr(jobOutlineWorks.getDescr());

        ProjectEntity project = projectDao.getProjectById(jobOutlineWorks.getProjectId());
        result.setProjectId(jobOutlineWorks.getProjectId());

        if (project != null) {
            result.setProjectName(project.getName());
        }

        // 获取基线配置
        CtrBaselineWorks baselineWorks = baselineDao.getBaselineWork(jobOutlineWorks.getId());

        if (baselineWorks != null) {

            result.setBaselineId(baselineWorks.getBaselineId());

            CtrBaselines baseline = baselineDao.getBaselineById(baselineWorks.getBaselineId());

            if (baseline != null) {

                result.setBaselineName(baseline.getName());
            }
        }

        // 资源组
        if (jobOutlineWorks.getWorkspaceId() != null && !jobOutlineWorks.getWorkspaceId().isEmpty()) {

            WorkSpaceEntity workSpace = workSpaceDao.getWorkSpaceById(jobOutlineWorks.getWorkspaceId());

            if (workSpace != null) {

                result.setWorkSpaceId(jobOutlineWorks.getWorkspaceId());
                result.setWorkSpaceName(workSpace.getName());
            }
        }

        return result;
    }

    /**
     * 获取实时作业的detail
     *
     * @param jobRealWorks 实时作业对象
     * @return workDetail
     */
    public WorkDetail getRealWorkDetail(JobRealWorks jobRealWorks) {

        WorkDetail result = new WorkDetail();

        // 补充额外数据 负责人 资源组 描述
        UserSimpleInfo userInfo = userService.getUserSimpleInfo(jobRealWorks.getDirector());
        if (userInfo != null) {
            result.setDirectorName(userInfo.getUsername());
        }
        result.setDescr(jobRealWorks.getDescr());

        result.setWorkSpaceId(jobRealWorks.getWorkspaceId());
        WorkSpaceEntity workSpace = workSpaceDao.getWorkSpaceById(jobRealWorks.getWorkspaceId());
        if (workSpace != null) {
            result.setWorkSpaceName(workSpace.getName());
        }

        ProjectEntity project = projectDao.getProjectById(jobRealWorks.getProjectId());
        result.setProjectId(jobRealWorks.getProjectId());

        if (project != null) {
            result.setProjectName(project.getName());
        }

        return result;
    }
}

package com.definesys.dehoop.admin.moudle.tagmanage.pojo.dto;

import lombok.Data;

@Data
public class LabelInfoDto {

    private String id;

    private String name;

    private String enName;

    private String descr;

    private String type;

    private String typeMeaning;

    private String dstTable;

    private String mainTable;

    private String mainTableName;

    private String labelPk;

    private String labelPkName;

    private Object rule;

    private Integer storeLifeCycle;

    private String state;

    private String stateMeaning;

    private String runningState;

    private String runningStateMeaning;

    private Integer DataNum;

    private String lastUpdateDate;

    private String categoryId;

    private String categoryName;

    private String creator;

    private String director;

    /** Hive/Spark */
    private String calculationMode;

    private String businessUnitId;

    /** 数据域 */
    private String dataFieldId;

    private String dataFieldName;

    private String businessProcessId;
    /** 业务过程 */
    private String businessProcessName;

    /** 数据分层 */
    private String datalayerId;

    private String datalayerName;
}

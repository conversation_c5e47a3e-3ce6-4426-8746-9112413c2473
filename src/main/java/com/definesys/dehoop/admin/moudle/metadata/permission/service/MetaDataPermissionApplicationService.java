package com.definesys.dehoop.admin.moudle.metadata.permission.service;

import com.definesys.dehoop.admin.moudle.businessunit.dao.BusinessUnitDao;
import com.definesys.dehoop.admin.moudle.businessunit.pojo.BusinessUnit;
import com.definesys.dehoop.admin.moudle.datasource.dao.DatasourceDao;
import com.definesys.dehoop.admin.moudle.datasource.pojo.DatasourceEntity;
import com.definesys.dehoop.admin.moudle.metadata.map.dto.DehoopDorisTable;
import com.definesys.dehoop.admin.moudle.metadata.map.dto.DehoopHiveTable;
import com.definesys.dehoop.admin.moudle.metadata.map.service.MetaDataMapService;
import com.definesys.dehoop.admin.moudle.metadata.map.service.MetaDataMapTableService;
import com.definesys.dehoop.admin.moudle.metadata.permission.dao.DataAccessPermissionDao;
import com.definesys.dehoop.admin.moudle.metadata.permission.dao.MetaDataMapTableDao;
import com.definesys.dehoop.admin.moudle.metadata.permission.dao.MetaDataPermissionApplicationDao;
import com.definesys.dehoop.admin.moudle.metadata.permission.dao.MetaDataPermissionApplicationDetailDao;
import com.definesys.dehoop.admin.moudle.metadata.permission.dto.DehoopMetaDataDto;
import com.definesys.dehoop.admin.moudle.metadata.permission.dto.entity.MetaDataPermission;
import com.definesys.dehoop.admin.moudle.metadata.permission.dto.entity.MetaDataPermissionApplication;
import com.definesys.dehoop.admin.moudle.metadata.permission.dto.entity.MetaDataPermissionApplicationDetail;
import com.definesys.dehoop.admin.moudle.metadata.permission.dto.pojo.*;
import com.definesys.dehoop.admin.moudle.security.ProjectSecurityService;
import com.definesys.dehoop.admin.moudle.user.dao.UserDao;
import com.definesys.dehoop.admin.moudle.user.pojo.FndUsers;
import com.definesys.dehoop.api.constant.DataBaseTypeConstant;
import com.definesys.dehoop.api.constant.MetaDataPermissionConstant;
import com.definesys.dehoop.api.enums.MetaDataPermissionApplicationStatus;
import com.definesys.dehoop.api.exception.MetaDataExceptionEnum;
import com.definesys.dehoop.api.properties.AttachmentProperties;
import com.definesys.mpaas.query.db.PageQueryResult;
import com.definesys.mpaas.query.session.MpaasSession;

import com.xdap.motor.entity.SnowflakeIdWorker;
import com.xdap.motor.exception.CommonBizExceptionEnum;
import com.xdap.motor.exception.XDapBizException;
import com.xdap.motor.i18n.I18nService;
import com.xdap.motor.utils.Assert;
import com.xdap.motor.utils.StringUtils;
import com.xdap.motor.vo.PageRespHelper;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import org.apache.commons.lang3.ObjectUtils;

import lombok.RequiredArgsConstructor;

import static com.definesys.dehoop.api.constant.MetaDataConstant.PERMISSION_READ;

import javax.servlet.http.HttpServletResponse;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class MetaDataPermissionApplicationService {

    private final SnowflakeIdWorker snowflakeIdWorker;
    private final MetaDataPermissionApplicationDao metaDataPermissionApplicationDao;
    private final MetaDataPermissionApplicationDetailDao metaDataPermissionApplicationDetailDao;
    private final UserDao userDao;
    private final BusinessUnitDao businessUnitDao;
    private final I18nService i18nService;
    private final DatasourceDao datasourceDao;
    private final DataAccessPermissionDao dataAccessPermissionDao;
    private final MetaDataMapService metaDataMapService;
    private final MetaDataMapTableService metaDataMapTableService;
    private final MetaDataMapTableDao metaDataMapTableDao;
    private final AttachmentProperties attachmentProperties;
    private final ProjectSecurityService projectSecurityService;

    /** 提交申请单 */
    public void submitApplication(MetaDataPermissionApplicationDto application) {

        List<String> guids =
                application.getDetailList().stream()
                        .map(MetaDataPermissionApplicationDetailDto::getTableGuid)
                        .collect(Collectors.toList());
        Map<String, DehoopMetaDataDto> alltableMap =
                metaDataMapTableDao.getDehoopTableByGuids(guids).stream()
                        .collect(Collectors.toMap(DehoopMetaDataDto::getGuid, e -> e));

        List<MetaDataPermissionApplicationDetailDto> applicationDetails = application.getDetailList();

        applicationDetails.forEach(
                applicationDetail -> {
                    DehoopMetaDataDto dehoopTable = alltableMap.get(applicationDetail.getTableGuid());
                    if (dehoopTable != null) {
                        BeanUtils.copyProperties(dehoopTable, applicationDetail);
                        applicationDetail.setTableName(dehoopTable.getName());
                        applicationDetail.setEvnType(dehoopTable.getEnvType());
                    }
                });

        List<MetaDataPermissionApplication> metaDataPermissionApplications = new ArrayList<>();
        applicationDetails.stream()
                .collect(
                        Collectors.groupingBy(
                                MetaDataPermissionApplicationDetailDto::getDirector, Collectors.toList()))
                .forEach(
                        (director, applicationDetailList) -> {
                            if (ObjectUtils.isEmpty(applicationDetailList)) {
                                return;
                            }

                            MetaDataPermissionApplicationDetailDto applicationDetailTemp =
                                    applicationDetailList.get(0);

                            final MetaDataPermissionApplication applicationPo =
                                    new MetaDataPermissionApplication();
                            applicationPo.setId(snowflakeIdWorker.nextId());
                            applicationPo.setDirector(director);
                            applicationPo.setApplicationUser(MpaasSession.getCurrentUser());
                            applicationPo.setApplicationCause(application.getApplicationCause());
                            applicationPo.setApplicationTime(new Date());
                            applicationPo.setApprovalStatus(MetaDataPermissionApplicationStatus.UNREVIEWED);
                            applicationPo.setBusinessUnitId(director);
                            applicationPo.setBusinessUnitId(applicationDetailTemp.getBusinessUnitId());
                            applicationPo.setEvnType(applicationDetailTemp.getEvnType());
                            applicationPo.setDbResourceId(applicationDetailTemp.getDbResourceId());
                            List<MetaDataPermissionApplicationDetail> metaDataPermissionApplicationDetails =
                                    new ArrayList<>();

                            List<String> tableNames = new ArrayList<>();
                            applicationDetailList.forEach(
                                    metaDataPermissionApplicationDetailDto -> {
                                        MetaDataPermissionApplicationDetail applicationDetail =
                                                new MetaDataPermissionApplicationDetail();
                                        BeanUtils.copyProperties(
                                                metaDataPermissionApplicationDetailDto, applicationDetail);
                                        applicationDetail.setId(snowflakeIdWorker.nextId());
                                        applicationDetail.setApplicationId(applicationPo.getId());
                                        applicationDetail.setName(
                                                metaDataPermissionApplicationDetailDto.getTableName());
                                        metaDataPermissionApplicationDetails.add(applicationDetail);

                                        tableNames.add(metaDataPermissionApplicationDetailDto.getTableName());
                                    });
                            metaDataPermissionApplicationDetailDao.insertMetaDataPermissionApplicationDetail(
                                    metaDataPermissionApplicationDetails);

                            applicationPo.setTableName(StringUtils.join(tableNames, ","));
                            metaDataPermissionApplications.add(applicationPo);
                        });

        metaDataPermissionApplicationDao.insertBatchMetaDataPermissionApplication(
                metaDataPermissionApplications);
    }

    /**
     * 重新提交 生成一条新的权限申请记录，复制旧申请记录中的信息，申请时间更新。审批状态为"未审核"，在审批记录里重新显示，之前的权限审批记录不存在。
     *
     * @param applicationId 申请单号
     */
    public void resubmitApplication(String applicationId) {

        MetaDataPermissionApplication metaDataPermissionApplication =
                metaDataPermissionApplicationDao.getMetaDataPermissionApplication(applicationId);
        if (metaDataPermissionApplication == null) {
            throw new XDapBizException(MetaDataExceptionEnum.APPLICATION_IS_NULL);
        }
        if (metaDataPermissionApplication.getApprovalStatus()
                        == MetaDataPermissionApplicationStatus.APPROVED
                || metaDataPermissionApplication.getApprovalStatus()
                        == MetaDataPermissionApplicationStatus.DISAPPROVE) {
            throw new XDapBizException(MetaDataExceptionEnum.APPLICATION_STATUS_ERROR);
        }

        // 过滤系统字段
        MetaDataPermissionApplicationDto applicationDto = new MetaDataPermissionApplicationDto();
        BeanUtils.copyProperties(metaDataPermissionApplication, applicationDto);
        MetaDataPermissionApplication resubmitApplication = new MetaDataPermissionApplication();
        BeanUtils.copyProperties(applicationDto, resubmitApplication);
        resubmitApplication.setId(snowflakeIdWorker.nextId());
        resubmitApplication.setApplicationTime(new Date());
        resubmitApplication.setApprovalStatus(MetaDataPermissionApplicationStatus.UNREVIEWED);

        // 更新表明细
        metaDataPermissionApplicationDetailDao
                .queryMetaDataPermissionApplication(applicationId)
                .forEach(
                        metaDataPermissionApplicationDetail -> {
                            metaDataPermissionApplicationDetail.setApplicationId(resubmitApplication.getId());
                            metaDataPermissionApplicationDetailDao.updateMetaDataPermissionApplicationDetail(
                                    metaDataPermissionApplicationDetail);
                        });

        // 一代新人换旧人
        metaDataPermissionApplicationDao.insertMetaDataPermissionApplication(resubmitApplication);
        metaDataPermissionApplicationDao.deleteMetaDataPermissionApplication(applicationId);
    }

    /**
     * 撤销
     *
     * @param applicationId 申请单号
     */
    public void revocationApplication(String applicationId) {

        MetaDataPermissionApplication metaDataPermissionApplication =
                metaDataPermissionApplicationDao.getMetaDataPermissionApplication(applicationId);
        if (metaDataPermissionApplication == null) {
            throw new XDapBizException(MetaDataExceptionEnum.APPLICATION_IS_NULL);
        }
        if (metaDataPermissionApplication.getApprovalStatus()
                        == MetaDataPermissionApplicationStatus.REVOCATION
                || metaDataPermissionApplication.getApprovalStatus()
                        == MetaDataPermissionApplicationStatus.APPROVED) {
            throw new XDapBizException(MetaDataExceptionEnum.APPLICATION_STATUS_ERROR);
        }

        metaDataPermissionApplication.setApprovalStatus(MetaDataPermissionApplicationStatus.REVOCATION);
        metaDataPermissionApplicationDao.updateMetaDataPermissionApplication(
                metaDataPermissionApplication);
    }

    /** 分页查询可申请的表 */
    public PageRespHelper<? extends DehoopMetaDataDto> pageQueryApplicationTable(
            DehoopMetaDataDto dehoopMetaDataDto) {

        if (StringUtils.isBlank(dehoopMetaDataDto.getType())) {
            dehoopMetaDataDto.setType(DataBaseTypeConstant.HIVE);
        }

        List<String> projectIds = projectSecurityService.queryUserProject();

        // 防止条件不生效
        if (CollectionUtils.isEmpty(projectIds)) {
            projectIds.add("null");
        }

        // 查询当前用户的存储空间
        List<String> businessIds =
                businessUnitDao.queryBusinessUnitByUserId(MpaasSession.getCurrentUser()).stream()
                        .map(BusinessUnit::getId)
                        .collect(Collectors.toList());
        dehoopMetaDataDto.setBusinessUnitIds(businessIds);

        PageQueryResult<? extends DehoopMetaDataDto> pageQueryResult =
                metaDataPermissionApplicationDao.pageQueryApplicationTable(dehoopMetaDataDto, projectIds);
        List<? extends DehoopMetaDataDto> dehoopTables = pageQueryResult.getResult();

        switch (dehoopMetaDataDto.getType()) {
            case DataBaseTypeConstant.DORIS:
                List<DehoopDorisTable> dehoopDorisTableDtos =
                        (List<DehoopDorisTable>) metaDataMapTableService.BeanToDto(dehoopTables);
                PageRespHelper<DehoopDorisTable> dehoopDorisTableDtoPageRespHelper = new PageRespHelper<>();
                dehoopDorisTableDtoPageRespHelper.setTable(dehoopDorisTableDtos);
                dehoopDorisTableDtoPageRespHelper.setTotal(pageQueryResult.getCount().intValue());
                return dehoopDorisTableDtoPageRespHelper;
            case DataBaseTypeConstant.HIVE:
            default:
                List<DehoopHiveTable> dehoopHiveTableDtos =
                        (List<DehoopHiveTable>) metaDataMapTableService.BeanToDto(dehoopTables);
                PageRespHelper<DehoopHiveTable> dehoopHiveTableDtoPageRespHelper = new PageRespHelper<>();
                dehoopHiveTableDtoPageRespHelper.setTable(dehoopHiveTableDtos);
                dehoopHiveTableDtoPageRespHelper.setTotal(pageQueryResult.getCount().intValue());
                return dehoopHiveTableDtoPageRespHelper;
        }
    }

    /** 添加权限 将申请表中的表和权限转换为权限记录 需要考虑权限记录已存在 暂时不处理多权限情况 */
    public void addAccessPermission(
            List<MetaDataPermissionApplicationDetail> metaDataPermissionApplicationDetailList,
            String applicationUserId) {

        // 移除已存在的表权限：多权限情况下应对这些数据进行更新操作
        List<String> guids =
                metaDataPermissionApplicationDetailList.stream()
                        .map(MetaDataPermissionApplicationDetail::getTableGuid)
                        .collect(Collectors.toList());
        List<String> existGuids =
                dataAccessPermissionDao.queryMetaDataPermissionByGuidsAndUserId(guids, applicationUserId)
                        .stream()
                        .map(MetaDataPermission::getGuid)
                        .collect(Collectors.toList());

        // 新增
        guids.removeAll(existGuids);

        // 获取当前表的属性
        Map<String, DehoopMetaDataDto> dehoopTablesMap =
                metaDataMapTableDao.getDehoopTableByGuids(guids).stream()
                        .collect(Collectors.toMap(DehoopMetaDataDto::getGuid, e -> e));

        List<MetaDataPermissionApplicationDetail> newApplicationDetails =
                metaDataPermissionApplicationDetailList.stream()
                        .filter(applicationDetail -> guids.contains(applicationDetail.getTableGuid()))
                        .collect(Collectors.toList());

        List<MetaDataPermission> metaDataPermissions =
                newApplicationDetails.stream()
                        .filter(
                                detail -> {
                                    DehoopMetaDataDto dehoopTable = dehoopTablesMap.get(detail.getTableGuid());
                                    return !dehoopTable.getDirector().equals(applicationUserId);
                                })
                        .map(
                                applicationDetail -> {
                                    DehoopMetaDataDto dehoopTable =
                                            dehoopTablesMap.get(applicationDetail.getTableGuid());
                                    MetaDataPermission permission = new MetaDataPermission();
                                    permission.setReadPermission(applicationDetail.getReadPermission());
                                    permission.setUserId(applicationUserId);
                                    permission.setGuid(applicationDetail.getTableGuid());
                                    permission.setId(snowflakeIdWorker.nextId());
                                    permission.setBusinessUnitId(dehoopTable.getBusinessUnitId());
                                    permission.setDataSourceId(dehoopTable.getDbResourceId());
                                    permission.setEnvType(dehoopTable.getEnvType());
                                    permission.setSourceType(dehoopTable.getSourceType());
                                    permission.setDirector(dehoopTable.getDirector());
                                    permission.setTableName(applicationDetail.getName());
                                    return permission;
                                })
                        .collect(Collectors.toList());

        if (metaDataPermissions.size() > 0) {
            dataAccessPermissionDao.insertDataAccessPermission(metaDataPermissions);
        }

        // 更新
        List<MetaDataPermissionApplicationDetail> updateApplicationDetails =
                metaDataPermissionApplicationDetailList.stream()
                        .filter(applicationDetail -> existGuids.contains(applicationDetail.getTableGuid()))
                        .collect(Collectors.toList());

        updateApplicationDetails.forEach(
                applicationDetail -> {
                    MetaDataPermission metaDataPermission = new MetaDataPermission();
                    metaDataPermission.setReadPermission(applicationDetail.getReadPermission());
                    metaDataPermission.setUserId(applicationUserId);
                    metaDataPermission.setGuid(applicationDetail.getTableGuid());
                    dataAccessPermissionDao.updateDataAccessPermission(metaDataPermission);
                });
    }

    /** 查询当前所有申请状态列表 */
    public List<Map<String, String>> metaDataPermissionApplicationStatusList() {
        return Arrays.stream(MetaDataPermissionApplicationStatus.values())
                .map(
                        e -> {
                            Map<String, String> map = new HashMap<>();
                            map.put("code", e.name());
                            map.put("name", i18nService.getMessage(e.name()));
                            return map;
                        })
                .collect(Collectors.toList());
    }

    /** 权限回收 */
    public void recycleAccessPermission(MetaDataPermission metaDataPermissionParam) {

        dataAccessPermissionDao.updateDataAccessPermission(metaDataPermissionParam);

        MetaDataPermission metaDataPermissions =
                dataAccessPermissionDao.getDataAccessPermission(
                        metaDataPermissionParam.getGuid(), metaDataPermissionParam.getUserId());
        if (!metaDataPermissions.getReadPermission()) {
            dataAccessPermissionDao.deleteMetadataPermissionTable(
                    metaDataPermissionParam.getGuid(), metaDataPermissionParam.getUserId());
        }
    }

    /** @param permissionType 权限类型 权限校验 判断用户是否有表权限的： 权限表中是否被授权、用户是否是表的负责人，用户是否是对应存储空间负责人 */
    public boolean accessPermission(String guid, String permissionType) {

        Assert.isNotBlank(guid, CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        DehoopMetaDataDto dehoopTable = metaDataMapTableDao.getDehoopTableByGuid(guid);

        // 用户是表负责人
        if (dehoopTable.getDirector() != null
                && MpaasSession.getCurrentUser().equals(dehoopTable.getDirector())) {
            return true;
        }

        // 判断用户是否是存储空间负责人
        if (dehoopTable.getDirector() != null && dehoopTable.getBusinessUnitId() != null) {
            BusinessUnit businessUnit =
                    businessUnitDao.getBusinessUintById(dehoopTable.getBusinessUnitId());
            if (businessUnit != null && MpaasSession.getCurrentUser().equals(businessUnit.getAdminId())) {
                return true;
            }
        }

        MetaDataPermission metaDataPermissions =
                dataAccessPermissionDao.queryMetaDataPermissionByGuidsAndUserId(
                        guid, MpaasSession.getCurrentUser());

        if (permissionType.equals(MetaDataPermissionConstant.PERMISSION_READ)
                && metaDataPermissions != null) {
            return metaDataPermissions.getReadPermission();
        } else {
            return false;
        }
    }

    /**
     * 返回用户当前权限
     *
     * @param guid 元数据表guid
     * @return 元数据表权限
     */
    public Map<String, Object> accessPermission(String guid) {
        Map<String, Object> permission = new HashMap<>();
        permission.put(
                MetaDataPermissionConstant.PERMISSION_READ,
                accessPermission(guid, MetaDataPermissionConstant.PERMISSION_READ));
        return permission;
    }

    /**
     * 批量处理申请单
     *
     * @param approvalDto 申请单号
     */
    public void approvalApplication(
            @RequestBody MetaDataPermissionApplicationApprovalDto approvalDto) {

        for (String applicationId : approvalDto.getApplicationIds()) {
            approvalApplication(
                    applicationId, approvalDto.getApprovalStatus(), approvalDto.getApprovalOpinion());
        }
    }

    /**
     * 审批申请单
     *
     * @param applicationId 申请ID
     */
    public void approvalApplication(
            String applicationId, MetaDataPermissionApplicationStatus status, String approvalOpinion) {

        // 处理申请单
        MetaDataPermissionApplication application =
                metaDataPermissionApplicationDao.getMetaDataPermissionApplication(applicationId);
        if (application == null) {
            throw new XDapBizException(MetaDataExceptionEnum.APPLICATION_IS_NULL);
        }
        if (application.getApprovalStatus() != MetaDataPermissionApplicationStatus.UNREVIEWED) {
            throw new XDapBizException(MetaDataExceptionEnum.APPLICATION_STATUS_ERROR);
        }

        application.setApprovalTime(new Date());
        application.setApprovalUser(MpaasSession.getCurrentUser());
        application.setApprovalOpinion(approvalOpinion);
        application.setApprovalStatus(status);
        metaDataPermissionApplicationDao.updateMetaDataPermissionApplication(application);

        if (MetaDataPermissionApplicationStatus.APPROVED == status) {
            // 添加权限关系
            List<MetaDataPermissionApplicationDetail> metaDataPermissionApplicationDetailList =
                    metaDataPermissionApplicationDetailDao.queryMetaDataPermissionApplication(
                            application.getId());
            addAccessPermission(
                    metaDataPermissionApplicationDetailList, application.getApplicationUser());
        }
    }

    /** 查询申请详情 */
    public MetaDataPermissionApplicationDto getMetaDataPermissionApplication(String applicationId) {

        // 查询申请单
        MetaDataPermissionApplication application =
                metaDataPermissionApplicationDao.getMetaDataPermissionApplication(applicationId);
        MetaDataPermissionApplicationDto metaDataPermissionApplicationDto =
                new MetaDataPermissionApplicationDto();
        BeanUtils.copyProperties(application, metaDataPermissionApplicationDto);

        List<MetaDataPermissionApplicationDetailDto> applicationDetailDtoList =
                metaDataPermissionApplicationDetailDao.queryMetaDataPermissionApplication(applicationId)
                        .stream()
                        .map(
                                applicationDetail -> {
                                    MetaDataPermissionApplicationDetailDto applicationDetailDto =
                                            new MetaDataPermissionApplicationDetailDto();
                                    BeanUtils.copyProperties(applicationDetail, applicationDetailDto);
                                    if (applicationDetail.getReadPermission()) {
                                        applicationDetailDto.setPermissionMeaning(PERMISSION_READ);
                                    }
                                    return applicationDetailDto;
                                })
                        .collect(Collectors.toList());

        List<String> guids =
                applicationDetailDtoList.stream()
                        .map(MetaDataPermissionApplicationDetailDto::getTableGuid)
                        .collect(Collectors.toList());
        Map<String, DehoopMetaDataDto> dehoopHiveTableDtoMap =
                metaDataMapTableDao.getDehoopTableByGuids(guids).stream()
                        .collect(Collectors.toMap(DehoopMetaDataDto::getGuid, e -> e));
        applicationDetailDtoList.forEach(
                e -> {
                    DehoopMetaDataDto dehoopTable = dehoopHiveTableDtoMap.get(e.getTableGuid());
                    if (dehoopTable != null) {
                        e.setTableName(dehoopTable.getName());
                        e.setDescr(dehoopTable.getDescr());
                    }
                });

        metaDataPermissionApplicationDto.setDetailList(applicationDetailDtoList);

        BusinessUnit businessUnit =
                businessUnitDao.getBusinessUintById(metaDataPermissionApplicationDto.getBusinessUnitId());
        if (businessUnit != null) {
            metaDataPermissionApplicationDto.setBusinessUnitName(businessUnit.getName());
        }
        DatasourceEntity datasourceEntity =
                datasourceDao.getDatasourceById(metaDataPermissionApplicationDto.getDbResourceId());
        if (datasourceEntity != null)
            metaDataPermissionApplicationDto.setDbResourceName(datasourceEntity.getName());

        FndUsers applicant = userDao.getUserInfo(metaDataPermissionApplicationDto.getApplicationUser());
        if (applicant != null) {
            metaDataPermissionApplicationDto.setApplicationUser(applicant.getUsername());
            if (StringUtils.isNotBlank(applicant.getAvatar())) {
                metaDataPermissionApplicationDto.setApplicationUserAvatarUrl(
                        attachmentProperties.getDisplayPrefixUrl() + applicant.getAvatar());
            }
        }

        FndUsers director = userDao.getUserInfo(metaDataPermissionApplicationDto.getDirector());
        if (director != null) {
            metaDataPermissionApplicationDto.setDirectorName(director.getUsername());
            if (StringUtils.isNotBlank(director.getAvatar())) {
                metaDataPermissionApplicationDto.setApplicationUserAvatarUrl(
                        attachmentProperties.getDisplayPrefixUrl() + director.getAvatar());
            }
        }

        // 审批人逻辑 ：注意 当审批状态是未审核时，审批人展示表负责人和存储空间管理员；当审批状态是通过或不通过时，审批人展示实际发生审批操作的账号。
        if (metaDataPermissionApplicationDto.getApprovalStatus()
                        == MetaDataPermissionApplicationStatus.APPROVED
                || metaDataPermissionApplicationDto.getApprovalStatus()
                        == MetaDataPermissionApplicationStatus.DISAPPROVE) {

            FndUsers auditor = userDao.getUserInfo(metaDataPermissionApplicationDto.getApprovalUser());
            if (auditor != null) {
                MetaDataPermissionApplicationDto.ApprovalUser approvalUser =
                        new MetaDataPermissionApplicationDto.ApprovalUser(auditor.getUsername(), null);
                if (StringUtils.isNotBlank(auditor.getAvatar())) {
                    approvalUser.setApprovalUserAvatarUrl(
                            attachmentProperties.getDisplayPrefixUrl() + auditor.getAvatar());
                }
                metaDataPermissionApplicationDto.setApprovalUsers(Collections.singletonList(approvalUser));
            }

        } else {

            Set<String> auditorId = new HashSet<>(2);
            auditorId.add(metaDataPermissionApplicationDto.getDirector());
            if (businessUnit != null) {
                auditorId.add(businessUnit.getAdminId());
            }

            metaDataPermissionApplicationDto.setApprovalUsers(
                    userDao.queryUserById(new ArrayList<>(auditorId)).stream()
                            .map(
                                    user -> {
                                        MetaDataPermissionApplicationDto.ApprovalUser approvalUser =
                                                new MetaDataPermissionApplicationDto.ApprovalUser(user.getUsername(), null);
                                        if (StringUtils.isNotBlank(user.getAvatar())) {
                                            approvalUser.setApprovalUserAvatarUrl(
                                                    attachmentProperties.getDisplayPrefixUrl() + user.getAvatar());
                                        }
                                        return approvalUser;
                                    })
                            .collect(Collectors.toList()));
        }

        metaDataPermissionApplicationDto.setApprovalStatusMeaning(
                i18nService.getMessage(application.getApprovalStatus().name()));

        return metaDataPermissionApplicationDto;
    }

    /** 当前账户的申请记录 */
    public PageRespHelper<MetaDataPermissionApplicationDto> pageQueryCurrentUserApplication(
            MetaDataPermissionApplicationQueryDto metaDataPermissionApplicationQueryDto) {

        metaDataPermissionApplicationQueryDto.setApplicationUser(MpaasSession.getCurrentUser());
        PageQueryResult<MetaDataPermissionApplication> result =
                metaDataPermissionApplicationDao.pageQueryCurrentUserApplication(
                        metaDataPermissionApplicationQueryDto);
        return pageQueryApplication(result);
    }

    /**
     * 分页查询待审批
     *
     * @param metaDataPermissionApplicationQueryDto 批量查询审批记录
     */
    public PageRespHelper<MetaDataPermissionApplicationDto> pageQueryApprovalUserApplication(
            MetaDataPermissionApplicationQueryDto metaDataPermissionApplicationQueryDto) {

        List<String> businessUnitIdList =
                businessUnitDao.queryBusinessUnitByUserId(MpaasSession.getCurrentUser()).stream()
                        .map(BusinessUnit::getId)
                        .collect(Collectors.toList());
        metaDataPermissionApplicationQueryDto.setApprovalStatus(
                MetaDataPermissionApplicationStatus.UNREVIEWED);

        // 避免为空时条件不存在
        if (businessUnitIdList.size() == 0) {
            businessUnitIdList = Collections.singletonList("false");
        }

        PageQueryResult<MetaDataPermissionApplication> result =
                metaDataPermissionApplicationDao.pageQueryManageApplication(
                        metaDataPermissionApplicationQueryDto, businessUnitIdList);

        return pageQueryApplication(result);
    }

    /**
     * 分页查询审批结束
     *
     * @param metaDataPermissionApplicationQueryDto 查询参数
     */
    public PageRespHelper<MetaDataPermissionApplicationDto> pageQueryApprovalCompleteApplication(
            MetaDataPermissionApplicationQueryDto metaDataPermissionApplicationQueryDto) {

        List<String> businessUnitIdList =
                businessUnitDao.queryBusinessUnitByUserId(MpaasSession.getCurrentUser()).stream()
                        .map(BusinessUnit::getId)
                        .collect(Collectors.toList());

        // 避免为空时条件不存在
        if (businessUnitIdList.size() == 0) {
            businessUnitIdList = Collections.singletonList("false");
        }

        PageQueryResult<MetaDataPermissionApplication> result =
                metaDataPermissionApplicationDao.pageQueryApprovalCompleteApplication(
                        metaDataPermissionApplicationQueryDto, businessUnitIdList);

        return pageQueryApplication(result);
    }

    /** 分页查询权限申请记录 */
    public PageRespHelper<MetaDataPermissionApplicationDto> pageQueryApplication(
            PageQueryResult<MetaDataPermissionApplication> pageQueryResult) {
        List<MetaDataPermissionApplication> metaDataPermissionApplicationList =
                pageQueryResult.getResult();
        List<MetaDataPermissionApplicationDto> metaDataPermissionApplicationDtoList = new ArrayList<>();

        metaDataPermissionApplicationList.forEach(
                metaDataPermissionApplication -> {
                    MetaDataPermissionApplicationDto metaDataPermissionApplicationDto =
                            new MetaDataPermissionApplicationDto();
                    BeanUtils.copyProperties(metaDataPermissionApplication, metaDataPermissionApplicationDto);

                    metaDataPermissionApplicationDto.setTableName(
                            metaDataPermissionApplicationDetailDao
                                    .queryMetaDataPermissionApplication(metaDataPermissionApplication.getId())
                                    .stream()
                                    .map(MetaDataPermissionApplicationDetail::getName)
                                    .collect(Collectors.joining(",")));

                    metaDataPermissionApplicationDto.setApprovalStatusMeaning(
                            i18nService.getMessage(metaDataPermissionApplication.getApprovalStatus().name()));

                    metaDataPermissionApplicationDtoList.add(metaDataPermissionApplicationDto);
                });

        // 翻译表相关
        metaDataMapService.interpretField(metaDataPermissionApplicationDtoList);
        metaDataMapService.assignLabelsInfo(metaDataPermissionApplicationDtoList);
        interpretField(metaDataPermissionApplicationDtoList);
        PageRespHelper<MetaDataPermissionApplicationDto> pageRespHelper = new PageRespHelper<>();
        pageRespHelper.setTotal(pageQueryResult.getCount().intValue());
        pageRespHelper.setTable(metaDataPermissionApplicationDtoList);
        return pageRespHelper;
    }

    /**
     * 审计列表
     *
     * @param metaDataPermissionQueryDto 审计列表查询参数
     */
    public PageRespHelper<MetaDataPermissionTableDto> auditPermissionList(
            MetaDataPermissionQueryDto metaDataPermissionQueryDto) {

        String userId = MpaasSession.getCurrentUser();

        List<String> businessUnitIds =
                businessUnitDao.queryBusinessUnitByUserId(userId).stream()
                        .map(BusinessUnit::getId)
                        .collect(Collectors.toList());
        metaDataPermissionQueryDto.setBusinessUnitIds(businessUnitIds);

        PageQueryResult<MetaDataPermission> metaDataPermissionPageQueryResult =
                dataAccessPermissionDao.pageQueryMetaDataPermission(metaDataPermissionQueryDto);
        List<MetaDataPermissionTableDto> metaDataPermissions =
                metaDataPermissionPageQueryResult.getResult().stream()
                        .map(
                                permission -> {
                                    MetaDataPermissionTableDto permissionDto = new MetaDataPermissionTableDto();
                                    BeanUtils.copyProperties(permission, permissionDto);
                                    return permissionDto;
                                })
                        .collect(Collectors.toList());

        Map<String, DehoopMetaDataDto> dehoopTableMap =
                metaDataMapTableDao
                        .getDehoopTableByGuids(
                                metaDataPermissions.stream()
                                        .map(MetaDataPermissionTableDto::getGuid)
                                        .collect(Collectors.toList()))
                        .stream()
                        .collect(Collectors.toMap(DehoopMetaDataDto::getGuid, e -> e));

        for (MetaDataPermissionTableDto metaDataPermission : metaDataPermissions) {
            DehoopMetaDataDto dehoopTable = dehoopTableMap.get(metaDataPermission.getGuid());
            if (dehoopTable != null) {
                BeanUtils.copyProperties(dehoopTable, metaDataPermission);
                metaDataPermission.setDescr(dehoopTable.getDescr());
            }
        }

        // 翻译
        metaDataMapService.interpretField(metaDataPermissions);
        metaDataMapService.assignLabelsInfo(metaDataPermissions);

        PageRespHelper<MetaDataPermissionTableDto> pageRespHelper = new PageRespHelper<>();
        pageRespHelper.setTable(metaDataPermissions);
        pageRespHelper.setTotal(metaDataPermissionPageQueryResult.getCount().intValue());
        return pageRespHelper;
    }

    /**
     * 审计记录 添加权限管理：当前用户是存储空间管理员或者有表负责人权限
     *
     * @param query 页面查询条件查询
     * @param response http返回对象
     * @return
     */
    public PageRespHelper<MetaDataPermissionTableDto> auditPermissionList(
            MetaDataPermissionQueryDto query, HttpServletResponse response) {

        List<BusinessUnit> businesses =
                businessUnitDao.queryBusinessUnitByUserId(MpaasSession.getCurrentUser());
        long hiveTables =
                metaDataMapTableDao.countDehoopHiveTableByDirector(MpaasSession.getCurrentUser());
        long dorisTables =
                metaDataMapTableDao.countDehoopDorisTableByDirector(MpaasSession.getCurrentUser());

        if (businesses.size() == 0 && hiveTables == 0 && dorisTables == 0) {

            throw new XDapBizException(MetaDataExceptionEnum.CAN_NOT_ACCESS);
        }

        return auditPermissionList(query);
    }

    /** 查询审计表详细 */
    public List<MetaDataPermissionDto> getPermissionDetail(String guid) {

        Assert.isNotBlank(guid, CommonBizExceptionEnum.XDAP_BIZ_EMPTY_VALUE);

        List<MetaDataPermissionDto> metaDataPermissionDtoList = new ArrayList<>();

        // 添加明细
        Map<String, List<MetaDataPermission>> dataPermissionMap =
                dataAccessPermissionDao.queryMetadataPermissionTable(guid).stream()
                        .collect(Collectors.groupingBy(MetaDataPermission::getGuid, Collectors.toList()));

        List<MetaDataPermission> metaDataPermissionList = dataPermissionMap.get(guid);
        if (metaDataPermissionList != null) {
            metaDataPermissionDtoList =
                    metaDataPermissionList.stream()
                            .filter(
                                    metaDataPermission ->
                                            !metaDataPermission.getDirector().equals(metaDataPermission.getUserId()))
                            .map(
                                    metaDataPermission ->
                                            MetaDataPermissionDto.builder()
                                                    .userId(metaDataPermission.getUserId())
                                                    .readPermission(metaDataPermission.getReadPermission())
                                                    .guid(metaDataPermission.getGuid())
                                                    .build())
                            .collect(Collectors.toList());
        }

        // 添加用户信息
        List<String> userIds =
                metaDataPermissionDtoList.stream()
                        .map(MetaDataPermissionDto::getUserId)
                        .collect(Collectors.toList());
        Map<String, FndUsers> userMap =
                userDao.queryUserByIdList(userIds).stream()
                        .collect(Collectors.toMap(FndUsers::getId, e -> e));

        metaDataPermissionDtoList.forEach(
                metaDataPermissionDto -> {
                    FndUsers user = userMap.get(metaDataPermissionDto.getUserId());
                    if (user != null) {
                        metaDataPermissionDto.setUserName(user.getUsername());
                        if (user.getAvatar() != null && !user.getAvatar().isEmpty()) {
                            metaDataPermissionDto.setUserAvatarUrl(
                                    attachmentProperties.getDisplayPrefixUrl() + user.getAvatar());
                        }
                    }
                });

        return metaDataPermissionDtoList;
    }

    /** 字段翻译 */
    public void interpretField(
            List<MetaDataPermissionApplicationDto> metaDataPermissionApplicationDos) {

        List<String> userIds = new ArrayList<>();

        metaDataPermissionApplicationDos.forEach(
                metaDataPermissionApplicationDto -> {
                    if (StringUtils.isNotBlank(metaDataPermissionApplicationDto.getApplicationUser())) {
                        userIds.add(metaDataPermissionApplicationDto.getApplicationUser());
                    }
                });

        Map<String, FndUsers> usersMap =
                userDao.queryUserByIdList(userIds).stream()
                        .collect(Collectors.toMap(FndUsers::getId, e -> e));

        for (MetaDataPermissionApplicationDto metaDataPermissionApplicationDto :
                metaDataPermissionApplicationDos) {

            // 翻译负责人
            String applicationUserId = metaDataPermissionApplicationDto.getApplicationUser();
            FndUsers applicationUser = usersMap.get(applicationUserId);
            if (applicationUser != null) {
                metaDataPermissionApplicationDto.setApplicationUserName(applicationUser.getUsername());
                if (applicationUser.getAvatar() != null) {
                    metaDataPermissionApplicationDto.setApplicationUserAvatarUrl(
                            attachmentProperties.getDisplayPrefixUrl() + applicationUser.getAvatar());
                }
            }
        }
    }
}

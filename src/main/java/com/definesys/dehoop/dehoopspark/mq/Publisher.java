package com.definesys.dehoop.dehoopspark.mq;

import org.springframework.data.redis.connection.RedisStreamCommands;
import org.springframework.data.redis.connection.stream.*;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class Publisher {

    private final RedisTemplate<String, Object> redisTemplate;

    public <T> void sendRecord(String streamKey, T data, Class<T> c) {

        ObjectRecord<String, String> record =
                StreamRecords.newRecord()
                        .in(streamKey)
                        .ofObject(JSON.toJSONString(data))
                        .withId(RecordId.autoGenerate());

        RecordId recordId = redisTemplate.opsForStream().add(record);

        log.info("返回的record-id:[{}]", recordId);
    }

    /** 发送对象 */
    public void sendRecord(String streamKey, String key, Object value) {

        log.info("发送信息:[{}：{}]", key, value);

        // redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Object.class));

        Map<String, Object> map = new HashMap<>();
        map.put(key, value);

        MapRecord<String, String, Object> mapRecord =
                StreamRecords.newRecord().in(streamKey).ofMap(map).withId(RecordId.autoGenerate());

        try {
            ByteRecord binaryRecord =
                    mapRecord.serialize(
                            RedisSerializer.string(), RedisSerializer.string(), RedisSerializer.json());
            redisTemplate.execute(
                    (RedisCallback<Object>)
                            connection ->
                                    connection.xAdd(binaryRecord, RedisStreamCommands.XAddOptions.maxlen(1000)));
        } catch (Exception e) {
            log.error("redis 数据推送失败！", e);
        }

        //        redisTemplate.opsForStream().add(mapRecord);

    }
}

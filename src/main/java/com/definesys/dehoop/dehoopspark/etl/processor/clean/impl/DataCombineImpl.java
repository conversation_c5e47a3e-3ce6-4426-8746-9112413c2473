package com.definesys.dehoop.dehoopspark.etl.processor.clean.impl;

import com.definesys.dehoop.dehoopspark.etl.dto.AddFieldDto;
import com.definesys.dehoop.dehoopspark.etl.dto.JobETLWorkNodeCleanFieldDto;
import com.definesys.dehoop.dehoopspark.etl.processor.clean.DataCleanExecute;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.api.java.UDF0;
import org.apache.spark.sql.api.java.UDF2;
import org.apache.spark.sql.functions;
import org.apache.spark.sql.types.DataTypes;

import java.util.List;

/** 多个字段合并 */
public class DataCombineImpl implements DataCleanExecute {

    private final SparkSession sparkSession;

    public DataCombineImpl(SparkSession sparkSession) {
        this.sparkSession = sparkSession;
    }

    @Override
    public Dataset<Row> clean(
            JobETLWorkNodeCleanFieldDto cleanFieldDto, Dataset<Row> previousNodeDataset) {
        // 新增列名
        String colName = cleanFieldDto.getCleanFilter().getColName();
        // 获取需要合并的列
        List<AddFieldDto> addFields = cleanFieldDto.getCleanFilter().getAddField();

        UDF0<String> NullColumn = () -> "";
        // 注册udf
        sparkSession.udf().register("NullColumn", NullColumn, DataTypes.StringType);
        // 添加新增列
        previousNodeDataset = previousNodeDataset.withColumn(colName, functions.callUDF("NullColumn"));

        for (AddFieldDto addField : addFields) {
            String connector = addField.getConnector();
            UDF2<String, String, String> dataCombine =
                    (s1, s2) -> (s1 == null ? "" : s1) + (s2 == null ? "" : s2) + connector;

            // 注册udf
            sparkSession.udf().register("dataCombine", dataCombine, DataTypes.StringType);

            previousNodeDataset =
                    previousNodeDataset.withColumn(
                            colName,
                            functions.callUDF(
                                    "dataCombine",
                                    previousNodeDataset.col(colName),
                                    previousNodeDataset.col(addField.getColName()).cast(DataTypes.StringType)));
        }
        return previousNodeDataset;
    }
}

package com.definesys.dehoop.dehoopspark.etl.processor.clean.impl;

import com.definesys.dehoop.dehoopspark.etl.dto.AddFieldDto;
import com.definesys.dehoop.dehoopspark.etl.dto.JobETLWorkNodeCleanFieldDto;
import com.definesys.dehoop.dehoopspark.etl.processor.clean.DataCleanExecute;

import org.apache.commons.lang.StringUtils;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.api.java.UDF0;
import org.apache.spark.sql.api.java.UDF2;
import org.apache.spark.sql.functions;
import org.apache.spark.sql.types.DataTypes;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/** 按前后分隔符提取列 */
public class ExtractDataImpl implements DataCleanExecute {

    private final SparkSession sparkSession;

    public ExtractDataImpl(SparkSession sparkSession) {
        this.sparkSession = sparkSession;
    }

    @Override
    public Dataset<Row> clean(
            JobETLWorkNodeCleanFieldDto cleanFieldDto, Dataset<Row> previousNodeDataset) {
        // 前后分隔符
        String startSplit = cleanFieldDto.getCleanFilter().getStartSplit();
        String endSplit = cleanFieldDto.getCleanFilter().getEndSplit();
        // 根据前后分隔符提取字段的正则表达式
        String regx = "(?<=[" + startSplit + "])[\\s\\S]*?(?=[" + endSplit + "])";
        Pattern pattern = Pattern.compile(regx);
        // 拆分列
        String colName = cleanFieldDto.getCleanFilter().getColName();
        // 新增列
        List<AddFieldDto> addFields = cleanFieldDto.getCleanFilter().getAddField();

        for (AddFieldDto addField : addFields) {
            UDF0<String> NullColumn = () -> null;
            // 注册udf
            sparkSession.udf().register("NullColumn", NullColumn, DataTypes.StringType);
            // 添加新增列
            previousNodeDataset =
                    previousNodeDataset.withColumn(addField.getColName(), functions.callUDF("NullColumn"));
        }

        for (int i = 0; i < addFields.size(); i++) {
            int index = i;
            UDF2<String, String, String> extractData =
                    (s1, s2) -> {
                        s2 = null;
                        // 保证处理字段的值不为空
                        if (StringUtils.isNotEmpty(s1)) {
                            Matcher matcher = pattern.matcher(s1);
                            for (int j = 0; j <= index; j++) {
                                // 需要匹配对应位置
                                if (j == index && matcher.find()) {
                                    try {

                                        s2 = matcher.group();
                                    } catch (Exception e) {
                                        s2 = null;
                                    }

                                } else {
                                    matcher.find();
                                }
                            }
                        }
                        return s2;
                    };
            // 注册udf
            sparkSession.udf().register("extractData", extractData, DataTypes.StringType);

            previousNodeDataset =
                    previousNodeDataset.withColumn(
                            addFields.get(i).getColName(),
                            functions.callUDF(
                                    "extractData",
                                    previousNodeDataset.col(colName).cast(DataTypes.StringType),
                                    previousNodeDataset
                                            .col(addFields.get(i).getColName())
                                            .cast(DataTypes.StringType)));
        }
        Boolean keepOriginalField = cleanFieldDto.getCleanFilter().getKeepOriginalField();
        if (keepOriginalField) {
            return previousNodeDataset;
        }
        return previousNodeDataset.drop(previousNodeDataset.col(colName));
    }
}

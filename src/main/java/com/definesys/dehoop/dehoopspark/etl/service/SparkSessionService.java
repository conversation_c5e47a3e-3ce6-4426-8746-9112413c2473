package com.definesys.dehoop.dehoopspark.etl.service;

import com.definesys.dehoop.dehoopspark.etl.dto.JobETLWorkNodeDto;
import com.definesys.dehoop.dehoopspark.service.SparkService;

import org.springframework.stereotype.Service;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年12月03日 16:41
 */
@Service
public class SparkSessionService {

    @Resource private SparkService sparkService;

    public SparkSession getSparkSession() {

        return sparkService.getSparkSession();
    }

    public void saveOutput(Dataset<Row> outTable, JobETLWorkNodeDto jobETLWorkNodeDto) {

        outTable
                .write()
                .option("timestampFormat", "yyyy/MM/dd HH:mm:ss ZZ")
                .format("Hive")
                .mode(SaveMode.Overwrite)
                .saveAsTable(jobETLWorkNodeDto.getDatabaseName() + "." + jobETLWorkNodeDto.getTableName());

        //        outTable.write()
        //                .format("jdbc")
        //
        // .option("url","*********************************************************************")
        //                .option("dbtable",jobETLWorkNodeDto.getTableName())
        //                .option("user","dehoop")
        //                .option("password","dehoop2021")
        //                .mode(SaveMode.Overwrite)
        //                .save();
        //
        //    }
    }
}

package com.definesys.dehoop.dehoopspark.etl.processor.clean.impl;

import com.definesys.dehoop.dehoopspark.etl.dto.JobETLWorkNodeCleanFieldDto;
import com.definesys.dehoop.dehoopspark.etl.processor.clean.DataCleanExecute;

import org.apache.spark.sql.*;
import org.apache.spark.sql.api.java.UDF2;
import org.apache.spark.sql.types.DataTypes;

/** 智能匹配 */
public class MatchDataImpl implements DataCleanExecute {

    private final SparkSession sparkSession;

    public MatchDataImpl(SparkSession sparkSession) {
        this.sparkSession = sparkSession;
    }

    @Override
    public Dataset<Row> clean(
            JobETLWorkNodeCleanFieldDto cleanFieldDto, Dataset<Row> previousNodeDataset) {
        String colName = cleanFieldDto.getCleanFilter().getMatchResult();

        UDF2<String, String, Boolean> matchField = String::equals;

        // 注册udf
        sparkSession.udf().register("matchField", matchField, DataTypes.BooleanType);

        previousNodeDataset =
                previousNodeDataset.withColumn(
                        colName,
                        functions.callUDF(
                                "matchField",
                                previousNodeDataset
                                        .col(cleanFieldDto.getCleanFilter().getColName())
                                        .cast(DataTypes.StringType),
                                previousNodeDataset
                                        .col(cleanFieldDto.getCleanFilter().getMatchFiled())
                                        .cast(DataTypes.StringType)));

        return previousNodeDataset;
    }
}

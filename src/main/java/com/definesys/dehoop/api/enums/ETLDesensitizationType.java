package com.definesys.dehoop.api.enums;

public enum ETLDesensitizationType {

    /*
     * 固定值替换
     */
    FIXED_REPLACE,

    /*
     * 前后缀增补
     */
    PREFIX_SUFFIX_ADDITION,

    /*
     * 数据模糊化
     */
    DATA_FUZZING,

    /*
     * 数据裁切
     */
    DATA_CROP,

    /*
     * MD5处理
     */
    MD5_PROCESSING,

    /*
     * 哈希处理
     */
    HASH_PROCESSING,

    /*
     * 随机填充
     */
    RANDOM_FILLING,

    /*
     * 偏移值加密
     */
    OFFSET_ENCRYPTION,

    SM2,

    SM3,

    SM4
}

package com.definesys.dehoop.api.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

@Data
@Component
@ConfigurationProperties(prefix = "dehoop.datasourcex")
public class DatasourcexProperties {

    /** datasourcex的pluginLibs目录 */
    private String pluginLibsPath;

    /** 单位秒，默认0s，以datasoucex为准 */
    private Integer connectTimeout = 0;
}

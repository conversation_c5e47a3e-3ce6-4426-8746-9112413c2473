package com.definesys.dehoop.api.config;

import com.definesys.dehoop.api.properties.SecurityProperties;
import com.definesys.dehoop.api.properties.TokenProperties;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties({SecurityProperties.class, TokenProperties.class})
public class ApiAutoConfiguration {}

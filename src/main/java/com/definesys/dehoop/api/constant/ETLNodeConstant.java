package com.definesys.dehoop.api.constant;

public interface ETLNodeConstant {

    String RELATION_FIELD = "RELATION_FIELD";

    String RELATION_CONDITION = "RELATION_CONDITION";

    /** 聚合组件 */
    String AGGREGATION_TRANSFORM = "AGGREGATION_TRANSFORM";

    /** 过滤组件 */
    String FILTER_TRANSFORM = "FILTER_TRANSFORM";

    /** 表达式组件 */
    String EXPRESSION_TRANSFORM = "EXPRESSION_TRANSFORM";

    /** 路由组件 */
    String ROUTER_PROCESS = "ROUTER_PROCESS";

    /** 关联组件 */
    String ASSOCIATION_PROCESS = "ASSOCIATION_PROCESS";

    String TABLE_INPUT = "TABLE_INPUT";

    String TABLE_OUTPUT = "TABLE_OUTPUT";

    /*
     * 清洗组件
     *
     * @ispong
     */
    String CLEAN_TRANSFORM = "CLEAN_TRANSFORM";

    /** 脱敏组件 */
    String DESENSITIZATION_TRANSFORM = "DESENSITIZATION_TRANSFORM";

    /** 排序组件 */
    String SORT_TRANSFORM = "SORT_TRANSFORM";

    /** 序号组件 */
    String SEQ_TRANSFORM = "SEQ_TRANSFORM";

    /** 唯一标识组件 */
    String UNIQUE_TRANSFORM = "UNIQUE_TRANSFORM";
}

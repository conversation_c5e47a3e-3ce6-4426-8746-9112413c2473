package com.definesys.dehoop.api.constant;

public interface MetadataTaskConstant {

    String TASK = "TASK";

    String FOLDER = "FOLDER";

    String DATASOURCE = "DATASOURCE";

    String CALENGINE = "CALENGINE";

    /** 待运行 */
    String PENDING = "PENDING";

    /** 进行中 */
    String DOING = "DOING";

    /** 成功 */
    String SUCCESS = "SUCCESS";

    /** 失败 */
    String ERROR = "ERROR";

    /** 所有的 */
    String ALL = "ALL";

    /** 自定义 */
    String CUSTOM = "CUSTOM";

    /** 需要删除 */
    String DELETE = "DELETE";

    /** 排除 */
    String IGNORE = "IGNORE";

    /** 仅更新 */
    String ONLY_UPDATE = "ONLY_UPDATE";

    /** 新增及更新 */
    String ADD_UPDATE = "ADD_UPDATE";

    /** 仅新增 */
    String ONLY_ADD = "ONLY_ADD";

    /** 忽略新增以及更新 */
    String IGNORE_ADD_UPDATE = "IGNORE_ADD_UPDATE";

    /** 忽略删除 */
    String IGNORE_DELETE = "IGNORE_DELETE";

    String METADATA_COLLECT = "METADATA_COLLECT";

    /** 手动执行方式 */
    String MANUAL = "MANUAL";

    String SCHEDULE = "SCHEDULE";
}

package com.definesys.dehoop.api.exception;

import com.xdap.motor.exception.BaseExceptionEnumInterface;

public enum UserExceptionEnum implements BaseExceptionEnumInterface {
    INVALID_USER("50000", "非法用户"),

    USER_IS_NOT_EXIST("50001", "用户不存在"),

    USER_IS_EXIST("50002", "用户已存在"),

    USER_IS_DISABLE("50003", "该用户已被禁用，请联系系统管理员"),

    USER_IS_EXPIRED("50004", "该用户不在有效期内，请联系系统管理员"),

    ACCESS_TOKEN_ERROR("50005", "获取access token异常"),

    SSO_ERROR("50006", "单点登录异常"),

    USER_PHONE_DUPLICATE("50007", "用户手机重复"),

    NOT_ADMIN_USER("50008", "不是管理员用户"),

    USER_EMAIL_DUPLICATE("50009", "用户邮箱重复"),

    USER_PHONE_NONSTANDARD("50010", "用户手机格式不规范"),

    USER_EMAIL_NONSTANDARD("50011", "用户邮箱格式不规范"),

    USER_START_DATE_AFTER_END_DATE("50012", "用户账号生效日期晚于失效日期"),

    USER_ACCOUNT_IS_OCCUPIED("50013", "该账号已注册"),

    USER_PHONE_IS_OCCUPIED("50014", "该手机号已注册"),

    USER_EMAIL_IS_OCCUPIED("50015", "该邮箱已注册"),

    ACCOUNT_IS_DISABLED_IN_PLATFORM("50016", "该用户已被平台禁用，请联系平台管理员"),

    SELF_DEVELOP_INTERFACE_LOGIN_ERROR("50017", "自开发接口登录异常"),

    PASSWORD_FREE_LOGIN_ERROR("50018", "免密登录异常"),

    ACCOUNT_PHONE_EMAIL_DUPLICATE("50019", "该账号/手机号/邮箱已注册"),

    USER_IS_ALREADY_IN_TENANT("50020", "用户已在租户内");

    private final String code;

    private final String message;

    UserExceptionEnum(String code, String message) {

        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {

        return this.code;
    }

    @Override
    public String getMessage() {

        return this.message;
    }
}

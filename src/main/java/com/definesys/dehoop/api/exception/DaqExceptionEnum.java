package com.definesys.dehoop.api.exception;

import com.xdap.motor.exception.BaseExceptionEnumInterface;

/**
 * 数据质量异常
 *
 * <AUTHOR>
 * @since 0.0.2
 */
public enum DaqExceptionEnum implements BaseExceptionEnumInterface {
    DATALAYER_NAME_IS_DUPLICATE("31001", "数据分层名称重复"),

    DATALAYER_IS_NOT_EXIST("31002", "数据分层不存在"),

    TABLE_INSPECTOR_NAME_IS_DUPLICATE("31003", "检查器名称重复"),

    TABLE_INSPECTOR_IS_NOT_EXIST("31004", "检查器不存在"),

    TABLE_NAME_LENGTH_INVALID("31005", "表名超出最大长度"),

    TABLE_NAME_EXPRESSION_INVALID("31006", "表名正则表达式不符合"),

    TABLE_NAME_KEY_WORDS_INVALID("31007", "表名存在关键字"),

    DATA_LAYER_HAS_TABLE("31008", "该数据分层中存在表"),

    CAN_NOT_MOVE_OUT_DATA_LAYER("31009", "不可移出该数据分层目录"),

    DIR_IS_DATA_LAYER("31010", "数据分层类型文件夹无法删除"),

    DATA_LAYER_HAS_WORK("31011", "数据分层中存在未删除作业"),
    ;

    private final String code;

    private final String message;

    DaqExceptionEnum(String code, String message) {

        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}

package com.definesys.dehoop.api.exception;

import com.xdap.motor.exception.BaseExceptionEnumInterface;

public enum RoleExceptionEnum implements BaseExceptionEnumInterface {
    ROLE_NAME_IS_DUPLICATE("12000", "角色名称重复"),

    ROLE_IS_NOT_EXIST("12001", "角色不存在"),

    ROLE_IS_SYSTEM_ROLE("12002", "该角色为默认角色，无法进行此操作"),

    ROLE_USER_HAS_EXIST("12003", "用户已存在"),

    ROLE_CODE_IS_DUPLICATE("12004", "用户编码重复"),

    SYSTEM_ROLE_IS_NOT_EXIST("12005", "租户管理员角色不存在");

    private final String code;

    private final String message;

    RoleExceptionEnum(String code, String message) {

        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {

        return this.code;
    }

    @Override
    public String getMessage() {

        return this.message;
    }
}

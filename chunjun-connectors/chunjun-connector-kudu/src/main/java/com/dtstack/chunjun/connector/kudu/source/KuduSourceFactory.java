/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.kudu.source;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.connector.kudu.conf.KuduSourceConf;
import com.dtstack.chunjun.connector.kudu.converter.KuduColumnConverter;
import com.dtstack.chunjun.connector.kudu.converter.KuduRawTypeConverter;
import com.dtstack.chunjun.converter.RawTypeConverter;
import com.dtstack.chunjun.source.SourceFactory;
import com.dtstack.chunjun.util.JsonUtil;
import com.dtstack.chunjun.util.TableUtil;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/6/9 星期三
 */
public class KuduSourceFactory extends SourceFactory {

    private final KuduSourceConf sourceConf;

    public KuduSourceFactory(SyncConf syncConf, StreamExecutionEnvironment env) {
        super(syncConf, env);

        sourceConf =
                JsonUtil.toObject(
                        JsonUtil.toJson(syncConf.getReader().getParameter()), KuduSourceConf.class);
        sourceConf.setColumn(syncConf.getReader().getFieldList());
        sourceConf.setKerberos(sourceConf.conventHadoopConfig());
        super.initCommonConf(sourceConf);
    }

    @Override
    public RawTypeConverter getRawTypeConverter() {
        return KuduRawTypeConverter::apply;
    }

    @Override
    public DataStream<RowData> createSource() {
        KuduInputFormatBuilder builder = new KuduInputFormatBuilder();

        builder.setKuduSourceConf(sourceConf);

        final RowType rowType = TableUtil.createRowType(sourceConf.getColumn(), getRawTypeConverter());

        List<FieldConf> fieldConfList = sourceConf.getColumn();
        List<String> columnNameList = new ArrayList<>();
        fieldConfList.forEach(fieldConf -> columnNameList.add(fieldConf.getName()));

        builder.setRowConverter(
                new KuduColumnConverter(rowType, columnNameList), useAbstractBaseColumn);

        return createInput(builder.finish());
    }
}

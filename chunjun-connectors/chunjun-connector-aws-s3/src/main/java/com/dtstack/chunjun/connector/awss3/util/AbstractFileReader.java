/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.awss3.util;

import com.dtstack.chunjun.connector.awss3.conf.AwsS3Conf;

import org.apache.commons.compress.compressors.bzip2.BZip2CompressorInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.Locale;

public abstract class AbstractFileReader implements FileReadClient {

    private S3SimpleObject file;

    @Override
    public void open(S3SimpleObject file, InputStream inputStream, AwsS3Conf awsS3Conf)
            throws IOException {
        this.file = file;
        if (StringUtils.isNotBlank(awsS3Conf.getCompressType())) {
            InputStream compressStream = getCompressStream(inputStream, awsS3Conf.getCompressType());
            open(compressStream, awsS3Conf);
        } else {
            open(inputStream, awsS3Conf);
        }
    }

    public abstract void open(InputStream inputStream, AwsS3Conf awsS3Conf) throws IOException;

    public InputStream getCompressStream(InputStream inputStream, String compressType)
            throws IOException {
        switch (compressType.toUpperCase(Locale.ENGLISH)) {
            case "ZIP":
                ZipInputStream zipInputStream = new ZipInputStream(inputStream);
                zipInputStream.addFileName(file.getFileName());
                return zipInputStream;
            case "BZIP2":
                return new BZip2CompressorInputStream(inputStream);
            case "GZIP":
                return new GzipCompressorInputStream(inputStream);
            default:
                throw new RuntimeException("not support " + compressType);
        }
    }

    public String getFileName() {
        return file.getFileName();
    }
}

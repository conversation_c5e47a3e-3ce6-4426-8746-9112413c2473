/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.ftp;

import com.dtstack.dtcenter.loader.exception.DtLoaderException;

import org.apache.commons.compress.compressors.bzip2.BZip2CompressorInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;

import com.google.common.collect.Lists;
import com.jcraft.jsch.ChannelSftp;
import org.jetbrains.annotations.NotNull;

/**
 * ftp 工具类
 *
 * <AUTHOR> date：Created in 下午4:11 2021/6/21 company: www.dtstack.com
 */
@Slf4j
public class FtpUtil {

    private static final Integer MAX_DFS = 1000;

    /** FTP 默认端口 */
    private static final Integer FTP_PORT_DEFAULT = 21;

    /** SFTP 默认端口 */
    private static final Integer SFTP_PORT_DEFAULT = 22;

    /**
     * 获取 ftp/sftp 端口
     *
     * @param protocol 协议类型
     * @param portStr String 类型端口
     * @return ftp/sftp 端口
     */
    public static Integer getFtpPort(String protocol, String portStr) {
        if (StringUtils.equalsIgnoreCase(ProtocolEnum.SFTP.name(), protocol)) {
            return StringUtils.isNotBlank(portStr) ? Integer.valueOf(portStr) : SFTP_PORT_DEFAULT;
        } else {
            return StringUtils.isNotBlank(portStr) ? Integer.valueOf(portStr) : FTP_PORT_DEFAULT;
        }
    }

    /**
     * 获取 SFTP 上的文件集合
     *
     * @param handler SFTP Client
     * @param path 地址
     * @param includeDir 是否包含文件夹
     * @param maxNum 最大条数
     * @param regexStr 正则匹配
     * @return 文件名集合
     */
    public static List<String> getSFTPFileNames(
            SFTPHandler handler,
            String path,
            Boolean includeDir,
            Boolean recursive,
            Integer maxNum,
            String regexStr) {
        List<String> fileNames = Lists.newArrayList();
        if (handler == null) {
            return fileNames;
        }
        // SFTP 文件夹队列
        LinkedList<String> dirQueue = Lists.newLinkedList();
        // 添加队列头信息
        dirQueue.add(path);
        try {
            int currentDfs = 0;
            while (!dirQueue.isEmpty()) {
                // 取出队列中的第一个元素
                String dirPath = dirQueue.removeFirst();
                Vector vector = handler.listFile(dirPath);
                for (Object single : vector) {
                    if (++currentDfs >= MAX_DFS) {
                        log.warn("Search more than {} files, stop looking for files", MAX_DFS);
                        if (fileNames.isEmpty()) {
                            throw new DtLoaderException(
                                    String.format(
                                            "The first %1$s files in the current folder do not match, please modify the matching rules",
                                            MAX_DFS));
                        }
                        return fileNames;
                    }
                    ChannelSftp.LsEntry lsEntry = (ChannelSftp.LsEntry) single;
                    if (lsEntry.getAttrs().isDir()
                            && !(lsEntry.getFilename().equals(".") || lsEntry.getFilename().equals(".."))) {
                        if (includeDir) {
                            if (fileNames.size() == maxNum) {
                                // 清空队列，退出循环
                                dirQueue.clear();
                                break;
                            }
                            listAddByRegex(fileNames, regexStr, lsEntry.getFilename(), dirPath);
                        }
                        if (recursive) {
                            // 如果循环则将文件路径添加到队列中
                            dirQueue.add(dirPath + "/" + lsEntry.getFilename());
                        }
                    } else if (!lsEntry.getAttrs().isDir()) {
                        if (fileNames.size() == maxNum) {
                            // 清空队列，退出循环
                            dirQueue.clear();
                            break;
                        }
                        listAddByRegex(fileNames, regexStr, lsEntry.getFilename(), dirPath);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return fileNames;
    }

    /**
     * 获取 FTP 上的文件集合
     *
     * @param ftpClient FTP client
     * @param path 地址
     * @param includeDir 是否包含文件夹
     * @param maxNum 最大条数
     * @param regexStr 正则匹配
     * @return 文件名集合
     */
    public static List<String> getFTPFileNames(
            FTPClient ftpClient,
            String path,
            Boolean includeDir,
            Boolean recursive,
            Integer maxNum,
            String regexStr) {
        List<String> fileNames = Lists.newArrayList();
        if (ftpClient == null) {
            return fileNames;
        }
        // 开启服务器对UTF-8的支持，如果服务器支持就用UTF-8编码
        try {
            if (FTPReply.isPositiveCompletion(ftpClient.sendCommand("OPTS UTF8", "ON"))) {
                ftpClient.setControlEncoding("UTF-8");
            }
        } catch (IOException e) {
            log.error("设置utf-8编码异常:{}", e.getMessage());
        }
        // SFTP 文件夹队列
        LinkedList<String> dirQueue = Lists.newLinkedList();
        // 添加队列头信息
        dirQueue.add(path);
        try {
            int currentDfs = 0;
            while (!dirQueue.isEmpty()) {
                // 取出队列中的第一个元素
                String dirPath = dirQueue.removeFirst();
                FTPFile[] ftpFiles = ftpClient.listFiles(dirPath);
                for (FTPFile file : ftpFiles) {
                    if (++currentDfs >= MAX_DFS) {
                        log.warn("Search more than {} files, stop looking for files", MAX_DFS);
                        if (fileNames.isEmpty()) {
                            throw new DtLoaderException(
                                    String.format(
                                            "The first %1$s files in the current folder do not match, please modify the matching rules",
                                            MAX_DFS));
                        }
                        return fileNames;
                    }
                    if (file.isDirectory() && !(file.getName().equals(".") || file.getName().equals(".."))) {
                        if (includeDir) {
                            if (fileNames.size() == maxNum) {
                                // 清空队列，退出循环
                                dirQueue.clear();
                                break;
                            }
                            listAddByRegex(fileNames, regexStr, file.getName(), dirPath);
                        }
                        if (recursive) {
                            // 如果循环则将文件路径添加到队列中
                            dirQueue.add(dirPath + "/" + file.getName());
                        }
                    } else if (!file.isDirectory()) {
                        if (fileNames.size() == maxNum) {
                            // 清空队列，退出循环
                            dirQueue.clear();
                            break;
                        }
                        listAddByRegex(fileNames, regexStr, file.getName(), dirPath);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return fileNames;
    }

    /**
     * 根据传入正则判断是否匹配，匹配则放入 list 中
     *
     * @param fileNames 文件名集合
     * @param regexStr 正则表达式
     * @param fileName 文件名
     * @param namePrefix 文件前缀（路径）
     */
    private static void listAddByRegex(
            List<String> fileNames, String regexStr, String fileName, String namePrefix) {
        if (StringUtils.isBlank(regexStr) || Pattern.compile(regexStr).matcher(fileName).matches()) {
            fileNames.add((namePrefix + "/" + fileName).replaceAll("//*", "/"));
        }
    }

    /**
     * 获取前十条数据
     *
     * @param ftpClient FTP client
     * @param sftpHandler SFTP handler
     * @param dirPath 文件路径
     * @param dirType 文本格式
     * @param encode 编码格式
     * @param nullValue 空值
     * @param newLineChar 换行符
     * @param newColumnChar 列分隔符
     * @param jsonType json类型
     * @param recordNode 记录节点
     * @param compressType
     * @return 数据预览结果
     */
    public static List<List<String>> getPreview(
            FTPClient ftpClient,
            SFTPHandler sftpHandler,
            List<DirPath> dirPath,
            String dirType,
            String encode,
            String nullValue,
            String newLineChar,
            String newColumnChar,
            String jsonType,
            String recordNode,
            Integer previewNum,
            Boolean withTableHeader,
            String compressType)
            throws Exception {

        List<List<String>> previewData = new ArrayList<>();
        if (ftpClient == null && sftpHandler == null) {
            return previewData;
        }
        Set<String> jsonSchema = new HashSet<>();
        int lineSize = -1;
        int columnSize = -1;

        // 获取当前系统换行符
        String lineSeparator =
                (String)
                        java.security.AccessController.doPrivileged(
                                new sun.security.action.GetPropertyAction("line.separator"));

        for (int i = 0; i < dirPath.size(); i++) {

            StringBuilder datas = new StringBuilder();
            InputStream inputStream = null;
            if (Objects.nonNull(ftpClient)) {
                inputStream = ftpClient.retrieveFileStream(dirPath.get(i).getFilePath());
            }
            if (Objects.nonNull(sftpHandler)) {
                inputStream = sftpHandler.getSFTPPreview(dirPath.get(i).getFilePath());
            }

            BufferedReader bufferedReader =
                    getBufferedReader(encode, dirPath.get(i).getFileName(), compressType, inputStream);

            try {

                String data = bufferedReader.readLine();
                while (data != null) {
                    if (data.length() == 0) {
                        data = bufferedReader.readLine();
                        continue;
                    }
                    datas.append(data);
                    if (!dirType.equalsIgnoreCase("JSON")) {
                        datas.append(lineSeparator);
                    }
                    data = bufferedReader.readLine();
                }

                // 读取到空文件时直接读下一个文件
                if (datas.length() == 0) {
                    continue;
                }

                if (dirType.equalsIgnoreCase("JSON")) {
                    if (jsonType.equalsIgnoreCase("ARRAY")) {

                        JSONArray jsonArray = JSON.parseArray(datas.toString());

                        for (int j = 0; j < jsonArray.size(); j++) {
                            List<String> columns = new ArrayList<>();
                            JSONObject jsonObject = jsonArray.getJSONObject(j);
                            // 获取表头
                            if (jsonSchema.isEmpty()) {
                                jsonSchema = jsonObject.keySet();
                            }
                            // 根据表头获取值
                            for (String key : jsonSchema) {
                                columns.add(jsonObject.get(key) == null ? null : jsonObject.get(key).toString());
                            }
                            previewData.add(columns);
                            if (previewData.size() == previewNum) {
                                break;
                            }
                        }

                    } else if (jsonType.equalsIgnoreCase("OBJECT")) {
                        List<String> columns = new ArrayList<>();
                        if (recordNode == null) {
                            JSONObject jsonObject = JSON.parseObject(datas.toString());
                            if (jsonSchema.isEmpty()) {
                                jsonSchema = jsonObject.keySet();
                            }
                            for (String key : jsonSchema) {
                                columns.add(jsonObject.get(key) == null ? null : jsonObject.get(key).toString());
                            }
                            previewData.add(columns);
                            if (previewData.size() == previewNum) {
                                break;
                            }
                        } else {
                            // 获取根结点
                            String finalJson = JSONPath.read(datas.toString(), "$." + recordNode).toString();

                            // 判断根结点类型
                            if (finalJson.startsWith("[")) {
                                JSONArray jsonArray = JSON.parseArray(finalJson);
                                for (int j = 0; j < jsonArray.size(); j++) {
                                    columns = new ArrayList<>();
                                    JSONObject arrayObject = jsonArray.getJSONObject(j);
                                    if (jsonSchema.isEmpty()) {
                                        jsonSchema = arrayObject.keySet();
                                    }
                                    for (String key : jsonSchema) {
                                        columns.add(
                                                arrayObject.get(key) == null ? null : arrayObject.get(key).toString());
                                    }
                                    previewData.add(columns);
                                    if (previewData.size() == previewNum) {
                                        break;
                                    }
                                }
                            } else {
                                JSONObject jsonObject = JSON.parseObject(finalJson);
                                if (jsonSchema.isEmpty()) {
                                    jsonSchema = jsonObject.keySet();
                                }
                                for (String key : jsonSchema) {
                                    columns.add(jsonObject.get(key) == null ? null : jsonObject.get(key).toString());
                                }
                                previewData.add(columns);
                                if (previewData.size() == previewNum) {
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    List<String> lines = new ArrayList<>();
                    Collections.addAll(lines, datas.toString().trim().split(newLineChar));
                    lineSize = lines.size();
                    for (int j = 0; j < lineSize; j++) {
                        List<String> columns = new ArrayList<>();

                        Collections.addAll(columns, lines.get(j).split(newColumnChar, -1));
                        if (columnSize == -1 && j == 0) {
                            columnSize = columns.size();
                        }

                        // 是否包含表头
                        if (withTableHeader) {
                            // 跳过表头,不加入previewData
                            if (columnSize != -1 && j == 0) {
                                continue;
                            }
                        }

                        if (columns.size() > columnSize) {
                            columns = columns.subList(0, columnSize);
                        }

                        columns =
                                columns.stream()
                                        .map(column -> column.equals(nullValue) ? null : column)
                                        .collect(Collectors.toList());

                        while (columns.size() < columnSize) {
                            columns.add(null);
                        }

                        previewData.add(columns);
                        if (previewData.size() == previewNum) {
                            break;
                        }
                    }
                }
            } finally {
                IOUtils.closeQuietly(bufferedReader);
                IOUtils.closeQuietly(inputStream);
                if (Objects.nonNull(ftpClient)) {
                    ftpClient.completePendingCommand();
                }
            }
            if (previewData.size() == previewNum) {
                break;
            }
        }
        return previewData;
    }

    /**
     * 检查对应路径下的文件表头是否一致 *
     *
     * @param ftpClient FTP client
     * @param sftpHandler SFTP handler
     * @param dirPath 文件路径
     * @param dirType 文本格式
     * @param newLineChar 换行符
     * @param encode 编码格式
     * @param newColumnChar 列分隔符
     * @param jsonType json类型
     * @param recordNode 记录节点
     * @param compressType 压缩格式
     * @return 表头检测结果
     */
    public static Boolean confirmSchema(
            FTPClient ftpClient,
            SFTPHandler sftpHandler,
            List<DirPath> dirPath,
            String dirType,
            String encode,
            String newLineChar,
            String newColumnChar,
            String jsonType,
            String recordNode,
            Boolean withTableHeader,
            String compressType)
            throws Exception {

        if (ftpClient == null && sftpHandler == null) {
            return true;
        }

        Set<String> jsonSchema = new HashSet<>();
        List<String> lines = new ArrayList<>();
        List<String> schema = new ArrayList<>();

        // 获取当前系统换行符
        String lineSeparator =
                (String)
                        java.security.AccessController.doPrivileged(
                                new sun.security.action.GetPropertyAction("line.separator"));
        for (int i = 0; i < dirPath.size(); i++) {

            StringBuilder datas = new StringBuilder();
            InputStream inputStream = null;
            if (Objects.nonNull(ftpClient)) {
                inputStream = ftpClient.retrieveFileStream(dirPath.get(i).getFilePath());
            }
            if (Objects.nonNull(sftpHandler)) {
                inputStream = sftpHandler.getSFTPPreview(dirPath.get(i).getFilePath());
            }

            BufferedReader bufferedReader =
                    getBufferedReader(encode, dirPath.get(i).getFileName(), compressType, inputStream);

            try {

                String data = bufferedReader.readLine();
                while (data != null) {
                    if (data.length() == 0) {
                        data = bufferedReader.readLine();
                        continue;
                    }
                    datas.append(data);
                    if (!dirType.equalsIgnoreCase("JSON")) {
                        datas.append(lineSeparator);
                    }
                    data = bufferedReader.readLine();
                }

                // 读取到空文件时直接读下一个文件
                if (datas.length() == 0) {
                    continue;
                }

                if (dirType.equalsIgnoreCase("JSON")) {
                    if (jsonType.equalsIgnoreCase("ARRAY")) {
                        JSONArray jsonArray = JSON.parseArray(datas.toString());
                        if (jsonSchema.isEmpty()) {
                            jsonSchema = jsonArray.getJSONObject(0).keySet();
                        } else {
                            if (!jsonArray.getJSONObject(0).keySet().equals(jsonSchema)) {
                                return false;
                            }
                        }
                    } else if (jsonType.equalsIgnoreCase("OBJECT")) {
                        // 只有object类型的json才有记录节点
                        if (recordNode == null) {
                            JSONObject jsonObject = JSON.parseObject(datas.toString());
                            if (jsonSchema.isEmpty()) {
                                jsonSchema = jsonObject.keySet();
                            } else {
                                if (!jsonObject.keySet().equals(jsonSchema)) {
                                    return false;
                                }
                            }
                        } else {
                            // 获取根结点
                            String finalJson = JSONPath.read(datas.toString(), "$." + recordNode).toString();

                            // 判断根结点类型
                            if (finalJson.startsWith("[")) {
                                JSONArray jsonArray = JSON.parseArray(finalJson);
                                if (jsonSchema.isEmpty()) {
                                    jsonSchema = jsonArray.getJSONObject(0).keySet();
                                } else {
                                    if (!jsonArray.getJSONObject(0).keySet().equals(jsonSchema)) {
                                        return false;
                                    }
                                }
                            } else {
                                JSONObject jsonObject = JSON.parseObject(finalJson);
                                if (jsonSchema.isEmpty()) {
                                    jsonSchema = jsonObject.keySet();
                                } else {
                                    if (!jsonObject.keySet().equals(jsonSchema)) {
                                        return false;
                                    }
                                }
                            }
                        }
                    }
                } else {
                    lines = Arrays.asList(datas.toString().trim().split(newLineChar));
                    // 表头为空
                    if (schema.isEmpty()) {
                        schema = Arrays.asList(lines.get(0).split(newColumnChar, -1));
                        continue;
                    }

                    // 是否包含表头
                    if (withTableHeader) {
                        // 表头不一致
                        if (!schema.equals(Arrays.asList(lines.get(0).split(newColumnChar, -1)))) {
                            return false;
                        }
                    } else {
                        if (schema.size() != lines.get(0).split(newColumnChar, -1).length) {
                            return false;
                        }
                    }
                }
            } finally {
                IOUtils.closeQuietly(bufferedReader);
                IOUtils.closeQuietly(inputStream);
                if (Objects.nonNull(ftpClient)) {
                    ftpClient.completePendingCommand();
                }
            }
        }
        return true;
    }

    /**
     * 获取bufferReader *
     *
     * @param encode 编码格式
     * @param fileName zip压缩文件中目标文件名
     * @param compressType 压缩格式
     * @param inputStream 文件输入流
     * @return bufferReader
     */
    @NotNull
    private static BufferedReader getBufferedReader(
            String encode, String fileName, String compressType, InputStream inputStream)
            throws IOException {

        BufferedReader bufferedReader = null;

        if (StringUtils.isNotBlank(compressType)) {
            switch (compressType.toUpperCase(Locale.ENGLISH)) {
                case "ZIP":
                    // 对当前文件进行解析获取真正文件数量循环验证
                    ZipInputStream zipInputStream = new ZipInputStream(inputStream);
                    zipInputStream.addFileName(fileName);
                    bufferedReader = new BufferedReader(new InputStreamReader(zipInputStream, encode));
                    break;
                case "BZIP2":
                    BZip2CompressorInputStream bzip2InputStream = new BZip2CompressorInputStream(inputStream);
                    bufferedReader = new BufferedReader(new InputStreamReader(bzip2InputStream, encode));
                    break;
                case "GZIP":
                    GzipCompressorInputStream gzipInputStream = new GzipCompressorInputStream(inputStream);
                    bufferedReader = new BufferedReader(new InputStreamReader(gzipInputStream, encode));
                    break;
                default:
                    throw new DtLoaderException("not support compressType " + compressType);
            }
        }

        if (bufferedReader == null) {
            bufferedReader = new BufferedReader(new InputStreamReader(inputStream, encode));
        }
        return bufferedReader;
    }

    /**
     * 获取表头 *
     *
     * @param ftpClient FTP client
     * @param sftpHandler SFTP handler
     * @param dirPath 文件路径
     * @param dirType 文本格式
     * @param encode 编码格式
     * @param newLineChar 换行符
     * @param newColumnChar 列分隔符
     * @param jsonType json类型
     * @param recordNode 记录节点
     * @param compressType 压缩格式
     * @return 表头信息
     */
    public static List<String> getSchema(
            FTPClient ftpClient,
            SFTPHandler sftpHandler,
            List<DirPath> dirPath,
            String dirType,
            String encode,
            String newLineChar,
            String newColumnChar,
            String jsonType,
            String recordNode,
            String compressType)
            throws Exception {

        // 获取当前系统换行符
        String lineSeparator =
                (String)
                        java.security.AccessController.doPrivileged(
                                new sun.security.action.GetPropertyAction("line.separator"));
        for (int i = 0; i < dirPath.size(); i++) {

            StringBuilder datas = new StringBuilder();
            InputStream inputStream = null;
            if (Objects.nonNull(ftpClient)) {
                inputStream = ftpClient.retrieveFileStream(dirPath.get(i).getFilePath());
            }
            if (Objects.nonNull(sftpHandler)) {
                inputStream = sftpHandler.getSFTPPreview(dirPath.get(i).getFilePath());
            }

            BufferedReader bufferedReader =
                    getBufferedReader(encode, dirPath.get(i).getFileName(), compressType, inputStream);

            try {

                String data = bufferedReader.readLine();
                while (data != null) {
                    if (data.length() == 0) {
                        data = bufferedReader.readLine();
                        continue;
                    }
                    datas.append(data);
                    if (!dirType.equalsIgnoreCase("JSON")) {
                        datas.append(lineSeparator);
                    }
                    data = bufferedReader.readLine();
                }

                // 读取到空文件时直接读下一个文件
                if (datas.length() == 0) {
                    continue;
                }

                if (dirType.equalsIgnoreCase("JSON")) {
                    if (jsonType.equalsIgnoreCase("ARRAY")) {
                        JSONArray jsonArray = JSON.parseArray(datas.toString());
                        return new ArrayList<>(jsonArray.getJSONObject(0).keySet());
                    } else if (jsonType.equalsIgnoreCase("OBJECT")) {
                        // 只有object类型的json才有记录节点
                        if (recordNode == null) {
                            JSONObject jsonObject = JSON.parseObject(datas.toString());
                            return new ArrayList<>(jsonObject.keySet());
                        } else {
                            // 获取根结点
                            String finalJson = JSONPath.read(datas.toString(), "$." + recordNode).toString();

                            // 判断根结点类型
                            if (finalJson.startsWith("[")) {
                                JSONArray jsonArray = JSON.parseArray(finalJson);
                                return new ArrayList<>(jsonArray.getJSONObject(0).keySet());
                            } else {
                                JSONObject jsonObject = JSON.parseObject(finalJson);
                                return new ArrayList<>(jsonObject.keySet());
                            }
                        }
                    }
                } else {
                    List<String> lines = Arrays.asList(datas.toString().trim().split(newLineChar));
                    return Arrays.asList(lines.get(0).split(newColumnChar, -1));
                }

            } finally {
                IOUtils.closeQuietly(bufferedReader);
                IOUtils.closeQuietly(inputStream);
                if (Objects.nonNull(ftpClient)) {
                    ftpClient.completePendingCommand();
                }
            }
        }
        return null;
    }

    public static List<DirPath> getDecompressedPath(
            FTPClient ftpClient,
            SFTPHandler sftpHandler,
            List<String> dirPath,
            String encode,
            String compressType)
            throws Exception {

        List<DirPath> decompressedPath = new ArrayList<>();

        for (String path : dirPath) {
            switch (compressType.toUpperCase(Locale.ENGLISH)) {
                case "BZIP2":
                case "GZIP":
                    decompressedPath.add(new DirPath(path, null));
                    break;
                case "ZIP":
                    java.util.zip.ZipInputStream zipInputStream = null;
                    try {
                        if (Objects.nonNull(ftpClient)) {
                            zipInputStream =
                                    new java.util.zip.ZipInputStream(
                                            ftpClient.retrieveFileStream(path), Charset.forName(encode));
                        }
                        if (Objects.nonNull(sftpHandler)) {
                            zipInputStream =
                                    new java.util.zip.ZipInputStream(
                                            sftpHandler.getSFTPPreview(path), Charset.forName(encode));
                        }
                        ZipEntry zipEntry;
                        while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                            if (!zipEntry.isDirectory()) {
                                decompressedPath.add(new DirPath(path, zipEntry.getName()));
                            }
                        }
                    } finally {
                        IOUtils.closeQuietly(zipInputStream);
                        if (Objects.nonNull(ftpClient)) {
                            ftpClient.completePendingCommand();
                        }
                    }
                    break;
                default:
                    throw new DtLoaderException("not support compressType " + compressType);
            }
        }
        return decompressedPath;
    }
}

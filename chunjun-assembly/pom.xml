<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>1.12-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-assembly</artifactId>
	<packaging>pom</packaging>
	<name>ChunJun : Assembly</name>


	<properties>
		<skipTests>true</skipTests>
		<main.basedir>${project.parent.basedir}</main.basedir>
		<sbt.project.name>assembly</sbt.project.name>
		<build.testJarPhase>none</build.testJarPhase>
		<build.copyDependenciesPhase>package</build.copyDependenciesPhase>
	</properties>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>3.0.0</version>
				<executions>
					<execution>
						<id>dist</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>chunjun-dist</finalName>
							<descriptors>
								<descriptor>src/main/assembly/assembly.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	<profiles>
		<profile>
			<id>default</id>
		</profile>
	</profiles>
</project>

{"job": {"content": [{"reader": {"parameter": {"jdbcUrl": "***********************************", "username": "username", "password": "password", "supportAutoAddLog": false, "table": ["table.*"], "cat": "UPDATE,INSERT,DELETE", "pavingData": true, "readPosition": "CURRENT", "queryTimeout": 3000}, "name": "oraclelogminerreader"}, "writer": {"parameter": {"print": true}, "name": "streamwriter"}}], "setting": {"speed": {"bytes": 0, "channel": 1}}}}
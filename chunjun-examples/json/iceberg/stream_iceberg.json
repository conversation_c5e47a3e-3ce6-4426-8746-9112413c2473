{"job": {"content": [{"reader": {"parameter": {"column": [{"name": "id", "type": "id"}, {"name": "sell", "type": "long"}, {"name": "content", "type": "string"}], "sliceRecordCount": ["10"], "permitsPerSecond": 1}, "name": "streamreader"}, "writer": {"parameter": {"path": "/Users/<USER>/study/bigdata-iceberg/iceberg_warehouse/tb2", "writeMode": "upsert", "primaryKey": ["id"], "defaultFS": "file:///", "hadoopConfig": {"fs.hdfs.impl.disable.cache": "true"}}, "name": "icebergwriter"}}], "setting": {"errorLimit": {"record": 100}, "speed": {"bytes": 0, "channel": 1, "readerChannel": 1, "writerChannel": 1}}}}
{"job": {"content": [{"reader": {"name": "kud<PERSON><PERSON>", "parameter": {"column": [{"name": "REPORT_TIME", "type": "UNIXTIME_MICROS"}, {"name": "VEHICLE_TAG", "type": "STRING"}, {"name": "REV", "type": "INT32"}, {"name": "LONGITUDE", "type": "DOUBLE"}, {"name": "LATITUDE", "type": "DOUBLE"}, {"name": "SPEED", "type": "DOUBLE"}, {"name": "HEADING", "type": "DOUBLE"}, {"name": "TRAIN_ASSIGNMENT", "type": "STRING"}, {"name": "PREDICTABLE", "type": "INT32"}], "masters": "localhost:7051", "table": "sfmta_kudu", "readMode": "read_latest", "workerCount": 2, "operationTimeout": 300000, "adminOperationTimeout": 300000, "queryTimeout": 300000, "batchSizeBytes": 1048576}}, "writer": {"parameter": {"column": [{"name": "REPORT_TIME", "type": "UNIXTIME_MICROS"}, {"name": "VEHICLE_TAG", "type": "STRING"}, {"name": "REV", "type": "INT32"}, {"name": "LONGITUDE", "type": "DOUBLE"}, {"name": "LATITUDE", "type": "DOUBLE"}, {"name": "SPEED", "type": "DOUBLE"}, {"name": "HEADING", "type": "DOUBLE"}, {"name": "TRAIN_ASSIGNMENT", "type": "STRING"}, {"name": "PREDICTABLE", "type": "INT32"}], "masters": "localhost:7051", "table": "sfmta_kudu_two", "flushMode": "manual_flush", "writeMode": "append", "batchSizeBytes": 1048576}, "name": "kudu<PERSON>"}}], "setting": {"restore": {"maxRowNumForCheckpoint": 0, "isRestore": false, "restoreColumnName": "", "restoreColumnIndex": 0}, "errorLimit": {"record": 100}, "speed": {"bytes": 0, "channel": 1}, "log": {"isLogger": false, "level": "debug", "path": "", "pattern": ""}}}}
# 设置时区
TZ=Asia/Shanghai
# 设置网络模式
NETWORKS_DRIVER=bridge
# 宿主机上代码存放的目录路径
DATA_PATH_HOST=./data

# MYSQL57服务映射宿主机端口号，可在宿主机127.0.0.1:40057访问
MYSQL57_PORT=40057
MYSQL57_USERNAME=admin
MYSQL57_PASSWORD=123456
MYSQL57_ROOT_PASSWORD=123456

# MYSQL8服务映射宿主机端口号，可在宿主机127.0.0.1:40080访问
MYSQL8_PORT=40080
MYSQL8_USERNAME=admin
MYSQL8_PASSWORD=123456
MYSQL8_ROOT_PASSWORD=123456

# Redis5 服务映射宿主机端口号，可在宿主机127.0.0.1:46375访问
REDIS5_PORT=46375
REDIS5_PASSWORD=123456
# Redis6 服务映射宿主机端口号，可在宿主机127.0.0.1:46376访问
REDIS6_PORT=46376
REDIS6_PASSWORD=123456

# Dm7 服务映射宿主机端口号，可在宿主机127.0.0.1:45237访问
DM7_PORT=45237
# Dm8 服务映射宿主机端口号，可在宿主机127.0.0.1:45238访问
DM8_PORT=45238

# Zookeeper 服务映射宿主机端口号，可在宿主机127.0.0.1:42181访问
ZOOKEEPER_PORT=42181

# kafka28 服务映射宿主机端口号，可在宿主机127.0.0.1:49092访问
KAFKA28_PORT=49092

# kudu 服务映射宿主机端口号，可在宿主机127.0.0.1:49092访问
KUDU_MASTER_PORT=7051
KUDU_TSERVER_PORT=7050

package com.dtstack.chunjun.client;

import lombok.Data;

@Data
public class JobEvent {

    private String jobName;

    private String jobId;

    private String applicationId;

    private String status;

    private String eventType;

    public JobEvent(String status) {
        this.status = status;
    }

    public JobEvent(String applicationId, String eventType) {
        this.applicationId = applicationId;
        this.eventType = eventType;
    }

    public JobEvent(String applicationId, String jobId, String eventType) {
        this.applicationId = applicationId;
        this.jobId = jobId;
        this.eventType = eventType;
    }
}
